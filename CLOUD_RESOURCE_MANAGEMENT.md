# 云资源管理功能说明

## 📋 功能概述

云资源管理功能允许用户通过AMS系统统一管理多云环境下的资源，支持从金山云和火山云发现、导入和管理云资源。

### 🎯 核心特性

- **多云支持** - 支持金山云、火山云等主流云厂商
- **人工触发** - 避免自动同步带来的风险，由用户主动控制
- **资源发现** - 自动发现云端资源，支持多种过滤条件
- **选择性导入** - 用户可选择需要导入的资源
- **树形管理** - 导入的资源可挂载到现有的树形节点结构
- **状态同步** - 支持手动同步云资源状态
- **安全加密** - 云厂商密钥采用AES加密存储

### 🏗️ 架构设计

```
前端界面
    ↓
云资源管理API
    ↓
云厂商SDK适配层
    ↓
金山云/火山云 API
```

## 🚀 快速开始

### 1. 数据库迁移

首先执行数据库迁移脚本：

```bash
mysql -u username -p database_name < sql/cloud_resource_migration.sql
```

### 2. 配置云厂商

通过API或前端界面配置云厂商信息：

```bash
# 创建金山云配置
curl -X POST http://localhost:8080/api/ams-ce/cloud/configs \
  -H "Content-Type: application/json" \
  -H "X-User-Token: your_token" \
  -d '{
    "name": "生产环境-金山云",
    "provider": "kingsoft",
    "region": "cn-beijing-6",
    "access_key": "your_access_key",
    "secret_key": "your_secret_key",
    "description": "生产环境金山云配置"
  }'
```

### 3. 测试连接

```bash
# 测试云厂商连接
curl -X POST http://localhost:8080/api/ams-ce/cloud/configs/1/test \
  -H "X-User-Token: your_token"
```

### 4. 发现资源

```bash
# 发现云资源
curl -X POST http://localhost:8080/api/ams-ce/cloud/discover \
  -H "Content-Type: application/json" \
  -H "X-User-Token: your_token" \
  -d '{
    "config_id": 1,
    "resource_types": ["ecs", "rds"],
    "filters": {
      "zone": "cn-beijing-6a",
      "status": ["running"],
      "name_like": "prod"
    }
  }'
```

### 5. 查看发现的资源

```bash
# 获取发现的资源列表
curl -X GET "http://localhost:8080/api/ams-ce/cloud/discoveries/1/resources?limit=20&offset=0" \
  -H "X-User-Token: your_token"
```

### 6. 选择要导入的资源

```bash
# 选择资源
curl -X PUT http://localhost:8080/api/ams-ce/cloud/discoveries/1/resources/select \
  -H "Content-Type: application/json" \
  -H "X-User-Token: your_token" \
  -d '{
    "resource_ids": [1, 2, 3],
    "selected": true
  }'
```

### 7. 导入资源到节点

```bash
# 导入选中的资源
curl -X POST http://localhost:8080/api/ams-ce/cloud/discoveries/1/import \
  -H "Content-Type: application/json" \
  -H "X-User-Token: your_token" \
  -d '{
    "node_id": 123,
    "resource_mapping": {
      "1": {"cate": "server", "tenant": "prod", "note": "生产服务器"},
      "2": {"cate": "database", "tenant": "prod", "note": "生产数据库"}
    }
  }'
```

## 📚 API 文档

### 云厂商配置管理

| 方法 | 路径 | 说明 |
|------|------|------|
| GET | `/cloud/configs` | 获取云厂商配置列表 |
| POST | `/cloud/configs` | 创建云厂商配置 |
| GET | `/cloud/configs/:id` | 获取云厂商配置详情 |
| PUT | `/cloud/configs/:id` | 更新云厂商配置 |
| DELETE | `/cloud/configs/:id` | 删除云厂商配置 |
| POST | `/cloud/configs/:id/test` | 测试云厂商连接 |

### 资源发现管理

| 方法 | 路径 | 说明 |
|------|------|------|
| POST | `/cloud/discover` | 发现云资源 |
| GET | `/cloud/discoveries` | 获取发现记录列表 |
| GET | `/cloud/discoveries/:id` | 获取发现记录详情 |
| GET | `/cloud/discoveries/:id/resources` | 获取发现的资源列表 |
| PUT | `/cloud/discoveries/:id/resources/select` | 选择/取消选择资源 |
| POST | `/cloud/discoveries/:id/import` | 导入选中的资源 |

### 云资源管理

| 方法 | 路径 | 说明 |
|------|------|------|
| GET | `/cloud/resources` | 获取云资源列表 |
| POST | `/cloud/resources/sync` | 同步云资源状态 |
| DELETE | `/cloud/resources/:id/nodes/:node_id` | 解除云资源挂载 |

## 🔧 支持的云厂商和资源类型

### 金山云 (kingsoft)

- **ECS** - 云服务器
- **RDS** - 云数据库
- **Redis** - 云缓存
- **SLB** - 负载均衡

### 火山云 (volcano)

- **ECS** - 云服务器
- **RDS** - 云数据库
- **Redis** - 云缓存
- **CLB** - 负载均衡

### 阿里云 (aliyun)

- **ECS** - 云服务器
- **RDS** - 云数据库
- **Redis** - 云缓存
- **SLB** - 负载均衡
- **OSS** - 对象存储
- **VPC** - 专有网络

### 腾讯云 (tencent)

- **CVM** - 云服务器
- **CDB** - 云数据库
- **Redis** - 云缓存
- **CLB** - 负载均衡
- **COS** - 对象存储
- **VPC** - 私有网络

### 天翼云 (ctyun)

- **ECS** - 云服务器
- **RDS** - 云数据库
- **Redis** - 云缓存
- **ELB** - 负载均衡
- **OBS** - 对象存储
- **VPC** - 虚拟私有云

## 🛡️ 安全说明

### 密钥加密

- 云厂商的AccessKey和SecretKey采用AES-256-GCM加密存储
- 加密密钥配置在系统配置文件中，请妥善保管
- 建议定期轮换云厂商密钥

### 权限控制

- 云厂商配置管理需要管理员权限
- 资源发现和导入需要相应的操作权限
- 所有操作都有详细的审计日志

### 网络安全

- 建议使用HTTPS协议
- 云厂商API调用支持自定义端点
- 支持通过代理访问云厂商API

## 🔍 故障排查

### 常见问题

1. **连接测试失败**
   - 检查AccessKey和SecretKey是否正确
   - 检查网络连接是否正常
   - 检查云厂商API端点是否可达

2. **资源发现失败**
   - 检查云厂商账号权限是否足够
   - 检查地域配置是否正确
   - 查看错误日志获取详细信息

3. **资源导入失败**
   - 检查目标节点是否存在
   - 检查资源映射配置是否正确
   - 确认没有重复导入相同资源

### 日志查看

```bash
# 查看云资源相关日志
tail -f logs/ams.log | grep -i cloud

# 查看特定发现任务的日志
tail -f logs/ams.log | grep "discovery.*123"
```

## 📈 性能优化

### 大规模资源发现

- 建议分批发现资源，每批不超过1000个
- 使用过滤条件减少不必要的资源发现
- 在业务低峰期进行大规模发现操作

### 数据库优化

- 定期清理过期的发现记录和临时资源
- 为经常查询的字段添加索引
- 监控数据库性能指标

## 🔄 最佳实践

### 配置管理

1. **环境隔离** - 为不同环境创建独立的云厂商配置
2. **权限最小化** - 云厂商账号只授予必要的权限
3. **定期审计** - 定期检查和更新云厂商配置

### 资源管理

1. **标准化命名** - 使用统一的资源命名规范
2. **分类管理** - 合理设置资源分类和租户
3. **定期同步** - 定期同步云资源状态保持数据一致性

### 安全管理

1. **密钥轮换** - 定期轮换云厂商密钥
2. **访问控制** - 严格控制云资源管理权限
3. **审计日志** - 定期审查操作日志

## 🚧 开发说明

### 添加新的云厂商

1. 实现`CloudProvider`接口
2. 在`NewCloudProvider`函数中添加新厂商的创建逻辑
3. 更新配置文件和文档
4. 添加相应的测试用例

### 扩展资源类型

1. 在云厂商适配器中添加新的资源发现方法
2. 更新资源类型映射配置
3. 添加相应的前端界面支持
4. 更新API文档

## 📞 技术支持

如有问题或建议，请联系开发团队或提交Issue。
