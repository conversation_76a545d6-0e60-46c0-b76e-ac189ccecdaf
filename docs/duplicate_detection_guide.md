# 重复检测功能使用指南

## 📋 概述

重复检测功能用于识别和管理手动添加的设备与自动发现的设备之间的重复问题。该功能以IP地址为唯一标识符，当检测到重复时，会标记设备状态并提供用户决策选项。

## 🚀 快速开始

### 1. 数据库迁移

首先运行数据库迁移脚本：

```bash
mysql -u username -p database_name < sql/duplicate_detection_migration.sql
```

### 2. 配置重复检测

在配置文件中启用重复检测：

```yaml
# etc/cloud-config-example.yml
cloud:
  duplicate_detection:
    enabled: true
    ip_matching:
      prefer_private_ip: true
      fallback_to_public_ip: true
      ignore_empty_ip: true
    auto_rules:
      auto_ignore_same_source: false
      auto_override_manual_with_discovery: false
```

### 3. 启动服务

```bash
# 编译并启动AMS服务
go build -o arboris-ams src/modules/ams/ams.go
./arboris-ams
```

## 🔧 功能特性

### 核心功能

- ✅ **IP地址唯一标识**: 以私网IP优先，公网IP备用的方式识别设备
- ✅ **重复状态管理**: 自动标记重复设备状态
- ✅ **用户决策支持**: 提供覆盖、忽略等处理选项
- ✅ **批量操作**: 支持批量处理重复设备
- ✅ **详细对比**: 显示字段差异和冲突信息
- ✅ **历史记录**: 记录所有重复处理操作

### 支持的设备类型

- **Host**: 主机设备（host表）
- **Resource**: 资源设备（resource表）

## 📊 数据结构

### 重复状态枚举

```go
type DuplicateStatus string

const (
    DuplicateStatusNone       = "NONE"       // 无重复
    DuplicateStatusDetected   = "DETECTED"   // 检测到重复
    DuplicateStatusIgnored    = "IGNORED"    // 用户选择忽略
    DuplicateStatusOverridden = "OVERRIDDEN" // 用户选择覆盖
)
```

### 数据来源枚举

```go
type DataSource string

const (
    DataSourceManual    = "MANUAL"        // 手动添加
    DataSourceImport    = "IMPORT"        // 批量导入
    DataSourceDiscovery = "AUTO_DISCOVERY" // 自动发现
)
```

### 处理动作枚举

```go
type DuplicateAction string

const (
    ActionOverride = "OVERRIDE" // 用发现的数据覆盖
    ActionIgnore   = "IGNORE"   // 忽略，保持现有数据
    ActionMerge    = "MERGE"    // 合并（预留功能）
)
```

## 🌐 API接口

### 1. 获取重复设备列表

```http
GET /api/ams-ce/devices/duplicates?device_type=host&limit=20&offset=0
```

**查询参数:**
- `device_type`: 设备类型 (`host` | `resource`)
- `limit`: 每页数量 (默认20，最大100)
- `offset`: 偏移量 (默认0)

**响应示例:**
```json
{
  "total": 5,
  "duplicates": [
    {
      "id": 1,
      "name": "web-server-01",
      "ip": "*********",
      "device_type": "host",
      "data_source": "MANUAL",
      "duplicate_info": {
        "conflict_ip": "*********",
        "existing_device_id": 1,
        "existing_source": "MANUAL",
        "detected_at": "2023-12-01T10:00:00Z",
        "differences": [...]
      },
      "created_at": "2023-11-01T10:00:00Z",
      "last_discovery_at": "2023-12-01T10:00:00Z"
    }
  ]
}
```

### 2. 解决重复设备

```http
POST /api/ams-ce/devices/duplicates/resolve
```

**请求体:**
```json
{
  "device_type": "host",
  "device_id": 1,
  "action": "OVERRIDE",
  "keep_fields": ["name", "note"]
}
```

**响应示例:**
```json
{
  "success": true,
  "message": "Host duplicate resolved successfully",
  "device": {
    "id": 1,
    "name": "web-server-01",
    "ip": "*********",
    "duplicate_status": "OVERRIDDEN"
  }
}
```

### 3. 批量解决重复设备

```http
POST /api/ams-ce/devices/duplicates/batch-resolve
```

**请求体:**
```json
{
  "operations": [
    {
      "device_type": "host",
      "device_id": 1,
      "action": "OVERRIDE"
    },
    {
      "device_type": "resource",
      "device_id": 2,
      "action": "IGNORE"
    }
  ]
}
```

### 4. 获取重复设备详情

```http
GET /api/ams-ce/devices/duplicates/host/1
```

### 5. 获取重复统计信息

```http
GET /api/ams-ce/devices/duplicates/stats
```

**响应示例:**
```json
{
  "duplicate_hosts": 3,
  "duplicate_resources": 2,
  "total_duplicates": 5
}
```

## 🔍 重复检测逻辑

### 检测流程

1. **IP地址提取**: 从设备信息中提取主要IP地址
   - 优先使用私网IP
   - 私网IP不存在时使用公网IP
   - 可配置是否忽略没有IP的设备

2. **数据库查询**: 在现有设备中查找相同IP的设备

3. **重复标记**: 如果找到重复设备，标记状态并记录详细信息

4. **用户决策**: 等待用户选择处理方式

### 配置选项

```yaml
duplicate_detection:
  enabled: true                              # 是否启用
  ip_matching:
    prefer_private_ip: true                  # 优先私网IP
    fallback_to_public_ip: true              # 备用公网IP
    ignore_empty_ip: true                    # 忽略空IP
  auto_rules:
    auto_ignore_same_source: false           # 自动忽略相同来源
    auto_override_manual_with_discovery: false # 自动覆盖手动数据
```

## 💡 使用场景

### 场景1: 手动添加后自动发现

1. 用户手动添加了一台服务器 (IP: *********)
2. 后续自动发现也发现了同一台服务器
3. 系统检测到IP重复，标记为 `DETECTED` 状态
4. 用户可以选择：
   - **覆盖**: 用自动发现的数据更新手动添加的记录
   - **忽略**: 保持手动添加的数据，忽略自动发现的数据

### 场景2: 批量导入冲突

1. 用户批量导入了设备清单
2. 自动发现发现了部分相同的设备
3. 系统批量标记重复状态
4. 用户可以批量处理这些重复

### 场景3: 信息不一致

1. 手动记录的设备名称为 "web-server-old"
2. 自动发现的设备名称为 "web-server-01"
3. 系统显示字段差异，用户可以选择保留哪些字段

## 🛠 开发指南

### 集成重复检测

在云资源发现时启用重复检测：

```go
// 在发现过程中启用重复检测
filters := map[string]string{
    "enable_duplicate_detection": "true",
}

resources, err := provider.DiscoverResources("ecs", filters)
```

### 自定义重复检测逻辑

```go
// 创建自定义配置
config := &models.DuplicateDetectionConfig{
    Enabled:         true,
    PreferPrivateIP: true,
    // ... 其他配置
}

// 创建检测器
detector := duplicate.NewDuplicateDetector(config)

// 检测重复
err := detector.DetectHostDuplicates(discoveredResources)
```

### 处理重复设备

```go
// 创建服务
service := duplicate.NewDuplicateService(config)

// 解决重复
req := &models.ResolveDuplicateRequest{
    DeviceType: models.DeviceTypeHost,
    DeviceID:   1,
    Action:     models.ActionOverride,
}

result, err := service.ResolveDuplicate(req, "admin")
```

## 📝 最佳实践

### 1. 配置建议

- **生产环境**: 启用重复检测，关闭自动处理
- **测试环境**: 可以启用自动忽略相同来源
- **开发环境**: 可以启用自动覆盖手动数据

### 2. 操作建议

- **定期检查**: 定期查看重复设备列表
- **批量处理**: 对于大量重复，使用批量操作
- **保留关键字段**: 覆盖时保留重要的业务字段

### 3. 监控建议

- **重复统计**: 监控重复设备数量趋势
- **处理效率**: 跟踪重复处理的响应时间
- **错误率**: 监控重复检测的错误率

## 🔧 故障排查

### 常见问题

1. **重复检测不工作**
   - 检查配置是否启用
   - 确认数据库迁移是否完成
   - 查看日志中的错误信息

2. **IP匹配不准确**
   - 检查IP提取逻辑
   - 确认网络信息格式
   - 调整IP匹配配置

3. **性能问题**
   - 优化数据库索引
   - 调整批量处理大小
   - 考虑异步处理

### 日志查看

```bash
# 查看重复检测相关日志
grep "duplicate" /var/log/arboris-ams.log

# 查看错误日志
grep "Error.*duplicate" /var/log/arboris-ams.log
```

## 📚 参考资料

- [API文档](./api_documentation.md)
- [数据库设计](./database_schema.md)
- [配置参考](./configuration_reference.md)
- [示例代码](../examples/duplicate_detection_example.go)
