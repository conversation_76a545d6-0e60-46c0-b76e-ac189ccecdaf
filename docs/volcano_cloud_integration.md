# 火山云集成文档

## 概述

本项目已成功集成火山云ECS服务，支持通过真实的火山云API进行云资源发现和管理。

## 功能特性

- ✅ **连接测试**: 验证火山云API连接是否正常
- ✅ **ECS实例发现**: 获取火山云ECS实例列表
- ✅ **资源详情查询**: 获取单个ECS实例的详细信息
- ✅ **状态同步**: 同步ECS实例的最新状态
- ✅ **过滤支持**: 支持按可用区、状态、名称等条件过滤
- ✅ **标签支持**: 获取和显示实例标签信息

## 配置要求

### 环境变量

在使用火山云功能前，需要设置以下环境变量：

```bash
export VOLCANO_ACCESS_KEY=your_access_key
export VOLCANO_SECRET_KEY=your_secret_key
export VOLCANO_REGION=cn-beijing  # 可选，默认为cn-beijing
export VOLCANO_ENDPOINT=          # 可选，使用默认端点
```

### 权限要求

确保您的火山云账号具有以下权限：
- `ecs:DescribeInstances` - 查询ECS实例列表
- `ecs:DescribeInstanceAttribute` - 查询ECS实例详情

## 使用示例

### 1. 基本使用

```go
package main

import (
    "arboris/src/modules/ams/cloud"
    "fmt"
    "log"
)

func main() {
    // 创建火山云配置
    config := cloud.ProviderConfig{
        Provider:  "volcano",
        Region:    "cn-beijing",
        AccessKey: "your_access_key",
        SecretKey: "your_secret_key",
    }

    // 创建火山云提供商
    provider := cloud.NewVolcanoProvider(config)

    // 测试连接
    if err := provider.TestConnection(); err != nil {
        log.Fatal("连接失败:", err)
    }

    // 发现ECS实例
    resources, err := provider.DiscoverResources("ecs", nil)
    if err != nil {
        log.Fatal("发现资源失败:", err)
    }

    fmt.Printf("发现 %d 个ECS实例\n", len(resources))
}
```

### 2. 使用过滤器

```go
// 基本过滤器示例
filters := map[string]string{
    "zone":           "cn-beijing-a",        // 按可用区过滤
    "status":         "running",             // 按状态过滤
    "name_like":      "web",                 // 按名称模糊匹配
    "instance_name":  "exact-name",          // 按名称精确匹配
    "instance_ids":   "i-123,i-456",         // 按实例ID列表过滤
}

// 高级过滤器示例
advancedFilters := map[string]string{
    "vpc_ids":        "vpc-123,vpc-456",     // 按VPC ID过滤
    "subnet_ids":     "subnet-123,subnet-456", // 按子网ID过滤
    "instance_types": "ecs.g1.large,ecs.c1.xlarge", // 按实例类型过滤
    "tags":           "Environment:production,Project:web-app", // 按标签过滤
    "max_results":    "50",                  // 限制返回数量
    "sort":           "name:asc",            // 排序 (name:asc/desc, created_time:asc/desc, status:asc/desc, zone:asc/desc)
}

resources, err := provider.DiscoverResources("ecs", filters)
```

### 3. 获取资源详情

```go
// 获取特定实例的详情
detail, err := provider.GetResourceDetail("ecs", "instance-id")
if err != nil {
    log.Printf("获取详情失败: %v", err)
} else {
    fmt.Printf("实例状态: %s\n", detail.Status)
}
```

### 4. 同步资源状态

```go
// 同步资源状态
err := provider.SyncResourceStatus(resources)
if err != nil {
    log.Printf("同步状态失败: %v", err)
}
```

## 运行示例程序

项目提供了完整的示例程序，可以直接运行测试：

```bash
# 编译示例程序
go build -mod=mod -o volcano_example examples/volcano_example.go

# 设置环境变量
export VOLCANO_ACCESS_KEY=your_access_key
export VOLCANO_SECRET_KEY=your_secret_key
export VOLCANO_REGION=cn-beijing

# 运行示例
./volcano_example
```

## 支持的资源类型

目前支持的资源类型：
- `ecs` - 云服务器实例 ✅
- `rds` - 云数据库 (待实现)
- `redis` - 云缓存 (待实现)
- `clb` - 负载均衡 (待实现)

## 返回的资源信息

每个ECS实例包含以下信息：

```go
type CloudResource struct {
    CloudID     string                 // 实例ID
    Name        string                 // 实例名称
    Type        string                 // 资源类型 (ecs)
    Region      string                 // 区域
    Zone        string                 // 可用区
    Status      string                 // 实例状态
    SpecInfo    map[string]interface{} // 规格信息 (CPU、内存、实例类型)
    NetworkInfo map[string]interface{} // 网络信息 (公网IP、私网IP)
    Tags        map[string]string      // 标签
    RawData     map[string]interface{} // 原始API响应数据
    CreatedTime time.Time              // 创建时间
    UpdatedTime time.Time              // 更新时间
}
```

## 错误处理

常见错误及解决方案：

1. **认证失败**
   - 检查AccessKey和SecretKey是否正确
   - 确认账号权限是否足够

2. **网络连接失败**
   - 检查网络连接
   - 确认Endpoint配置是否正确

3. **API限流**
   - 降低请求频率
   - 使用分页查询大量资源

## 技术实现

### SDK版本
- 使用 `github.com/volcengine/volcengine-go-sdk` v1.1.24

### 主要组件
- `VolcanoProvider`: 火山云服务提供商实现
- `convertECSInstanceToCloudResource`: 数据转换器
- 支持安全的类型转换和错误处理

### 设计特点
- 参考金山云实现模式，保持代码一致性
- 使用interface{}类型处理API响应，提高兼容性
- 完整的错误处理和日志记录
- 支持过滤器和分页查询

## 后续计划

- [ ] 实现RDS实例发现
- [ ] 实现Redis实例发现  
- [ ] 实现CLB实例发现
- [ ] 添加更多过滤器选项
- [ ] 支持批量操作
- [ ] 添加缓存机制提高性能
