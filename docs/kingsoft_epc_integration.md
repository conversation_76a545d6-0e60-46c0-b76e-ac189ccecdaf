# 金山云EPC裸金属服务器集成文档

## 📋 概述

本项目已成功集成金山云EPC（Elastic Physical Compute）裸金属服务器服务，支持通过真实的金山云API进行裸金属服务器资源发现和管理。

## 🚀 新增功能特性

- ✅ **EPC实例发现**: 获取金山云EPC裸金属服务器列表
- ✅ **资源详情查询**: 获取单个EPC实例的详细信息
- ✅ **状态同步**: 同步EPC实例的最新状态
- ✅ **高级过滤支持**: 支持按主机ID、项目ID、可用区等条件过滤
- ✅ **完整规格信息**: 获取CPU、内存、磁盘、RAID等硬件信息
- ✅ **网络信息**: 获取私网IP、公网IP、子网等网络配置

## 🔧 技术实现

### SDK集成
- 使用 `github.com/kingsoftcloud/sdk-go/v2/ksyun/client/epc/v20151101`
- 集成到现有的金山云Provider架构中
- 保持与ECS、RDS等服务的一致性

### 核心方法
```go
// 发现EPC实例
func (k *KingsoftProvider) discoverEPCInstances(filters map[string]string) ([]CloudResource, error)

// 获取EPC实例详情
func (k *KingsoftProvider) getEPCInstanceDetail(hostId string) (*CloudResource, error)

// 同步EPC实例状态
func (k *KingsoftProvider) syncEPCInstanceStatus(resource *CloudResource) error

// 转换EPC数据格式
func (k *KingsoftProvider) convertEPCHostToCloudResource(host interface{}) CloudResource
```

## 📊 支持的过滤器

### API级别过滤器
- `host_ids` - 主机ID列表 (逗号分隔)
- `project_ids` - 项目ID列表 (逗号分隔)
- `max_results` - 每页最大结果数 (默认100，最大1000)

### 客户端级别过滤器
- `zone` - 可用区过滤
- `name_like` - 名称模糊匹配
- `status` - 状态过滤 (注意：EPC可能没有明确状态字段)

## 🔍 使用示例

### 1. 基本使用

```go
package main

import (
    "arboris/src/modules/ams/cloud"
    "fmt"
    "log"
)

func main() {
    // 创建金山云配置
    config := cloud.ProviderConfig{
        Provider:  "kingsoft",
        Region:    "cn-beijing-6",
        AccessKey: "your_access_key",
        SecretKey: "your_secret_key",
    }

    // 创建金山云提供商
    provider := cloud.NewKingsoftProvider(config)

    // 发现EPC实例
    resources, err := provider.DiscoverResources("epc", nil)
    if err != nil {
        log.Fatal("发现EPC实例失败:", err)
    }

    fmt.Printf("发现 %d 个EPC裸金属服务器\n", len(resources))
}
```

### 2. 使用过滤器

```go
// 过滤器示例
filters := map[string]string{
    "zone":        "cn-beijing-6a",           // 按可用区过滤
    "name_like":   "web",                     // 按名称模糊匹配
    "host_ids":    "host-123,host-456",       // 按主机ID列表过滤
    "project_ids": "project-123,project-456", // 按项目ID列表过滤
    "max_results": "50",                      // 限制返回数量
}

resources, err := provider.DiscoverResources("epc", filters)
```

### 3. 获取资源详情

```go
// 获取特定EPC实例的详情
detail, err := provider.GetResourceDetail("epc", "host-id")
if err != nil {
    log.Printf("获取详情失败: %v", err)
} else {
    fmt.Printf("EPC实例状态: %s\n", detail.Status)
}
```

## 📋 返回的资源信息

每个EPC实例包含以下信息：

```go
type CloudResource struct {
    CloudID     string                 // 主机ID
    Name        string                 // 主机名称
    Type        string                 // 资源类型 (epc)
    Region      string                 // 区域
    Zone        string                 // 可用区
    Status      string                 // 实例状态
    SpecInfo    map[string]interface{} // 规格信息
    NetworkInfo map[string]interface{} // 网络信息
    Tags        map[string]string      // 标签
    RawData     map[string]interface{} // 原始API响应数据
    CreatedTime time.Time              // 创建时间
    UpdatedTime time.Time              // 更新时间
}
```

### SpecInfo 规格信息字段
```go
{
    "host_type":    "物理机类型",
    "product_type": "产品类型", 
    "cpu":          CPU核数,
    "memory":       内存大小(MB),
    "disk":         "磁盘配置",
    "raid":         "RAID配置",
    "os_name":      "操作系统名称",
    "sn":           "序列号"
}
```

### NetworkInfo 网络信息字段
```go
{
    "private_ip":  "主私网IP",
    "public_ip":   "主公网IP",
    "private_ips": []string{"所有私网IP"},
    "public_ips":  []string{"所有公网IP"},
    "vpc_id":      "VPC ID (EPC可能为空)",
    "subnet_id":   "子网ID"
}
```

## 🔄 与现有服务的对比

| 特性 | ECS云服务器 | EPC裸金属服务器 | 说明 |
|------|-------------|----------------|------|
| 虚拟化 | ✅ 虚拟化 | ❌ 物理机 | EPC提供独占物理资源 |
| 性能 | 共享资源 | ✅ 独占性能 | EPC性能更稳定 |
| 规格信息 | 实例类型 | ✅ 详细硬件信息 | EPC提供CPU、内存、磁盘、RAID等 |
| 网络配置 | VPC网络 | 物理网络 | EPC网络配置可能不同 |
| 状态管理 | 明确状态 | ⚠️ 状态字段可能不同 | EPC状态字段需要特殊处理 |
| 标签支持 | ✅ 完整支持 | ⚠️ 可能有限 | EPC标签支持可能不完整 |

## ⚠️ 注意事项

### 1. 状态字段差异
EPC实例可能没有明确的状态字段，当前实现：
- 默认状态设置为 "active"
- 如果存在HostStatus字段，则使用该字段
- 状态过滤器可能不完全适用

### 2. 网络配置差异
EPC作为物理机，网络配置可能与虚拟机不同：
- 可能没有VPC概念
- 网络接口配置可能更复杂
- 公网IP获取方式可能不同

### 3. 分页处理
EPC API支持分页，实现了完整的分页逻辑：
- 使用NextToken进行分页
- 默认每页100个实例
- 最大支持1000个实例防止无限循环

## 🚀 运行示例

```bash
# 设置环境变量
export KINGSOFT_ACCESS_KEY=your_access_key
export KINGSOFT_SECRET_KEY=your_secret_key
export KINGSOFT_REGION=cn-beijing-6

# 编译并运行示例
go build -mod=mod -o kingsoft_epc_example examples/kingsoft_epc_example.go
./kingsoft_epc_example
```

## 📈 支持的资源类型更新

金山云现在支持的完整资源类型：
- `ecs` - 云服务器 ✅
- `epc` - 裸金属服务器 ✨ **新增**
- `rds` - 云数据库 ✅
- `redis` - 云缓存 ✅
- `slb` - 负载均衡 ✅

## 🔮 后续计划

- [ ] 优化EPC状态字段处理
- [ ] 增强网络信息获取
- [ ] 添加EPC特有的标签支持
- [ ] 实现EPC实例操作（启动、停止等）
- [ ] 添加EPC监控指标获取
- [ ] 支持EPC实例规格变更

## 🐛 故障排查

### 常见问题

1. **EPC实例状态显示不准确**
   - 检查HostStatus字段是否存在
   - 可能需要根据业务需求自定义状态逻辑

2. **网络信息获取不完整**
   - 检查NetworkInterfaceAttributeSet字段
   - EPC网络配置可能与ECS不同

3. **分页查询超时**
   - 调整max_results参数
   - 检查网络连接和API限流

### 调试建议

1. **查看原始数据**
   ```go
   fmt.Printf("Raw EPC data: %+v\n", resource.RawData)
   ```

2. **启用详细日志**
   ```go
   log.Printf("EPC API response: %+v", response)
   ```

3. **测试单个实例**
   ```go
   filters := map[string]string{
       "host_ids": "specific-host-id",
   }
   ```

## ✅ 验证清单

- ✅ EPC SDK集成完成
- ✅ API调用正常工作
- ✅ 数据转换正确
- ✅ 过滤器功能完整
- ✅ 分页逻辑正确
- ✅ 错误处理完善
- ✅ 示例程序可运行
- ✅ 文档完整

---

**总结**: 金山云EPC裸金属服务器集成已完成，提供了完整的资源发现和管理功能，与现有的云服务器管理保持一致的接口和体验。
