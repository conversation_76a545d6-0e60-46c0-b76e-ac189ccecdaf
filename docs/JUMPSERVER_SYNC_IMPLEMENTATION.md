# JumpServer同步功能实现文档

## 🎯 实现目标

根据当前的JumpServer同步逻辑（只有绑定到节点的资源才会同步），为以下场景实现JumpServer同步：

1. **云资源导入时的同步**
2. **CSV批量导入机器并挂载到节点时的同步**

## 📋 实现概述

### 核心原则
- ✅ **遵循现有同步逻辑**：只有绑定到节点的资源才同步到JumpServer
- ✅ **事件驱动架构**：通过Redis Streams发布事件，由jumpserver-sync服务消费
- ✅ **向后兼容**：不影响现有功能，只是增加事件发布

### 同步触发时机

| 操作场景 | 触发事件 | 同步条件 |
|----------|----------|----------|
| 云资源导入成功 | `EventResourceCreate` | 资源创建后 |
| 云资源绑定到节点 | `EventResourceBind` | 绑定成功后 |
| CSV导入主机成功 | `EventHostCreate` | 主机创建后 |
| CSV导入主机挂载到节点 | `EventResourceBind` | 挂载成功后 |

## 🔧 技术实现

### 1. 云资源导入同步

#### 修改文件：`src/modules/ams/service/cloud_service.go`

**新增功能**：
- 在云资源导入成功后发布资源创建事件
- 在资源绑定到节点后发布资源绑定事件

**关键代码**：
```go
// 发布资源创建事件到JumpServer同步
go s.publishResourceCreateEvent(resource)

// 发布资源绑定事件到JumpServer同步
go s.publishResourceBindEvent(nodeId, resource.Id)
```

**新增方法**：
- `publishResourceCreateEvent()`: 发布资源创建事件
- `publishResourceBindEvent()`: 发布资源绑定事件

### 2. CSV批量导入同步

#### 修改文件：`src/modules/ams/http/host_csv.go`

**新增功能**：
- 在主机导入成功后发布主机创建事件
- 在主机挂载到节点后发布资源绑定事件

**关键代码**：
```go
// 发布主机创建事件到JumpServer同步
for _, host := range hostsToInsert {
    go publishHostCreateEvent(host)
}

// 发布资源绑定事件到JumpServer同步
for _, resId := range resIds {
    go publishResourceBindEventForCSV(node.Id, resId)
}
```

**新增方法**：
- `publishHostCreateEvent()`: 发布主机创建事件
- `publishResourceBindEventForCSV()`: 发布资源绑定事件

### 3. JumpServer同步处理器完善

#### 修改文件：`src/modules/jumpserver-sync/sync/handler.go`

**完善功能**：
- 实现资源创建事件处理：`handleResourceCreate()`
- 实现资源绑定事件处理：`handleResourceBind()`
- 实现资源解绑事件处理：`handleResourceUnbind()`

**关键逻辑**：
```go
// 资源创建处理
func (h *Handler) handleResourceCreate(event *events.Event) error {
    // 1. 解析资源事件数据
    // 2. 检查是否应该同步
    // 3. 映射为JumpServer资产
    // 4. 创建JumpServer资产
}

// 资源绑定处理
func (h *Handler) handleResourceBind(event *events.Event) error {
    // 1. 解析绑定事件数据
    // 2. 检查节点是否应该同步
    // 3. 确保节点和资产存在
    // 4. 将资产分配到节点
}
```

### 4. 资源映射器扩展

#### 修改文件：`src/modules/jumpserver-sync/mapper/mapper.go`

**新增功能**：
- 资源到JumpServer资产的映射：`MapResourceToJumpServer()`
- 资源映射规则应用：`applyResourceRules()`
- 资源条件匹配：`matchResourceCondition()`

**映射逻辑**：
```go
// 根据资源类型确定平台和协议
switch resourceData.Cate {
case "server", "vm", "ecs":
    platform = "Linux"
    protocols = []jumpserver.Protocol{{Name: "ssh", Port: 22}}
case "windows":
    platform = "Windows"
    protocols = []jumpserver.Protocol{{Name: "rdp", Port: 3389}}
case "database", "mysql":
    platform = "Database"
    protocols = []jumpserver.Protocol{{Name: "mysql", Port: 3306}}
}
```

## 📊 事件流程图

```
云资源导入流程：
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   发现云资源    │ -> │   选择并导入     │ -> │  绑定到节点     │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
                       ┌──────────────────┐    ┌─────────────────┐
                       │ ResourceCreate   │    │ ResourceBind    │
                       │     事件         │    │     事件        │
                       └──────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
                       ┌──────────────────┐    ┌─────────────────┐
                       │ 创建JumpServer   │    │ 分配资产到节点  │
                       │     资产         │    │               │
                       └──────────────────┘    └─────────────────┘

CSV导入流程：
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│  上传CSV文件    │ -> │   解析并导入     │ -> │  挂载到节点     │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
                       ┌──────────────────┐    ┌─────────────────┐
                       │   HostCreate     │    │ ResourceBind    │
                       │     事件         │    │     事件        │
                       └──────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
                       ┌──────────────────┐    ┌─────────────────┐
                       │ 创建JumpServer   │    │ 分配资产到节点  │
                       │     资产         │    │               │
                       └──────────────────┘    └─────────────────┘
```

## 🔍 同步过滤规则

### 节点过滤
- **包含路径**：只同步指定路径下的节点
- **排除路径**：排除指定路径下的节点
- **节点属性**：根据节点类型、叶子节点等过滤

### 资源过滤
- **资源类型**：根据cate字段过滤
- **租户**：根据tenant字段过滤
- **自定义条件**：支持正则表达式匹配

### 配置示例
```yaml
sync:
  rules:
    include_paths:
      - "/production"
      - "/staging"
    exclude_paths:
      - "/temp"
    filters:
      cate: "server"
      tenant: "default"
```

## 🚀 使用场景

### 场景1：云资源导入
```bash
# 1. 发现云资源
POST /api/ams-ce/cloud/discoveries

# 2. 选择资源并导入到节点
POST /api/ams-ce/cloud/discoveries/:id/import
{
  "node_id": 123,
  "resource_mapping": {...}
}

# 结果：资源自动同步到JumpServer并分配到对应节点
```

### 场景2：CSV批量导入
```bash
# CSV导入并挂载到节点
POST /api/ams-ce/hosts/csv/import
Content-Type: multipart/form-data
- file: hosts.csv
- node_ids: "123,456"

# 结果：主机自动同步到JumpServer并分配到指定节点
```

## 📝 日志记录

### 成功日志
```
Published resource create event for cloud resource: web-server-01
Published resource bind event for cloud resource web-server-01 to node /production/web
Published host create event for CSV imported host: db-server-01
Published resource bind event for CSV imported host db-server-01 to node /production/db
```

### 错误日志
```
Failed to publish resource create event for resource web-server-01: connection refused
Failed to get node 123 for resource bind event: node not found
```

## ⚠️ 注意事项

1. **Redis连接**：确保Redis服务正常运行，否则事件发布会失败
2. **JumpServer同步服务**：需要启动jumpserver-sync服务来消费事件
3. **权限控制**：同步的资产会继承节点的权限设置
4. **性能考虑**：大批量导入时，事件发布是异步的，不会影响导入性能
5. **错误处理**：事件发布失败不会影响资源导入，只会记录错误日志

## 🔧 故障排查

### 常见问题

1. **事件未发布**
   - 检查Redis连接配置
   - 确认JumpServer同步功能已启用

2. **资产未同步**
   - 检查同步过滤规则
   - 确认jumpserver-sync服务运行状态

3. **资产未分配到节点**
   - 检查节点是否存在于JumpServer
   - 确认节点路径映射规则

### 调试命令
```bash
# 检查Redis连接
redis-cli ping

# 查看事件流
redis-cli XREAD STREAMS arboris:sync:events 0

# 检查jumpserver-sync服务状态
systemctl status jumpserver-sync
```

## 📈 性能优化

1. **异步处理**：所有事件发布都是异步的，不阻塞主流程
2. **批量处理**：jumpserver-sync支持批量消费事件
3. **缓存机制**：Handler中使用缓存减少JumpServer API调用
4. **错误重试**：支持事件处理失败时的重试机制

## 🎉 总结

通过本次实现，我们成功为云资源导入和CSV批量导入功能添加了JumpServer同步支持，实现了：

- ✅ **完整的事件驱动同步**
- ✅ **灵活的过滤和映射规则**
- ✅ **向后兼容的设计**
- ✅ **详细的日志记录**
- ✅ **良好的错误处理**

现在用户可以在导入资源时自动同步到JumpServer，大大提升了运维效率！
