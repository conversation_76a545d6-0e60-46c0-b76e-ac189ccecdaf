# 云厂商配置管理文档

## 📋 概述

本项目已实现统一的云厂商配置管理系统，所有云厂商的AK/SK认证信息都从配置文件中读取，不再使用环境变量或其他方式。

## 🔧 配置文件结构

### 配置文件位置
系统会按以下优先级查找配置文件：
1. `etc/cloud-config.local.yml` - 本地开发配置（优先级最高）
2. `etc/cloud-config.yml` - 正式配置文件
3. `etc/cloud-config-example.yml` - 示例配置文件

### 配置文件格式

```yaml
providers:
  # 金山云配置
  kingsoft:
    credentials:
      access_key: "your_kingsoft_access_key"
      secret_key: "your_kingsoft_secret_key"
      default_region: "cn-beijing-6"
    regions:
      - cn-beijing-6
      - cn-shanghai-2
      - cn-guangzhou-1
    resource_types:
      - ecs      # 云服务器
      - epc      # 裸金属服务器
      - rds      # 云数据库
      - redis    # 云缓存
      - slb      # 负载均衡
    api:
      endpoint: "https://ecs.cn-beijing-6.api.ksyun.com"
      version: "2016-03-04"
      timeout: 30

  # 火山云配置
  volcano:
    credentials:
      access_key: "your_volcano_access_key"
      secret_key: "your_volcano_secret_key"
      default_region: "cn-beijing"
    regions:
      - cn-beijing
      - cn-shanghai
      - cn-guangzhou
    resource_types:
      - ecs      # 云服务器
      - rds      # 云数据库
      - redis    # 云缓存
      - clb      # 负载均衡

  # 阿里云配置
  aliyun:
    credentials:
      access_key: "your_aliyun_access_key"
      secret_key: "your_aliyun_secret_key"
      default_region: "cn-hangzhou"

  # 腾讯云配置
  tencent:
    credentials:
      access_key: "your_tencent_secret_id"
      secret_key: "your_tencent_secret_key"
      default_region: "ap-beijing"

  # 天翼云配置
  ctyun:
    credentials:
      access_key: "your_ctyun_access_key"
      secret_key: "your_ctyun_secret_key"
      default_region: "cn-bj4"
```

## 🚀 使用方式

### 1. 基本使用

```go
package main

import (
    "arboris/src/modules/ams/cloud"
    "fmt"
)

func main() {
    // 创建云厂商实例（自动从配置文件读取认证信息）
    provider, err := cloud.NewKingsoftProvider()
    if err != nil {
        fmt.Printf("创建金山云提供商失败: %v\n", err)
        return
    }

    // 测试连接
    err = provider.TestConnection()
    if err != nil {
        fmt.Printf("连接测试失败: %v\n", err)
        return
    }

    // 发现资源
    resources, err := provider.DiscoverResources("ecs", nil)
    if err != nil {
        fmt.Printf("资源发现失败: %v\n", err)
        return
    }

    fmt.Printf("发现 %d 个ECS实例\n", len(resources))
}
```

### 2. 支持的云厂商

```go
// 金山云
provider, err := cloud.NewKingsoftProvider()

// 火山云
provider, err := cloud.NewVolcanoProvider()

// 阿里云
provider, err := cloud.NewAliyunProvider()

// 腾讯云
provider, err := cloud.NewTencentProvider()

// 天翼云
provider, err := cloud.NewCtyunProvider()

// 通用方式
provider, err := cloud.NewCloudProvider("kingsoft")
```

### 3. 配置管理API

```go
import "arboris/src/modules/ams/config"

// 获取所有已配置的云厂商
providers := config.GetAllProviders()

// 检查特定厂商是否已配置
configured := config.IsProviderConfigured("kingsoft")

// 验证厂商配置
err := config.ValidateProviderConfig("kingsoft")

// 获取认证信息
accessKey, secretKey, region, err := config.GetProviderCredentials("kingsoft")

// 获取支持的区域
regions, err := config.GetProviderRegions("kingsoft")

// 获取支持的资源类型
resourceTypes, err := config.GetProviderResourceTypes("kingsoft")

// 重新加载配置
err := config.ReloadCloudConfig()
```

## 📝 配置步骤

### 1. 复制配置文件

```bash
# 复制示例配置文件
cp etc/cloud-config-example.yml etc/cloud-config.yml

# 或者创建本地开发配置
cp etc/cloud-config-example.yml etc/cloud-config.local.yml
```

### 2. 编辑配置文件

```bash
# 编辑配置文件
vim etc/cloud-config.yml

# 或者编辑本地配置
vim etc/cloud-config.local.yml
```

### 3. 配置认证信息

只需要配置您实际使用的云厂商：

```yaml
providers:
  kingsoft:
    credentials:
      access_key: "LTAI5tFhxxxxxxxxxxxxxxxx"
      secret_key: "2Bpxxxxxxxxxxxxxxxxxxxxxxxx"
      default_region: "cn-beijing-6"
  
  volcano:
    credentials:
      access_key: "AKLTxxxxxxxxxxxxxxxx"
      secret_key: "Wkdxxxxxxxxxxxxxxxxxxxxxxxx"
      default_region: "cn-beijing"
```

### 4. 验证配置

```bash
# 运行配置验证示例
go run examples/cloud_config_example.go
```

## 🔍 配置验证

系统会自动验证配置的完整性：

1. **必要字段检查**
   - access_key 不能为空
   - secret_key 不能为空
   - default_region 不能为空

2. **区域有效性检查**
   - default_region 必须在支持的区域列表中

3. **连接测试**
   - 验证认证信息是否正确
   - 测试API连接是否正常

## 🛡️ 安全建议

### 1. 配置文件权限

```bash
# 设置配置文件权限，只有所有者可读写
chmod 600 etc/cloud-config.yml
chmod 600 etc/cloud-config.local.yml
```

### 2. 版本控制

```bash
# 将配置文件添加到 .gitignore
echo "etc/cloud-config.yml" >> .gitignore
echo "etc/cloud-config.local.yml" >> .gitignore
```

### 3. 环境分离

- **开发环境**: 使用 `etc/cloud-config.local.yml`
- **测试环境**: 使用 `etc/cloud-config.yml`
- **生产环境**: 使用独立的配置管理系统

## 🔄 迁移指南

### 从环境变量迁移

**旧方式（环境变量）:**
```bash
export KINGSOFT_ACCESS_KEY=your_access_key
export KINGSOFT_SECRET_KEY=your_secret_key
export KINGSOFT_REGION=cn-beijing-6
```

**新方式（配置文件）:**
```yaml
providers:
  kingsoft:
    credentials:
      access_key: "your_access_key"
      secret_key: "your_secret_key"
      default_region: "cn-beijing-6"
```

### 代码迁移

**旧方式:**
```go
config := cloud.ProviderConfig{
    Provider:  "kingsoft",
    Region:    "cn-beijing-6",
    AccessKey: "your_access_key",
    SecretKey: "your_secret_key",
}
provider := cloud.NewKingsoftProvider(config)
```

**新方式:**
```go
provider, err := cloud.NewKingsoftProvider()
if err != nil {
    // 处理配置错误
}
```

## 🎯 优势

### 1. 统一管理
- 所有云厂商配置集中在一个文件中
- 便于维护和管理
- 支持版本控制

### 2. 安全性
- 避免在代码中硬编码认证信息
- 支持文件权限控制
- 便于环境分离

### 3. 灵活性
- 支持多环境配置
- 支持配置热重载
- 支持动态配置更新

### 4. 易用性
- 自动配置验证
- 详细的错误提示
- 完整的示例和文档

## 🐛 故障排查

### 常见问题

1. **配置文件找不到**
   ```
   Error: cloud configuration file not found
   ```
   解决：确保配置文件存在于 `etc/` 目录下

2. **认证信息为空**
   ```
   Error: access_key not configured for provider kingsoft
   ```
   解决：检查配置文件中的认证信息是否正确填写

3. **区域不支持**
   ```
   Error: default_region cn-beijing-7 is not in supported regions
   ```
   解决：使用支持的区域，参考配置文件中的 regions 列表

4. **连接测试失败**
   ```
   Error: connection test failed
   ```
   解决：检查网络连接和认证信息是否正确

### 调试方法

1. **查看配置解析**
   ```go
   err := config.ParseCloudConfig()
   if err != nil {
       fmt.Printf("配置解析失败: %v\n", err)
   }
   ```

2. **验证特定厂商配置**
   ```go
   err := config.ValidateProviderConfig("kingsoft")
   if err != nil {
       fmt.Printf("配置验证失败: %v\n", err)
   }
   ```

3. **运行配置示例**
   ```bash
   go run examples/cloud_config_example.go
   ```

## ✅ 检查清单

- [ ] 配置文件已创建
- [ ] 认证信息已填写
- [ ] 配置验证通过
- [ ] 连接测试成功
- [ ] 文件权限已设置
- [ ] 版本控制已配置

---

**总结**: 新的配置管理系统提供了统一、安全、灵活的云厂商配置方式，大大简化了多云环境的管理复杂度。
