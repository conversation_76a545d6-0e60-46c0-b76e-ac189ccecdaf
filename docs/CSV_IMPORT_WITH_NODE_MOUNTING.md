# CSV批量导入主机并挂载到节点功能

## 功能概述

CSV批量导入功能现在支持在导入主机时自动挂载到指定的节点，实现了与云资源导入流程的一致性。

## 功能特性

- ✅ **向后兼容**：不指定节点时，功能与原来完全一致
- ✅ **节点选择**：支持导入时指定一个或多个节点
- ✅ **自动挂载**：导入成功后自动挂载到指定节点
- ✅ **节点验证**：自动验证节点是否存在且为叶子节点
- ✅ **详细反馈**：返回挂载结果统计信息

## API接口

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| file | file | 是 | CSV或Excel文件 |
| node_ids | string | 否 | 节点ID列表，逗号分隔 |

### 使用示例

#### 1. 传统方式（不挂载节点）

```bash
curl -X POST http://localhost:8080/api/ams-ce/hosts/csv/import \
  -H "X-User-Token: your_token" \
  -F "file=@hosts.csv"
```

#### 2. 导入并挂载到单个节点

```bash
curl -X POST http://localhost:8080/api/ams-ce/hosts/csv/import \
  -H "X-User-Token: your_token" \
  -F "file=@hosts.csv" \
  -F "node_ids=123"
```

#### 3. 导入并挂载到多个节点

```bash
curl -X POST http://localhost:8080/api/ams-ce/hosts/csv/import \
  -H "X-User-Token: your_token" \
  -F "file=@hosts.csv" \
  -F "node_ids=123,456,789"
```

### 响应格式

#### 成功响应（无节点挂载）

```json
{
  "err": null,
  "dat": {
    "success_count": 10,
    "failed_count": 0,
    "total_rows": 10
  }
}
```

#### 成功响应（包含节点挂载）

```json
{
  "err": null,
  "dat": {
    "success_count": 10,
    "failed_count": 0,
    "total_rows": 10,
    "mounted_to_nodes": 3,
    "mounted_count": 30
  }
}
```

#### 部分失败响应

```json
{
  "err": "some rows failed to import",
  "dat": {
    "success_count": 8,
    "failed_count": 2,
    "total_rows": 10,
    "mounted_to_nodes": 3,
    "mounted_count": 24,
    "import_id": "import_20240729_123456"
  }
}
```

## 响应字段说明

| 字段名 | 说明 |
|--------|------|
| success_count | 成功导入的主机数量 |
| failed_count | 导入失败的主机数量 |
| total_rows | 总行数（不包括标题行） |
| mounted_to_nodes | 挂载到的节点数量（仅在指定节点时返回） |
| mounted_count | 总挂载次数（主机数 × 节点数）（仅在指定节点时返回） |
| import_id | 导入批次ID，用于下载失败数据（仅在有失败记录时返回） |

## 错误处理

### 节点相关错误

1. **无效的节点ID**
   ```json
   {
     "err": "invalid node_id: abc",
     "dat": null
   }
   ```

2. **节点不存在**
   ```json
   {
     "err": "some nodes not found",
     "dat": null
   }
   ```

3. **节点不是叶子节点**
   ```json
   {
     "err": "node Production (id: 123) is not a leaf node",
     "dat": null
   }
   ```

### 文件相关错误

与原有的CSV导入错误处理保持一致。

## 实现细节

### 节点验证流程

1. 解析 `node_ids` 参数
2. 验证节点ID格式
3. 查询节点是否存在
4. 检查节点是否为叶子节点

### 挂载流程

1. 主机导入成功后
2. 为每个成功导入的主机生成资源UUID
3. 获取对应的资源ID
4. 为每个指定的节点执行挂载操作
5. 记录挂载结果

### 日志记录

- 成功挂载：记录挂载的主机数量和节点信息
- 挂载失败：记录错误信息，但不影响导入结果

## 与云资源导入的一致性

| 功能 | 云资源导入 | CSV导入 |
|------|------------|---------|
| 节点选择 | ✅ 必须指定 | ✅ 可选指定 |
| 自动挂载 | ✅ 支持 | ✅ 支持 |
| 批量操作 | ✅ 支持 | ✅ 支持 |
| 错误处理 | ✅ 完善 | ✅ 完善 |

## 使用建议

1. **生产环境**：建议先在测试环境验证节点ID的正确性
2. **大批量导入**：建议分批导入，避免单次操作过多主机
3. **节点选择**：确保选择的节点是叶子节点且有相应权限
4. **错误处理**：关注返回的挂载统计信息，确保挂载成功

## 兼容性说明

- ✅ **完全向后兼容**：现有的导入脚本无需修改
- ✅ **渐进式升级**：可以逐步迁移到新的挂载功能
- ✅ **API稳定性**：原有的响应格式保持不变，只是增加了新字段
