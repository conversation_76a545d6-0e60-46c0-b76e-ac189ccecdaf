package main

import (
	"encoding/json"
	"fmt"
	"log"
	"os"

	"arboris/src/modules/ams/cloud"
)

func main() {
	// 示例：使用真实的云厂商API调用

	// 1. 阿里云示例
	fmt.Println("=== 阿里云资源发现示例 ===")
	aliyunExample()

	// 2. 腾讯云示例
	fmt.Println("\n=== 腾讯云资源发现示例 ===")
	tencentExample()

	// 3. 火山云示例
	fmt.Println("\n=== 火山云资源发现示例 ===")
	volcanoExample()

	// 4. 金山云示例
	fmt.Println("\n=== 金山云资源发现示例 ===")
	kingsoftExample()

	// 5. 天翼云示例
	fmt.Println("\n=== 天翼云资源发现示例 ===")
	ctyunExample()
}

func aliyunExample() {
	// 从环境变量获取阿里云配置
	config := cloud.ProviderConfig{
		Provider:  "aliyun",
		Region:    getEnv("ALIYUN_REGION", "cn-hangzhou"),
		AccessKey: getEnv("ALIYUN_ACCESS_KEY", ""),
		SecretKey: getEnv("ALIYUN_SECRET_KEY", ""),
		Endpoint:  getEnv("ALIYUN_ENDPOINT", ""),
	}

	if config.AccessKey == "" || config.SecretKey == "" {
		fmt.Println("请设置环境变量 ALIYUN_ACCESS_KEY 和 ALIYUN_SECRET_KEY")
		return
	}

	// 创建阿里云提供商
	provider := cloud.NewAliyunProvider(config)

	// 测试连接
	if err := provider.TestConnection(); err != nil {
		log.Printf("阿里云连接测试失败: %v", err)
		return
	}

	// 发现ECS实例
	filters := map[string]string{
		"status": "Running",
		// "zone": "cn-hangzhou-b",
		// "name_like": "web",
	}

	resources, err := provider.DiscoverResources("ecs", filters)
	if err != nil {
		log.Printf("发现阿里云ECS实例失败: %v", err)
		return
	}

	fmt.Printf("发现 %d 个阿里云ECS实例:\n", len(resources))
	for _, resource := range resources {
		printResource(resource)
	}
}

func tencentExample() {
	// 从环境变量获取腾讯云配置
	config := cloud.ProviderConfig{
		Provider:  "tencent",
		Region:    getEnv("TENCENT_REGION", "ap-beijing"),
		AccessKey: getEnv("TENCENT_SECRET_ID", ""),
		SecretKey: getEnv("TENCENT_SECRET_KEY", ""),
		Endpoint:  getEnv("TENCENT_ENDPOINT", ""),
	}

	if config.AccessKey == "" || config.SecretKey == "" {
		fmt.Println("请设置环境变量 TENCENT_SECRET_ID 和 TENCENT_SECRET_KEY")
		return
	}

	// 创建腾讯云提供商
	provider := cloud.NewTencentProvider(config)

	// 测试连接
	if err := provider.TestConnection(); err != nil {
		log.Printf("腾讯云连接测试失败: %v", err)
		return
	}

	// 发现CVM实例
	filters := map[string]string{
		"status": "RUNNING",
		// "zone": "ap-beijing-1",
		// "name_like": "web",
	}

	resources, err := provider.DiscoverResources("cvm", filters)
	if err != nil {
		log.Printf("发现腾讯云CVM实例失败: %v", err)
		return
	}

	fmt.Printf("发现 %d 个腾讯云CVM实例:\n", len(resources))
	for _, resource := range resources {
		printResource(resource)
	}
}

func volcanoExample() {
	// 从环境变量获取火山云配置
	config := cloud.ProviderConfig{
		Provider:  "volcano",
		Region:    getEnv("VOLCANO_REGION", "cn-beijing"),
		AccessKey: getEnv("VOLCANO_ACCESS_KEY", ""),
		SecretKey: getEnv("VOLCANO_SECRET_KEY", ""),
		Endpoint:  getEnv("VOLCANO_ENDPOINT", ""),
	}

	if config.AccessKey == "" || config.SecretKey == "" {
		fmt.Println("请设置环境变量 VOLCANO_ACCESS_KEY 和 VOLCANO_SECRET_KEY")
		return
	}

	// 创建火山云提供商
	provider := cloud.NewVolcanoProvider(config)

	// 测试连接
	if err := provider.TestConnection(); err != nil {
		log.Printf("火山云连接测试失败: %v", err)
		return
	}

	// 发现ECS实例
	filters := map[string]string{
		"status": "RUNNING",
		// "zone": "cn-beijing-a",
		// "name_like": "web",
	}

	resources, err := provider.DiscoverResources("ecs", filters)
	if err != nil {
		log.Printf("发现火山云ECS实例失败: %v", err)
		return
	}

	fmt.Printf("发现 %d 个火山云ECS实例:\n", len(resources))
	for _, resource := range resources {
		printResource(resource)
	}
}

func kingsoftExample() {
	// 从环境变量获取金山云配置
	config := cloud.ProviderConfig{
		Provider:  "kingsoft",
		Region:    getEnv("KINGSOFT_REGION", "cn-beijing-6"),
		AccessKey: getEnv("KINGSOFT_ACCESS_KEY", ""),
		SecretKey: getEnv("KINGSOFT_SECRET_KEY", ""),
		Endpoint:  getEnv("KINGSOFT_ENDPOINT", ""),
	}

	if config.AccessKey == "" || config.SecretKey == "" {
		fmt.Println("请设置环境变量 KINGSOFT_ACCESS_KEY 和 KINGSOFT_SECRET_KEY")
		return
	}

	// 创建金山云提供商
	provider := cloud.NewKingsoftProvider(config)

	// 测试连接
	if err := provider.TestConnection(); err != nil {
		log.Printf("金山云连接测试失败: %v", err)
		return
	}

	// 发现ECS实例
	filters := map[string]string{
		"status": "running",
		// "zone": "cn-beijing-6a",
		// "name_like": "web",
	}

	resources, err := provider.DiscoverResources("ecs", filters)
	if err != nil {
		log.Printf("发现金山云ECS实例失败: %v", err)
		return
	}

	fmt.Printf("发现 %d 个金山云ECS实例:\n", len(resources))
	for _, resource := range resources {
		printResource(resource)
	}
}

func ctyunExample() {
	// 从环境变量获取天翼云配置
	config := cloud.ProviderConfig{
		Provider:  "ctyun",
		Region:    getEnv("CTYUN_REGION", "cn-bj"),
		AccessKey: getEnv("CTYUN_ACCESS_KEY", ""),
		SecretKey: getEnv("CTYUN_SECRET_KEY", ""),
		Endpoint:  getEnv("CTYUN_ENDPOINT", ""),
	}

	if config.AccessKey == "" || config.SecretKey == "" {
		fmt.Println("请设置环境变量 CTYUN_ACCESS_KEY 和 CTYUN_SECRET_KEY")
		return
	}

	// 创建天翼云提供商
	provider := cloud.NewCtyunProvider(config)

	// 测试连接
	if err := provider.TestConnection(); err != nil {
		log.Printf("天翼云连接测试失败: %v", err)
		return
	}

	// 发现ECS实例
	filters := map[string]string{
		"status": "ACTIVE",
		// "zone": "cn-bj-1a",
		// "name_like": "web",
	}

	resources, err := provider.DiscoverResources("ecs", filters)
	if err != nil {
		log.Printf("发现天翼云ECS实例失败: %v", err)
		return
	}

	fmt.Printf("发现 %d 个天翼云ECS实例:\n", len(resources))
	for _, resource := range resources {
		printResource(resource)
	}
}

func printResource(resource cloud.CloudResource) {
	fmt.Printf("  - ID: %s\n", resource.CloudID)
	fmt.Printf("    名称: %s\n", resource.Name)
	fmt.Printf("    类型: %s\n", resource.Type)
	fmt.Printf("    地域: %s\n", resource.Region)
	fmt.Printf("    可用区: %s\n", resource.Zone)
	fmt.Printf("    状态: %s\n", resource.Status)
	fmt.Printf("    创建时间: %s\n", resource.CreatedTime.Format("2006-01-02 15:04:05"))

	if len(resource.SpecInfo) > 0 {
		fmt.Printf("    规格信息:\n")
		for k, v := range resource.SpecInfo {
			fmt.Printf("      %s: %v\n", k, v)
		}
	}

	if len(resource.NetworkInfo) > 0 {
		fmt.Printf("    网络信息:\n")
		for k, v := range resource.NetworkInfo {
			fmt.Printf("      %s: %v\n", k, v)
		}
	}

	if len(resource.Tags) > 0 {
		fmt.Printf("    标签:\n")
		for k, v := range resource.Tags {
			fmt.Printf("      %s: %s\n", k, v)
		}
	}

	fmt.Println()
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
