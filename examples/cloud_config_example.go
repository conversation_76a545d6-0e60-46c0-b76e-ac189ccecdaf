package main

import (
	"fmt"

	"arboris/src/modules/ams/cloud"
	"arboris/src/modules/ams/config"
)

func main() {
	fmt.Println("云厂商配置管理示例")
	fmt.Println("==================")
	fmt.Println()

	// 1. 检查配置文件是否存在
	fmt.Println("1. 检查配置文件...")
	err := config.ParseCloudConfig()
	if err != nil {
		fmt.Printf("❌ 配置文件解析失败: %v\n", err)
		fmt.Println()
		fmt.Println("请确保配置文件存在:")
		fmt.Println("- etc/cloud-config.yml")
		fmt.Println("- etc/cloud-config.local.yml")
		fmt.Println("- etc/cloud-config-example.yml")
		return
	}
	fmt.Println("✅ 配置文件解析成功")
	fmt.Println()

	// 2. 获取所有已配置的云厂商
	fmt.Println("2. 检查已配置的云厂商...")
	providers := config.GetAllProviders()
	if len(providers) == 0 {
		fmt.Println("❌ 没有找到已配置的云厂商")
		fmt.Println()
		showConfigurationGuide()
		return
	}

	fmt.Printf("✅ 找到 %d 个已配置的云厂商: %v\n", len(providers), providers)
	fmt.Println()

	// 3. 逐个测试云厂商配置
	fmt.Println("3. 测试云厂商配置...")
	for _, providerName := range providers {
		fmt.Printf("测试 %s 配置...\n", providerName)
		
		// 验证配置
		err := config.ValidateProviderConfig(providerName)
		if err != nil {
			fmt.Printf("  ❌ 配置验证失败: %v\n", err)
			continue
		}
		fmt.Printf("  ✅ 配置验证通过\n")

		// 获取认证信息
		accessKey, secretKey, region, err := config.GetProviderCredentials(providerName)
		if err != nil {
			fmt.Printf("  ❌ 获取认证信息失败: %v\n", err)
			continue
		}
		fmt.Printf("  ✅ 认证信息: AK=%s***, SK=%s***, Region=%s\n", 
			maskString(accessKey), maskString(secretKey), region)

		// 创建云厂商实例
		provider, err := cloud.NewCloudProvider(providerName)
		if err != nil {
			fmt.Printf("  ❌ 创建云厂商实例失败: %v\n", err)
			continue
		}
		fmt.Printf("  ✅ 云厂商实例创建成功\n")

		// 测试连接
		err = provider.TestConnection()
		if err != nil {
			fmt.Printf("  ❌ 连接测试失败: %v\n", err)
		} else {
			fmt.Printf("  ✅ 连接测试成功\n")
		}

		// 获取支持的资源类型
		resourceTypes := provider.GetSupportedResourceTypes()
		fmt.Printf("  ✅ 支持的资源类型: %v\n", resourceTypes)
		
		fmt.Println()
	}

	// 4. 演示资源发现
	fmt.Println("4. 演示资源发现...")
	if len(providers) > 0 {
		providerName := providers[0]
		fmt.Printf("使用 %s 进行资源发现演示...\n", providerName)
		
		provider, err := cloud.NewCloudProvider(providerName)
		if err != nil {
			fmt.Printf("❌ 创建云厂商实例失败: %v\n", err)
			return
		}

		resourceTypes := provider.GetSupportedResourceTypes()
		if len(resourceTypes) > 0 {
			resourceType := resourceTypes[0]
			fmt.Printf("发现 %s 资源...\n", resourceType)
			
			// 使用基本过滤器
			filters := map[string]string{
				"max_results": "5", // 限制返回数量
			}
			
			resources, err := provider.DiscoverResources(resourceType, filters)
			if err != nil {
				fmt.Printf("❌ 资源发现失败: %v\n", err)
			} else {
				fmt.Printf("✅ 发现 %d 个 %s 资源\n", len(resources), resourceType)
				
				// 显示前几个资源的基本信息
				for i, resource := range resources {
					if i >= 3 { // 只显示前3个
						break
					}
					fmt.Printf("  资源 %d: ID=%s, Name=%s, Status=%s\n", 
						i+1, resource.CloudID, resource.Name, resource.Status)
				}
			}
		}
	}
	fmt.Println()

	// 5. 显示配置管理功能
	fmt.Println("5. 配置管理功能演示...")
	fmt.Println("✅ 支持的功能:")
	fmt.Println("  - 从配置文件自动读取AK/SK")
	fmt.Println("  - 支持多云厂商配置")
	fmt.Println("  - 配置验证和错误提示")
	fmt.Println("  - 动态配置更新")
	fmt.Println("  - 配置文件热重载")
	fmt.Println()

	fmt.Println("🎉 配置管理示例完成！")
}

func showConfigurationGuide() {
	fmt.Println("📝 配置指南:")
	fmt.Println()
	fmt.Println("请在 etc/cloud-config.yml 文件中配置云厂商认证信息:")
	fmt.Println()
	fmt.Println("providers:")
	fmt.Println("  kingsoft:")
	fmt.Println("    credentials:")
	fmt.Println("      access_key: \"your_kingsoft_access_key\"")
	fmt.Println("      secret_key: \"your_kingsoft_secret_key\"")
	fmt.Println("      default_region: \"cn-beijing-6\"")
	fmt.Println()
	fmt.Println("  volcano:")
	fmt.Println("    credentials:")
	fmt.Println("      access_key: \"your_volcano_access_key\"")
	fmt.Println("      secret_key: \"your_volcano_secret_key\"")
	fmt.Println("      default_region: \"cn-beijing\"")
	fmt.Println()
	fmt.Println("  aliyun:")
	fmt.Println("    credentials:")
	fmt.Println("      access_key: \"your_aliyun_access_key\"")
	fmt.Println("      secret_key: \"your_aliyun_secret_key\"")
	fmt.Println("      default_region: \"cn-hangzhou\"")
	fmt.Println()
	fmt.Println("  tencent:")
	fmt.Println("    credentials:")
	fmt.Println("      access_key: \"your_tencent_secret_id\"")
	fmt.Println("      secret_key: \"your_tencent_secret_key\"")
	fmt.Println("      default_region: \"ap-beijing\"")
	fmt.Println()
	fmt.Println("  ctyun:")
	fmt.Println("    credentials:")
	fmt.Println("      access_key: \"your_ctyun_access_key\"")
	fmt.Println("      secret_key: \"your_ctyun_secret_key\"")
	fmt.Println("      default_region: \"cn-bj4\"")
	fmt.Println()
	fmt.Println("💡 提示:")
	fmt.Println("- 只需要配置您实际使用的云厂商")
	fmt.Println("- 可以创建 etc/cloud-config.local.yml 用于本地开发")
	fmt.Println("- 配置文件支持热重载，修改后无需重启")
}

func maskString(s string) string {
	if len(s) <= 6 {
		return s
	}
	return s[:3] + "***" + s[len(s)-3:]
}

// 演示动态配置功能
func demonstrateConfigManagement() {
	fmt.Println("\n🔧 动态配置管理演示:")
	
	// 检查特定厂商是否配置
	providers := []string{"kingsoft", "volcano", "aliyun", "tencent", "ctyun"}
	for _, provider := range providers {
		configured := config.IsProviderConfigured(provider)
		status := "❌ 未配置"
		if configured {
			status = "✅ 已配置"
		}
		fmt.Printf("  %s: %s\n", provider, status)
	}
	
	// 获取支持的区域
	fmt.Println("\n📍 支持的区域:")
	for _, provider := range providers {
		if config.IsProviderConfigured(provider) {
			regions, err := config.GetProviderRegions(provider)
			if err == nil {
				fmt.Printf("  %s: %v\n", provider, regions)
			}
		}
	}
	
	// 获取支持的资源类型
	fmt.Println("\n🔗 支持的资源类型:")
	for _, provider := range providers {
		if config.IsProviderConfigured(provider) {
			resourceTypes, err := config.GetProviderResourceTypes(provider)
			if err == nil {
				fmt.Printf("  %s: %v\n", provider, resourceTypes)
			}
		}
	}
}
