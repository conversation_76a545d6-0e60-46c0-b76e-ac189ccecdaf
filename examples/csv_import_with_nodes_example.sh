#!/bin/bash

# CSV批量导入主机并挂载到节点的示例脚本

# 配置
API_BASE="http://localhost:8080"
TOKEN="your_token_here"
CSV_FILE="test_hosts.csv"

# 创建测试CSV文件
create_test_csv() {
    echo "Creating test CSV file: $CSV_FILE"
    cat > $CSV_FILE << EOF
ip,ident,name,cate,note
*************,test-host-001,测试主机001,server,CSV导入测试主机1
*************,test-host-002,测试主机002,server,CSV导入测试主机2
*************,test-host-003,测试主机003,server,CSV导入测试主机3
EOF
    echo "Test CSV file created successfully"
}

# 测试1: 传统导入（不挂载节点）
test_traditional_import() {
    echo "=== 测试1: 传统导入（不挂载节点） ==="
    
    response=$(curl -s -X POST "$API_BASE/api/ams-ce/hosts/csv/import" \
        -H "X-User-Token: $TOKEN" \
        -F "file=@$CSV_FILE")
    
    echo "Response: $response"
    echo ""
}

# 测试2: 导入并挂载到单个节点
test_single_node_import() {
    local node_id=$1
    echo "=== 测试2: 导入并挂载到单个节点 (ID: $node_id) ==="
    
    response=$(curl -s -X POST "$API_BASE/api/ams-ce/hosts/csv/import" \
        -H "X-User-Token: $TOKEN" \
        -F "file=@$CSV_FILE" \
        -F "node_ids=$node_id")
    
    echo "Response: $response"
    echo ""
}

# 测试3: 导入并挂载到多个节点
test_multiple_nodes_import() {
    local node_ids=$1
    echo "=== 测试3: 导入并挂载到多个节点 (IDs: $node_ids) ==="
    
    response=$(curl -s -X POST "$API_BASE/api/ams-ce/hosts/csv/import" \
        -H "X-User-Token: $TOKEN" \
        -F "file=@$CSV_FILE" \
        -F "node_ids=$node_ids")
    
    echo "Response: $response"
    echo ""
}

# 测试4: 错误处理 - 无效节点ID
test_invalid_node_id() {
    echo "=== 测试4: 错误处理 - 无效节点ID ==="
    
    response=$(curl -s -X POST "$API_BASE/api/ams-ce/hosts/csv/import" \
        -H "X-User-Token: $TOKEN" \
        -F "file=@$CSV_FILE" \
        -F "node_ids=invalid_id")
    
    echo "Response: $response"
    echo ""
}

# 测试5: 错误处理 - 不存在的节点ID
test_nonexistent_node_id() {
    echo "=== 测试5: 错误处理 - 不存在的节点ID ==="
    
    response=$(curl -s -X POST "$API_BASE/api/ams-ce/hosts/csv/import" \
        -H "X-User-Token: $TOKEN" \
        -F "file=@$CSV_FILE" \
        -F "node_ids=999999")
    
    echo "Response: $response"
    echo ""
}

# 获取可用的节点列表
get_available_nodes() {
    echo "=== 获取可用的节点列表 ==="
    
    response=$(curl -s -X GET "$API_BASE/api/rdb/nodes?inner=0" \
        -H "X-User-Token: $TOKEN")
    
    echo "Available nodes: $response"
    echo ""
}

# 主函数
main() {
    echo "CSV批量导入主机并挂载到节点的功能测试"
    echo "========================================"
    echo ""
    
    # 检查参数
    if [ "$#" -lt 1 ]; then
        echo "Usage: $0 <token> [node_id1] [node_id2,node_id3]"
        echo ""
        echo "Examples:"
        echo "  $0 your_token_here"
        echo "  $0 your_token_here 123"
        echo "  $0 your_token_here 123 \"456,789\""
        echo ""
        exit 1
    fi
    
    TOKEN=$1
    NODE_ID1=${2:-""}
    NODE_IDS_MULTIPLE=${3:-""}
    
    # 创建测试文件
    create_test_csv
    echo ""
    
    # 获取可用节点
    get_available_nodes
    
    # 执行测试
    test_traditional_import
    
    if [ -n "$NODE_ID1" ]; then
        test_single_node_import "$NODE_ID1"
    fi
    
    if [ -n "$NODE_IDS_MULTIPLE" ]; then
        test_multiple_nodes_import "$NODE_IDS_MULTIPLE"
    fi
    
    # 错误处理测试
    test_invalid_node_id
    test_nonexistent_node_id
    
    # 清理测试文件
    echo "Cleaning up test file: $CSV_FILE"
    rm -f $CSV_FILE
    
    echo "测试完成！"
}

# 运行主函数
main "$@"
