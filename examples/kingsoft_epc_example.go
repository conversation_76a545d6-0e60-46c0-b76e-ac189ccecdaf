package main

import (
	"fmt"
	"log"
	"os"

	"arboris/src/modules/ams/cloud"
)

func main() {
	fmt.Println("金山云EPC裸金属服务器发现示例")
	fmt.Println("============================")
	fmt.Println("注意：请在配置文件 etc/cloud-config.yml 中配置金山云的AK/SK")
	fmt.Println()

	// 创建金山云提供商（从配置文件读取认证信息）
	provider, err := cloud.NewKingsoftProvider()
	if err != nil {
		fmt.Printf("创建金山云提供商失败: %v\n", err)
		fmt.Println()
		fmt.Println("请检查配置文件 etc/cloud-config.yml 中的金山云配置:")
		fmt.Println("providers:")
		fmt.Println("  kingsoft:")
		fmt.Println("    credentials:")
		fmt.Println("      access_key: \"your_access_key\"")
		fmt.Println("      secret_key: \"your_secret_key\"")
		fmt.Println("      default_region: \"cn-beijing-6\"")
		return
	}

	// 测试连接
	fmt.Println("测试金山云连接...")
	if err := provider.TestConnection(); err != nil {
		log.Printf("金山云连接测试失败: %v", err)
		return
	}
	fmt.Println("金山云连接测试成功!")

	// 发现EPC裸金属服务器实例
	fmt.Println("\n发现金山云EPC裸金属服务器实例...")
	filters := map[string]string{
		// 基本过滤器
		// "zone": "cn-beijing-6a",
		// "name_like": "web",
		// "host_ids": "host-123,host-456",
		
		// 高级过滤器
		// "project_ids": "project-123,project-456",
		// "max_results": "50",
	}

	resources, err := provider.DiscoverResources("epc", filters)
	if err != nil {
		log.Printf("发现金山云EPC实例失败: %v", err)
		return
	}

	fmt.Printf("发现 %d 个金山云EPC裸金属服务器实例:\n", len(resources))
	for i, resource := range resources {
		fmt.Printf("\n实例 %d:\n", i+1)
		printEPCResource(resource)
		
		// 如果只有少量实例，可以测试获取详情
		if i < 2 {
			fmt.Printf("  获取实例详情...\n")
			detail, err := provider.GetResourceDetail("epc", resource.CloudID)
			if err != nil {
				fmt.Printf("  获取详情失败: %v\n", err)
			} else {
				fmt.Printf("  详情获取成功，状态: %s\n", detail.Status)
			}
		}
	}

	// 测试同步状态
	if len(resources) > 0 {
		fmt.Println("\n测试同步资源状态...")
		err := provider.SyncResourceStatus(resources[:1]) // 只同步第一个资源
		if err != nil {
			log.Printf("同步资源状态失败: %v", err)
		} else {
			fmt.Println("资源状态同步成功!")
		}
	}

	// 演示支持的资源类型
	fmt.Println("\n金山云支持的资源类型:")
	supportedTypes := provider.GetSupportedResourceTypes()
	for i, resourceType := range supportedTypes {
		fmt.Printf("  %d. %s", i+1, resourceType)
		switch resourceType {
		case "ecs":
			fmt.Printf(" - 云服务器")
		case "epc":
			fmt.Printf(" - 裸金属服务器 ✨ 新增支持")
		case "rds":
			fmt.Printf(" - 云数据库")
		case "redis":
			fmt.Printf(" - 云缓存")
		case "slb":
			fmt.Printf(" - 负载均衡")
		}
		fmt.Println()
	}

	// 演示EPC特有的过滤器
	fmt.Println("\nEPC特有的过滤器示例:")
	fmt.Println("1. 按主机ID过滤:")
	fmt.Println("   filters[\"host_ids\"] = \"host-123,host-456\"")
	fmt.Println("2. 按项目ID过滤:")
	fmt.Println("   filters[\"project_ids\"] = \"project-123,project-456\"")
	fmt.Println("3. 按可用区过滤:")
	fmt.Println("   filters[\"zone\"] = \"cn-beijing-6a\"")
	fmt.Println("4. 按名称模糊匹配:")
	fmt.Println("   filters[\"name_like\"] = \"web\"")
	fmt.Println("5. 限制返回数量:")
	fmt.Println("   filters[\"max_results\"] = \"50\"")
}

func printEPCResource(resource cloud.CloudResource) {
	fmt.Printf("  ID: %s\n", resource.CloudID)
	fmt.Printf("  名称: %s\n", resource.Name)
	fmt.Printf("  类型: %s (裸金属服务器)\n", resource.Type)
	fmt.Printf("  区域: %s\n", resource.Region)
	fmt.Printf("  可用区: %s\n", resource.Zone)
	fmt.Printf("  状态: %s\n", resource.Status)
	
	if resource.SpecInfo != nil {
		fmt.Printf("  规格信息:\n")
		if hostType, ok := resource.SpecInfo["host_type"]; ok {
			fmt.Printf("    主机类型: %v\n", hostType)
		}
		if productType, ok := resource.SpecInfo["product_type"]; ok {
			fmt.Printf("    产品类型: %v\n", productType)
		}
		if cpu, ok := resource.SpecInfo["cpu"]; ok {
			fmt.Printf("    CPU: %v 核\n", cpu)
		}
		if memory, ok := resource.SpecInfo["memory"]; ok {
			fmt.Printf("    内存: %v MB\n", memory)
		}
		if disk, ok := resource.SpecInfo["disk"]; ok {
			fmt.Printf("    磁盘: %v\n", disk)
		}
		if raid, ok := resource.SpecInfo["raid"]; ok {
			fmt.Printf("    RAID: %v\n", raid)
		}
		if osName, ok := resource.SpecInfo["os_name"]; ok {
			fmt.Printf("    操作系统: %v\n", osName)
		}
		if sn, ok := resource.SpecInfo["sn"]; ok {
			fmt.Printf("    序列号: %v\n", sn)
		}
	}
	
	if resource.NetworkInfo != nil {
		fmt.Printf("  网络信息:\n")
		if privateIP, ok := resource.NetworkInfo["private_ip"]; ok && privateIP != "" {
			fmt.Printf("    私网IP: %v\n", privateIP)
		}
		if publicIP, ok := resource.NetworkInfo["public_ip"]; ok && publicIP != "" {
			fmt.Printf("    公网IP: %v\n", publicIP)
		}
		if privateIPs, ok := resource.NetworkInfo["private_ips"].([]string); ok && len(privateIPs) > 0 {
			fmt.Printf("    所有私网IP: %v\n", privateIPs)
		}
		if subnetId, ok := resource.NetworkInfo["subnet_id"]; ok && subnetId != "" {
			fmt.Printf("    子网ID: %v\n", subnetId)
		}
	}
	
	if len(resource.Tags) > 0 {
		fmt.Printf("  标签:\n")
		for key, value := range resource.Tags {
			fmt.Printf("    %s: %s\n", key, value)
		}
	}
	
	if !resource.CreatedTime.IsZero() {
		fmt.Printf("  创建时间: %s\n", resource.CreatedTime.Format("2006-01-02 15:04:05"))
	}
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
