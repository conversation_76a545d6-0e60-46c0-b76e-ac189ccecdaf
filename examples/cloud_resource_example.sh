#!/bin/bash

# 云资源管理功能使用示例
# 请确保AMS服务已启动，并且已经执行了数据库迁移

BASE_URL="http://localhost:8080/api/ams-ce"
TOKEN="your_user_token_here"  # 请替换为实际的用户token

echo "=== 云资源管理功能测试 ==="

# 1. 创建金山云配置
echo "1. 创建金山云配置..."
curl -X POST "${BASE_URL}/cloud/configs" \
  -H "Content-Type: application/json" \
  -H "X-User-Token: ${TOKEN}" \
  -d '{
    "name": "测试金山云配置",
    "provider": "kingsoft",
    "region": "cn-beijing-6",
    "access_key": "test_access_key",
    "secret_key": "test_secret_key",
    "description": "测试用金山云配置"
  }' | jq .

echo -e "\n"

# 2. 获取云厂商配置列表
echo "2. 获取云厂商配置列表..."
curl -X GET "${BASE_URL}/cloud/configs" \
  -H "X-User-Token: ${TOKEN}" | jq .

echo -e "\n"

# 3. 测试云厂商连接
echo "3. 测试云厂商连接..."
curl -X POST "${BASE_URL}/cloud/configs/1/test" \
  -H "X-User-Token: ${TOKEN}" | jq .

echo -e "\n"

# 4. 发现云资源
echo "4. 发现云资源..."
curl -X POST "${BASE_URL}/cloud/discover" \
  -H "Content-Type: application/json" \
  -H "X-User-Token: ${TOKEN}" \
  -d '{
    "config_id": 1,
    "resource_types": ["ecs", "rds"],
    "filters": {
      "zone": "cn-beijing-6a",
      "status": ["running"],
      "name_like": ""
    }
  }' | jq .

echo -e "\n"

# 等待发现完成
echo "等待资源发现完成..."
sleep 3

# 5. 获取发现记录列表
echo "5. 获取发现记录列表..."
curl -X GET "${BASE_URL}/cloud/discoveries" \
  -H "X-User-Token: ${TOKEN}" | jq .

echo -e "\n"

# 6. 获取发现的资源列表
echo "6. 获取发现的资源列表..."
curl -X GET "${BASE_URL}/cloud/discoveries/1/resources?limit=10&offset=0" \
  -H "X-User-Token: ${TOKEN}" | jq .

echo -e "\n"

# 7. 选择要导入的资源
echo "7. 选择要导入的资源..."
curl -X PUT "${BASE_URL}/cloud/discoveries/1/resources/select" \
  -H "Content-Type: application/json" \
  -H "X-User-Token: ${TOKEN}" \
  -d '{
    "resource_ids": [1, 2],
    "selected": true
  }' | jq .

echo -e "\n"

# 8. 创建火山云配置
echo "8. 创建火山云配置..."
curl -X POST "${BASE_URL}/cloud/configs" \
  -H "Content-Type: application/json" \
  -H "X-User-Token: ${TOKEN}" \
  -d '{
    "name": "测试火山云配置",
    "provider": "volcano",
    "region": "cn-beijing",
    "access_key": "test_access_key",
    "secret_key": "test_secret_key",
    "description": "测试用火山云配置"
  }' | jq .

echo -e "\n"

# 9. 发现火山云资源
echo "9. 发现火山云资源..."
curl -X POST "${BASE_URL}/cloud/discover" \
  -H "Content-Type: application/json" \
  -H "X-User-Token: ${TOKEN}" \
  -d '{
    "config_id": 2,
    "resource_types": ["ecs", "redis"],
    "filters": {
      "zone": "cn-beijing-a",
      "status": ["running"],
      "name_like": "volcano"
    }
  }' | jq .

echo -e "\n"

# 10. 获取云资源列表
echo "10. 获取云资源列表..."
curl -X GET "${BASE_URL}/cloud/resources?limit=10&offset=0" \
  -H "X-User-Token: ${TOKEN}" | jq .

echo -e "\n"

# 11. 创建阿里云配置
echo "11. 创建阿里云配置..."
curl -X POST "${BASE_URL}/cloud/configs" \
  -H "Content-Type: application/json" \
  -H "X-User-Token: ${TOKEN}" \
  -d '{
    "name": "测试阿里云配置",
    "provider": "aliyun",
    "region": "cn-hangzhou",
    "access_key": "test_access_key",
    "secret_key": "test_secret_key",
    "description": "测试用阿里云配置"
  }' | jq .

echo -e "\n"

# 12. 创建腾讯云配置
echo "12. 创建腾讯云配置..."
curl -X POST "${BASE_URL}/cloud/configs" \
  -H "Content-Type: application/json" \
  -H "X-User-Token: ${TOKEN}" \
  -d '{
    "name": "测试腾讯云配置",
    "provider": "tencent",
    "region": "ap-beijing",
    "access_key": "test_access_key",
    "secret_key": "test_secret_key",
    "description": "测试用腾讯云配置"
  }' | jq .

echo -e "\n"

# 13. 创建天翼云配置
echo "13. 创建天翼云配置..."
curl -X POST "${BASE_URL}/cloud/configs" \
  -H "Content-Type: application/json" \
  -H "X-User-Token: ${TOKEN}" \
  -d '{
    "name": "测试天翼云配置",
    "provider": "ctyun",
    "region": "cn-bj",
    "access_key": "test_access_key",
    "secret_key": "test_secret_key",
    "description": "测试用天翼云配置"
  }' | jq .

echo -e "\n"

# 14. 发现阿里云资源
echo "14. 发现阿里云资源..."
curl -X POST "${BASE_URL}/cloud/discover" \
  -H "Content-Type: application/json" \
  -H "X-User-Token: ${TOKEN}" \
  -d '{
    "config_id": 3,
    "resource_types": ["ecs", "oss"],
    "filters": {
      "zone": "cn-hangzhou-b",
      "status": ["running"],
      "name_like": "aliyun"
    }
  }' | jq .

echo -e "\n"

# 15. 发现腾讯云资源
echo "15. 发现腾讯云资源..."
curl -X POST "${BASE_URL}/cloud/discover" \
  -H "Content-Type: application/json" \
  -H "X-User-Token: ${TOKEN}" \
  -d '{
    "config_id": 4,
    "resource_types": ["cvm", "cos"],
    "filters": {
      "zone": "ap-beijing-1",
      "status": ["running"],
      "name_like": "tencent"
    }
  }' | jq .

echo -e "\n"

echo "=== 测试完成 ==="
echo "注意："
echo "1. 请替换脚本中的TOKEN为实际的用户token"
echo "2. 确保AMS服务已启动并可访问"
echo "3. 确保已执行数据库迁移脚本"
echo "4. 实际使用时需要配置真实的云厂商密钥"
echo "5. 导入资源需要指定有效的节点ID"
