package main

import (
	"encoding/json"
	"fmt"
	"log"
	"time"

	"arboris/src/models"
	"arboris/src/modules/ams/cloud"
	"arboris/src/modules/ams/duplicate"
)

func main() {
	fmt.Println("重复检测功能演示")
	fmt.Println("==================")

	// 1. 创建重复检测配置
	config := &models.DuplicateDetectionConfig{
		Enabled:                        true,
		PreferPrivateIP:                true,
		FallbackToPublicIP:             true,
		IgnoreEmptyIP:                  true,
		AutoIgnoreSameSource:           false,
		AutoOverrideManualWithDiscovery: false,
	}

	// 2. 创建重复检测服务
	_ = duplicate.NewDuplicateService(config)
	fmt.Printf("✅ 重复检测服务已创建，配置: %+v\n\n", config)

	// 3. 模拟一些云资源数据
	discoveredResources := []cloud.CloudResource{
		{
			CloudID: "volcano-ecs-001",
			Name:    "web-server-01",
			Type:    "ecs",
			Region:  "cn-beijing",
			Zone:    "cn-beijing-a",
			Status:  "running",
			SpecInfo: map[string]interface{}{
				"instance_type": "ecs.g1.large",
				"cpu":          2,
				"memory":       4096,
			},
			NetworkInfo: map[string]interface{}{
				"private_ip": "*********",
				"public_ip":  "*************",
			},
			Tags: map[string]string{
				"Environment": "production",
				"Project":     "web-app",
			},
			RawData: map[string]interface{}{
				"InstanceId":   "volcano-ecs-001",
				"InstanceName": "web-server-01",
				"Status":       "RUNNING",
			},
			CreatedTime: time.Now().Add(-24 * time.Hour),
		},
		{
			CloudID: "volcano-ecs-002",
			Name:    "db-server-01",
			Type:    "ecs",
			Region:  "cn-beijing",
			Zone:    "cn-beijing-b",
			Status:  "running",
			SpecInfo: map[string]interface{}{
				"instance_type": "ecs.c1.xlarge",
				"cpu":          4,
				"memory":       8192,
			},
			NetworkInfo: map[string]interface{}{
				"private_ip": "*********",
				"public_ip":  "",
			},
			Tags: map[string]string{
				"Environment": "production",
				"Project":     "database",
			},
			RawData: map[string]interface{}{
				"InstanceId":   "volcano-ecs-002",
				"InstanceName": "db-server-01",
				"Status":       "RUNNING",
			},
			CreatedTime: time.Now().Add(-12 * time.Hour),
		},
	}

	fmt.Printf("📊 模拟发现了 %d 个云资源:\n", len(discoveredResources))
	for i, resource := range discoveredResources {
		fmt.Printf("  %d. %s (%s) - IP: %v\n", 
			i+1, 
			resource.Name, 
			resource.CloudID, 
			resource.NetworkInfo["private_ip"])
	}
	fmt.Println()

	// 4. 演示重复检测逻辑
	fmt.Println("🔍 开始重复检测...")
	detector := duplicate.NewDuplicateDetector(config)

	// 模拟检测主机重复
	fmt.Println("检测主机重复...")
	err := detector.DetectHostDuplicates(discoveredResources)
	if err != nil {
		log.Printf("主机重复检测失败: %v", err)
	} else {
		fmt.Println("✅ 主机重复检测完成")
	}

	// 模拟检测资源重复
	fmt.Println("检测资源重复...")
	err = detector.DetectResourceDuplicates(discoveredResources)
	if err != nil {
		log.Printf("资源重复检测失败: %v", err)
	} else {
		fmt.Println("✅ 资源重复检测完成")
	}
	fmt.Println()

	// 5. 演示API请求结构
	fmt.Println("📝 API请求示例:")
	fmt.Println()

	// 解决重复请求示例
	resolveReq := models.ResolveDuplicateRequest{
		DeviceType: models.DeviceTypeHost,
		DeviceID:   1,
		Action:     models.ActionOverride,
		KeepFields: []string{"name", "note"},
	}
	
	reqJSON, _ := json.MarshalIndent(resolveReq, "", "  ")
	fmt.Printf("POST /api/ams-ce/devices/duplicates/resolve\n%s\n\n", string(reqJSON))

	// 批量解决重复请求示例
	batchReq := models.BatchResolveDuplicatesRequest{
		Operations: []models.ResolveDuplicateRequest{
			{
				DeviceType: models.DeviceTypeHost,
				DeviceID:   1,
				Action:     models.ActionOverride,
			},
			{
				DeviceType: models.DeviceTypeResource,
				DeviceID:   2,
				Action:     models.ActionIgnore,
			},
		},
	}
	
	batchJSON, _ := json.MarshalIndent(batchReq, "", "  ")
	fmt.Printf("POST /api/ams-ce/devices/duplicates/batch-resolve\n%s\n\n", string(batchJSON))

	// 6. 演示重复信息结构
	fmt.Println("📋 重复信息结构示例:")
	duplicateInfo := models.DuplicateInfo{
		ConflictIP:       "*********",
		ExistingDeviceID: 123,
		ExistingSource:   models.DataSourceManual,
		DiscoveredData: map[string]interface{}{
			"name":   "web-server-01",
			"status": "running",
			"cpu":    2,
			"memory": 4096,
		},
		DetectedAt: time.Now(),
		Differences: []models.FieldDifference{
			{
				Field:           "name",
				ExistingValue:   "web-server-old",
				DiscoveredValue: "web-server-01",
				IsConflict:      true,
			},
			{
				Field:           "cpu",
				ExistingValue:   1,
				DiscoveredValue: 2,
				IsConflict:      true,
			},
		},
	}
	
	infoJSON, _ := json.MarshalIndent(duplicateInfo, "", "  ")
	fmt.Printf("%s\n\n", string(infoJSON))

	// 7. 演示配置选项
	fmt.Println("⚙️  配置选项说明:")
	fmt.Println("- enabled: 是否启用重复检测")
	fmt.Println("- prefer_private_ip: 优先使用私网IP作为唯一标识")
	fmt.Println("- fallback_to_public_ip: 私网IP不存在时使用公网IP")
	fmt.Println("- ignore_empty_ip: 忽略没有IP的设备")
	fmt.Println("- auto_ignore_same_source: 相同数据源自动忽略")
	fmt.Println("- auto_override_manual_with_discovery: 自动用发现数据覆盖手动数据")
	fmt.Println()

	// 8. 演示API端点
	fmt.Println("🌐 API端点列表:")
	fmt.Println("GET    /api/ams-ce/devices/duplicates                    - 获取重复设备列表")
	fmt.Println("POST   /api/ams-ce/devices/duplicates/resolve            - 解决重复设备")
	fmt.Println("POST   /api/ams-ce/devices/duplicates/batch-resolve      - 批量解决重复设备")
	fmt.Println("GET    /api/ams-ce/devices/duplicates/:type/:id          - 获取重复设备详情")
	fmt.Println("GET    /api/ams-ce/devices/duplicates/stats              - 获取重复统计信息")
	fmt.Println()

	// 9. 演示查询参数
	fmt.Println("🔍 查询参数示例:")
	fmt.Println("GET /api/ams-ce/devices/duplicates?device_type=host&limit=20&offset=0")
	fmt.Println("GET /api/ams-ce/devices/duplicates?device_type=resource&limit=50&offset=100")
	fmt.Println()

	fmt.Println("✨ 重复检测功能演示完成！")
	fmt.Println()
	fmt.Println("📚 使用说明:")
	fmt.Println("1. 运行数据库迁移脚本: sql/duplicate_detection_migration.sql")
	fmt.Println("2. 配置重复检测参数")
	fmt.Println("3. 在云资源发现时启用重复检测")
	fmt.Println("4. 通过API管理重复设备")
}

// 演示如何使用重复检测服务
func demonstrateService() {
	fmt.Println("\n🔧 服务使用演示:")
	
	config := &models.DuplicateDetectionConfig{
		Enabled:         true,
		PreferPrivateIP: true,
	}
	
	service := duplicate.NewDuplicateService(config)
	
	// 获取重复设备列表
	response, err := service.GetDuplicateDevices(models.DeviceTypeHost, 10, 0)
	if err != nil {
		fmt.Printf("❌ 获取重复设备失败: %v\n", err)
		return
	}
	
	fmt.Printf("📊 找到 %d 个重复设备\n", response.Total)
	
	// 解决重复设备
	if len(response.Duplicates) > 0 {
		req := &models.ResolveDuplicateRequest{
			DeviceType: models.DeviceTypeHost,
			DeviceID:   response.Duplicates[0].Id,
			Action:     models.ActionIgnore,
		}
		
		result, err := service.ResolveDuplicate(req, "admin")
		if err != nil {
			fmt.Printf("❌ 解决重复失败: %v\n", err)
		} else {
			fmt.Printf("✅ 重复解决结果: %s\n", result.Message)
		}
	}
}
