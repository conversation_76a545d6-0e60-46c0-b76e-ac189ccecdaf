package main

import (
	"fmt"
	"log"
	"os"

	"arboris/src/modules/ams/cloud"
)

func main() {
	fmt.Println("火山云ECS实例发现示例")
	fmt.Println("====================")
	fmt.Println("注意：请在配置文件 etc/cloud-config.yml 中配置火山云的AK/SK")
	fmt.Println()

	// 创建火山云提供商（从配置文件读取认证信息）
	provider, err := cloud.NewVolcanoProvider()
	if err != nil {
		fmt.Printf("创建火山云提供商失败: %v\n", err)
		fmt.Println()
		fmt.Println("请检查配置文件 etc/cloud-config.yml 中的火山云配置:")
		fmt.Println("providers:")
		fmt.Println("  volcano:")
		fmt.Println("    credentials:")
		fmt.Println("      access_key: \"your_access_key\"")
		fmt.Println("      secret_key: \"your_secret_key\"")
		fmt.Println("      default_region: \"cn-beijing\"")
		return
	}

	// 测试连接
	fmt.Println("测试火山云连接...")
	if err := provider.TestConnection(); err != nil {
		log.Printf("火山云连接测试失败: %v", err)
		return
	}
	fmt.Println("火山云连接测试成功!")

	// 发现ECS实例
	fmt.Println("\n发现火山云ECS实例...")
	filters := map[string]string{
		// 基本过滤器
		// "status": "running",
		// "zone": "cn-beijing-a",
		// "name_like": "web",
		// "instance_name": "exact-name",

		// 高级过滤器
		// "instance_ids": "i-123,i-456",
		// "vpc_ids": "vpc-123,vpc-456",
		// "subnet_ids": "subnet-123,subnet-456",
		// "instance_types": "ecs.g1.large,ecs.c1.xlarge",
		// "tags": "Environment:production,Project:web-app",

		// 分页和排序
		// "max_results": "50",
		// "sort": "name:asc",  // 可选: name:asc, name:desc, created_time:asc, created_time:desc, status:asc, zone:asc
	}

	resources, err := provider.DiscoverResources("ecs", filters)
	if err != nil {
		log.Printf("发现火山云ECS实例失败: %v", err)
		return
	}

	fmt.Printf("发现 %d 个火山云ECS实例:\n", len(resources))
	for i, resource := range resources {
		fmt.Printf("\n实例 %d:\n", i+1)
		printResource(resource)
		
		// 如果只有少量实例，可以测试获取详情
		if i < 2 {
			fmt.Printf("  获取实例详情...\n")
			detail, err := provider.GetResourceDetail("ecs", resource.CloudID)
			if err != nil {
				fmt.Printf("  获取详情失败: %v\n", err)
			} else {
				fmt.Printf("  详情获取成功，状态: %s\n", detail.Status)
			}
		}
	}

	// 测试同步状态
	if len(resources) > 0 {
		fmt.Println("\n测试同步资源状态...")
		err := provider.SyncResourceStatus(resources[:1]) // 只同步第一个资源
		if err != nil {
			log.Printf("同步资源状态失败: %v", err)
		} else {
			fmt.Println("资源状态同步成功!")
		}
	}

	// 演示高级过滤功能
	fmt.Println("\n演示高级过滤功能...")
	advancedFilters := map[string]string{
		"status":     "running",
		"sort":       "name:asc",
		"max_results": "10",
	}

	advancedResources, err := provider.DiscoverResources("ecs", advancedFilters)
	if err != nil {
		log.Printf("高级过滤失败: %v", err)
	} else {
		fmt.Printf("高级过滤发现 %d 个运行中的实例 (按名称排序):\n", len(advancedResources))
		for i, resource := range advancedResources {
			if i >= 3 { // 只显示前3个
				fmt.Printf("  ... 还有 %d 个实例\n", len(advancedResources)-3)
				break
			}
			fmt.Printf("  %d. %s (%s) - %s\n", i+1, resource.Name, resource.CloudID, resource.Status)
		}
	}
}

func printResource(resource cloud.CloudResource) {
	fmt.Printf("  ID: %s\n", resource.CloudID)
	fmt.Printf("  名称: %s\n", resource.Name)
	fmt.Printf("  类型: %s\n", resource.Type)
	fmt.Printf("  区域: %s\n", resource.Region)
	fmt.Printf("  可用区: %s\n", resource.Zone)
	fmt.Printf("  状态: %s\n", resource.Status)
	
	if resource.SpecInfo != nil {
		fmt.Printf("  规格信息:\n")
		if instanceType, ok := resource.SpecInfo["instance_type"]; ok {
			fmt.Printf("    实例类型: %v\n", instanceType)
		}
		if cpu, ok := resource.SpecInfo["cpu"]; ok {
			fmt.Printf("    CPU: %v 核\n", cpu)
		}
		if memory, ok := resource.SpecInfo["memory"]; ok {
			fmt.Printf("    内存: %v MB\n", memory)
		}
	}
	
	if resource.NetworkInfo != nil {
		fmt.Printf("  网络信息:\n")
		if publicIP, ok := resource.NetworkInfo["public_ip"]; ok && publicIP != "" {
			fmt.Printf("    公网IP: %v\n", publicIP)
		}
		if privateIP, ok := resource.NetworkInfo["private_ip"]; ok && privateIP != "" {
			fmt.Printf("    私网IP: %v\n", privateIP)
		}
	}
	
	if len(resource.Tags) > 0 {
		fmt.Printf("  标签:\n")
		for key, value := range resource.Tags {
			fmt.Printf("    %s: %s\n", key, value)
		}
	}
	
	if !resource.CreatedTime.IsZero() {
		fmt.Printf("  创建时间: %s\n", resource.CreatedTime.Format("2006-01-02 15:04:05"))
	}
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
