package main

import (
	"encoding/json"
	"fmt"
	"time"

	"arboris/src/models"
	"arboris/src/modules/ams/cloud"
)

func main() {
	fmt.Println("🎯 重复检测功能演示 (无数据库版本)")
	fmt.Println("==========================================")

	// 1. 创建重复检测配置
	config := &models.DuplicateDetectionConfig{
		Enabled:                        true,
		PreferPrivateIP:                true,
		FallbackToPublicIP:             true,
		IgnoreEmptyIP:                  true,
		AutoIgnoreSameSource:           false,
		AutoOverrideManualWithDiscovery: false,
	}

	fmt.Printf("✅ 重复检测配置: %+v\n\n", config)

	// 2. 模拟一些云资源数据
	discoveredResources := []cloud.CloudResource{
		{
			CloudID: "volcano-ecs-001",
			Name:    "web-server-01",
			Type:    "ecs",
			Region:  "cn-beijing",
			Zone:    "cn-beijing-a",
			Status:  "running",
			SpecInfo: map[string]interface{}{
				"instance_type": "ecs.g1.large",
				"cpu":          2,
				"memory":       4096,
			},
			NetworkInfo: map[string]interface{}{
				"private_ip": "*********",
				"public_ip":  "*************",
			},
			Tags: map[string]string{
				"Environment": "production",
				"Project":     "web-app",
			},
			RawData: map[string]interface{}{
				"InstanceId":   "volcano-ecs-001",
				"InstanceName": "web-server-01",
				"Status":       "RUNNING",
			},
			CreatedTime: time.Now().Add(-24 * time.Hour),
		},
		{
			CloudID: "volcano-ecs-002",
			Name:    "db-server-01",
			Type:    "ecs",
			Region:  "cn-beijing",
			Zone:    "cn-beijing-b",
			Status:  "running",
			SpecInfo: map[string]interface{}{
				"instance_type": "ecs.c1.xlarge",
				"cpu":          4,
				"memory":       8192,
			},
			NetworkInfo: map[string]interface{}{
				"private_ip": "*********",
				"public_ip":  "",
			},
			Tags: map[string]string{
				"Environment": "production",
				"Project":     "database",
			},
			RawData: map[string]interface{}{
				"InstanceId":   "volcano-ecs-002",
				"InstanceName": "db-server-01",
				"Status":       "RUNNING",
			},
			CreatedTime: time.Now().Add(-12 * time.Hour),
		},
	}

	fmt.Printf("📊 模拟发现了 %d 个云资源:\n", len(discoveredResources))
	for i, resource := range discoveredResources {
		fmt.Printf("  %d. %s (%s)\n", i+1, resource.Name, resource.CloudID)
		fmt.Printf("     IP: %v (私网), %v (公网)\n", 
			resource.NetworkInfo["private_ip"], 
			resource.NetworkInfo["public_ip"])
		fmt.Printf("     规格: %v CPU, %v MB 内存\n", 
			resource.SpecInfo["cpu"], 
			resource.SpecInfo["memory"])
		fmt.Printf("     标签: %v\n", resource.Tags)
		fmt.Println()
	}

	// 3. 演示重复检测逻辑（模拟）
	fmt.Println("🔍 重复检测逻辑演示:")
	fmt.Println("1. 提取主要IP地址 (优先私网IP)")
	fmt.Println("2. 在数据库中查找相同IP的设备")
	fmt.Println("3. 如果找到重复，标记状态并记录详细信息")
	fmt.Println("4. 等待用户选择处理方式")
	fmt.Println()

	// 4. 演示重复信息结构
	fmt.Println("📋 重复信息结构示例:")
	duplicateInfo := models.DuplicateInfo{
		ConflictIP:       "*********",
		ExistingDeviceID: 123,
		ExistingSource:   models.DataSourceManual,
		DiscoveredData: map[string]interface{}{
			"name":   "web-server-01",
			"status": "running",
			"cpu":    2,
			"memory": 4096,
		},
		DetectedAt: time.Now(),
		Differences: []models.FieldDifference{
			{
				Field:           "name",
				ExistingValue:   "web-server-old",
				DiscoveredValue: "web-server-01",
				IsConflict:      true,
			},
			{
				Field:           "cpu",
				ExistingValue:   1,
				DiscoveredValue: 2,
				IsConflict:      true,
			},
			{
				Field:           "memory",
				ExistingValue:   2048,
				DiscoveredValue: 4096,
				IsConflict:      true,
			},
		},
	}

	infoJSON, _ := json.MarshalIndent(duplicateInfo, "", "  ")
	fmt.Printf("%s\n\n", string(infoJSON))

	// 5. 演示API请求结构
	fmt.Println("📝 API请求示例:")
	fmt.Println()

	// 解决重复请求示例
	resolveReq := models.ResolveDuplicateRequest{
		DeviceType: models.DeviceTypeHost,
		DeviceID:   1,
		Action:     models.ActionOverride,
		KeepFields: []string{"name", "note"},
	}

	reqJSON, _ := json.MarshalIndent(resolveReq, "", "  ")
	fmt.Printf("POST /api/ams-ce/devices/duplicates/resolve\n%s\n\n", string(reqJSON))

	// 批量解决重复请求示例
	batchReq := models.BatchResolveDuplicatesRequest{
		Operations: []models.ResolveDuplicateRequest{
			{
				DeviceType: models.DeviceTypeHost,
				DeviceID:   1,
				Action:     models.ActionOverride,
			},
			{
				DeviceType: models.DeviceTypeResource,
				DeviceID:   2,
				Action:     models.ActionIgnore,
			},
		},
	}

	batchJSON, _ := json.MarshalIndent(batchReq, "", "  ")
	fmt.Printf("POST /api/ams-ce/devices/duplicates/batch-resolve\n%s\n\n", string(batchJSON))

	// 6. 演示处理流程
	fmt.Println("🔄 处理流程演示:")
	fmt.Println("场景: 手动添加的服务器与自动发现的服务器重复")
	fmt.Println()
	
	fmt.Println("1️⃣ 现有设备 (手动添加):")
	fmt.Println("   - ID: 123")
	fmt.Println("   - 名称: web-server-old")
	fmt.Println("   - IP: *********")
	fmt.Println("   - CPU: 1 核")
	fmt.Println("   - 内存: 2048 MB")
	fmt.Println("   - 来源: MANUAL")
	fmt.Println()

	fmt.Println("2️⃣ 发现设备 (自动发现):")
	fmt.Println("   - 云ID: volcano-ecs-001")
	fmt.Println("   - 名称: web-server-01")
	fmt.Println("   - IP: ********* (冲突!)")
	fmt.Println("   - CPU: 2 核")
	fmt.Println("   - 内存: 4096 MB")
	fmt.Println("   - 来源: AUTO_DISCOVERY")
	fmt.Println()

	fmt.Println("3️⃣ 系统检测到重复:")
	fmt.Println("   - 冲突IP: *********")
	fmt.Println("   - 标记状态: DETECTED")
	fmt.Println("   - 记录差异: 名称、CPU、内存不同")
	fmt.Println()

	fmt.Println("4️⃣ 用户处理选项:")
	fmt.Println("   A. 覆盖 (OVERRIDE): 用自动发现的数据更新现有记录")
	fmt.Println("      结果: 名称→web-server-01, CPU→2核, 内存→4096MB")
	fmt.Println("   B. 忽略 (IGNORE): 保持现有数据，忽略自动发现")
	fmt.Println("      结果: 保持原有数据不变")
	fmt.Println("   C. 部分覆盖: 选择保留某些字段")
	fmt.Println("      结果: 保留名称，更新CPU和内存")
	fmt.Println()

	// 7. 演示API端点
	fmt.Println("🌐 API端点列表:")
	fmt.Println("GET    /api/ams-ce/devices/duplicates                    - 获取重复设备列表")
	fmt.Println("POST   /api/ams-ce/devices/duplicates/resolve            - 解决重复设备")
	fmt.Println("POST   /api/ams-ce/devices/duplicates/batch-resolve      - 批量解决重复设备")
	fmt.Println("GET    /api/ams-ce/devices/duplicates/:type/:id          - 获取重复设备详情")
	fmt.Println("GET    /api/ams-ce/devices/duplicates/stats              - 获取重复统计信息")
	fmt.Println()

	// 8. 演示查询参数
	fmt.Println("🔍 查询参数示例:")
	fmt.Println("GET /api/ams-ce/devices/duplicates?device_type=host&limit=20&offset=0")
	fmt.Println("GET /api/ams-ce/devices/duplicates?device_type=resource&limit=50&offset=100")
	fmt.Println()

	// 9. 配置选项说明
	fmt.Println("⚙️  配置选项说明:")
	fmt.Println("- enabled: 是否启用重复检测")
	fmt.Println("- prefer_private_ip: 优先使用私网IP作为唯一标识")
	fmt.Println("- fallback_to_public_ip: 私网IP不存在时使用公网IP")
	fmt.Println("- ignore_empty_ip: 忽略没有IP的设备")
	fmt.Println("- auto_ignore_same_source: 相同数据源自动忽略")
	fmt.Println("- auto_override_manual_with_discovery: 自动用发现数据覆盖手动数据")
	fmt.Println()

	fmt.Println("✨ 重复检测功能演示完成！")
	fmt.Println()
	fmt.Println("📚 部署步骤:")
	fmt.Println("1. 运行数据库迁移脚本: sql/duplicate_detection_migration.sql")
	fmt.Println("2. 配置重复检测参数: etc/cloud-config-example.yml")
	fmt.Println("3. 启动AMS服务")
	fmt.Println("4. 在云资源发现时启用重复检测")
	fmt.Println("5. 通过API管理重复设备")
	fmt.Println()
	fmt.Println("🎯 核心优势:")
	fmt.Println("- 简单有效: 以IP地址为唯一标识，易于理解")
	fmt.Println("- 用户决策: 不自动处理，由用户选择处理方式")
	fmt.Println("- 批量操作: 支持批量处理，提高效率")
	fmt.Println("- 详细对比: 显示字段差异，便于决策")
	fmt.Println("- 历史记录: 记录所有操作，便于审计")
}
