# 云资源分层导入 API 使用指南

## 🎯 快速开始

### 完整导入流程（5步完成）

#### 1️⃣ 配置云厂商
```bash
curl -X POST http://localhost:8080/api/ams-ce/cloud/configs \
  -H "Content-Type: application/json" \
  -d '{
    "name": "阿里云生产环境",
    "provider": "aliyun",
    "region": "cn-beijing",
    "access_key": "your_access_key",
    "secret_key": "your_secret_key"
  }'
```

#### 2️⃣ 发现云资源
```bash
curl -X POST http://localhost:8080/api/ams-ce/cloud/discover \
  -H "Content-Type: application/json" \
  -d '{
    "config_id": 1,
    "resource_types": ["ecs", "rds", "redis"]
  }'
```

#### 3️⃣ 查看发现结果（含重复检测）
```bash
curl -X GET "http://localhost:8080/api/ams-ce/cloud/discoveries/1/resources/duplicates"
```

#### 4️⃣ 选择要导入的资源
```bash
curl -X PUT http://localhost:8080/api/ams-ce/cloud/discoveries/1/resources/select \
  -H "Content-Type: application/json" \
  -d '{"resource_ids": [1, 2, 3]}'
```

#### 5️⃣ 导入资源（处理重复）
```bash
curl -X POST http://localhost:8080/api/ams-ce/cloud/discoveries/1/import \
  -H "Content-Type: application/json" \
  -d '{
    "node_id": 123,
    "resource_mapping": {
      "1": {"cate": "server", "tenant": "prod", "note": "Web服务器"},
      "2": {"cate": "database", "tenant": "prod", "note": "MySQL数据库"}
    },
    "duplicate_actions": {
      "1": "OVERRIDE",
      "2": "IGNORE"
    }
  }'
```

## 📋 API接口详细说明

### 云厂商配置管理

#### 创建配置
```
POST /api/ams-ce/cloud/configs
```
**请求体**:
```json
{
  "name": "配置名称",
  "provider": "aliyun|tencent|ctyun|kingsoft|volcano",
  "region": "地域",
  "access_key": "访问密钥",
  "secret_key": "密钥",
  "description": "描述"
}
```

#### 获取配置列表
```
GET /api/ams-ce/cloud/configs
```

#### 测试连接
```
POST /api/ams-ce/cloud/configs/{id}/test
```

### 云资源发现

#### 启动发现
```
POST /api/ams-ce/cloud/discover
```
**请求体**:
```json
{
  "config_id": 1,
  "resource_types": ["ecs", "rds", "redis"],
  "filters": {
    "zone": "cn-beijing-a",
    "status": ["running"]
  }
}
```

#### 获取发现记录
```
GET /api/ams-ce/cloud/discoveries
GET /api/ams-ce/cloud/discoveries/{id}
```

### 资源预览和选择

#### 获取资源列表（含重复信息）
```
GET /api/ams-ce/cloud/discoveries/{id}/resources/duplicates
```
**查询参数**:
- `limit`: 每页数量（默认20，最大100）
- `offset`: 偏移量（默认0）

**响应结构**:
```json
{
  "dat": {
    "list": [
      {
        "id": 1,
        "cloud_id": "云端资源ID",
        "name": "资源名称",
        "resource_type": "资源类型",
        "has_duplicate": true,
        "duplicate_status": "DETECTED|IGNORED|OVERRIDDEN",
        "conflict_ip": "冲突IP",
        "duplicate_info": {
          "existing_host": {...},
          "differences": [...]
        }
      }
    ],
    "total": 总数,
    "duplicate_stats": {
      "total_resources": 总资源数,
      "duplicate_resources": 重复资源数,
      "resolved_duplicates": 已解决重复数,
      "pending_duplicates": 待处理重复数
    }
  }
}
```

#### 选择资源
```
PUT /api/ams-ce/cloud/discoveries/{id}/resources/select
```
**请求体**:
```json
{
  "resource_ids": [1, 2, 3, 4, 5]
}
```

### 资源导入

#### 导入选中资源
```
POST /api/ams-ce/cloud/discoveries/{id}/import
```
**请求体**:
```json
{
  "node_id": 123,
  "resource_mapping": {
    "资源ID": {
      "cate": "资源分类",
      "tenant": "租户",
      "note": "备注"
    }
  },
  "duplicate_actions": {
    "资源ID": "IGNORE|OVERRIDE|SKIP"
  }
}
```

## 🔄 重复处理策略

### IGNORE（忽略重复）
- **行为**: 保留现有资源，创建新资源
- **场景**: 确实需要两个相同IP的资源
- **结果**: 系统中有两个资源记录

### OVERRIDE（覆盖现有）
- **行为**: 用新数据更新现有资源
- **场景**: 现有数据过时，需要更新
- **结果**: 现有资源被更新

### SKIP（跳过导入）
- **行为**: 不导入该资源
- **场景**: 现有数据正确，不需要更新
- **结果**: 该资源不会被导入

## 🎯 最佳实践

### 1. 分批导入
对于大量资源，建议分批导入：
```bash
# 第一批：选择前10个资源
curl -X PUT .../resources/select -d '{"resource_ids": [1,2,3,4,5,6,7,8,9,10]}'
curl -X POST .../import -d '{...}'

# 第二批：选择接下来的10个资源
curl -X PUT .../resources/select -d '{"resource_ids": [11,12,13,14,15,16,17,18,19,20]}'
curl -X POST .../import -d '{...}'
```

### 2. 重复检测处理
- 先查看重复信息，了解冲突详情
- 根据业务需求选择合适的处理策略
- 对于重要资源，建议使用OVERRIDE策略

### 3. 错误处理
- 导入失败时，系统会自动清理已创建的记录
- 可以重新选择资源并再次导入
- 查看日志了解具体错误原因

## 🔧 故障排除

### 常见问题

1. **发现失败**
   - 检查云厂商配置是否正确
   - 确认访问密钥有足够权限
   - 检查网络连接

2. **重复检测不准确**
   - 主要基于IP地址检测
   - 可能存在IP地址变更的情况
   - 建议人工确认重复信息

3. **导入失败**
   - 检查节点是否存在
   - 确认资源映射配置正确
   - 查看系统日志获取详细错误信息

### 调试命令

```bash
# 查看发现状态
curl -X GET http://localhost:8080/api/ams-ce/cloud/discoveries/{id}

# 查看节点信息
curl -X GET http://localhost:8080/api/rdb-ce/nodes/{node_id}

# 查看导入后的资源
curl -X GET http://localhost:8080/api/rdb-ce/nodes/{node_id}/resources
```
