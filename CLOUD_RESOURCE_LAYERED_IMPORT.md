# 云资源分层导入和重复检测功能

## 🎯 功能概述

本功能实现了云资源的分层导入和重复检测机制，支持将不同类型的云资源先存储到专门的表中，然后同步到统一的resource表，同时提供完整的重复检测和处理功能。

## 🏗️ 架构设计

### 分层存储架构

```
云资源发现 → cloud_resource_temp (临时表)
    ↓
重复检测 → 标记重复状态
    ↓
用户选择处理方式
    ↓
分层导入:
├── ECS/VM → host → resource
├── RDS → cloud_database → resource  
├── Redis → cloud_cache → resource
├── 负载均衡 → cloud_loadbalancer → resource
├── 存储 → cloud_storage → resource
├── 网络 → cloud_network → resource
└── 其他 → cloud_resource_generic → resource
    ↓
绑定到节点 → node_resource
    ↓
JumpServer同步
```

## 📊 数据表结构

### 新增的云资源专门表

1. **cloud_database** - 云数据库资源
2. **cloud_cache** - 云缓存资源  
3. **cloud_loadbalancer** - 云负载均衡资源
4. **cloud_storage** - 云存储资源
5. **cloud_network** - 云网络资源
6. **cloud_resource_generic** - 通用云资源

### 扩展的现有表

1. **host** - 添加云资源相关字段
2. **cloud_resource_temp** - 添加重复检测字段

## 🔍 重复检测机制

### 检测逻辑

- **主机资源**: 通过IP地址检测重复（优先私网IP，备用公网IP）
- **数据库资源**: 通过cloud_id和cloud_provider检测重复
- **缓存资源**: 通过cloud_id和cloud_provider检测重复
- **其他资源**: 根据资源类型采用相应的检测策略

### 处理方式

- **IGNORE**: 忽略重复，导入新资源
- **OVERRIDE**: 用新数据覆盖现有资源
- **SKIP**: 跳过导入

## 🚀 核心API接口总览

### 云厂商配置管理
- `POST /api/ams-ce/cloud/configs` - 创建云厂商配置
- `GET /api/ams-ce/cloud/configs` - 获取配置列表
- `POST /api/ams-ce/cloud/configs/{id}/test` - 测试连接

### 云资源发现
- `POST /api/ams-ce/cloud/discover` - 启动资源发现
- `GET /api/ams-ce/cloud/discoveries` - 获取发现记录列表
- `GET /api/ams-ce/cloud/discoveries/{id}` - 获取发现详情

### 资源预览和选择
- `GET /api/ams-ce/cloud/discoveries/{id}/resources` - 获取发现的资源列表
- `GET /api/ams-ce/cloud/discoveries/{id}/resources/duplicates` - 获取资源列表（含重复信息）
- `PUT /api/ams-ce/cloud/discoveries/{id}/resources/select` - 选择要导入的资源

### 资源导入
- `POST /api/ams-ce/cloud/discoveries/{id}/import` - 导入选中资源（支持重复处理）

### 重复检测管理
- `GET /api/ams-ce/hosts/duplicates` - 获取重复主机列表
- `POST /api/ams-ce/hosts/{id}/resolve-duplicate` - 解决主机重复问题

## 🔍 重要接口详解

### 1. 获取资源列表（包含重复信息）

```bash
GET /api/ams-ce/cloud/discoveries/{id}/resources/duplicates?limit=20&offset=0
```

**响应示例**:
```json
{
  "dat": {
    "list": [
      {
        "id": 1,
        "cloud_id": "i-1234567890abcdef0",
        "name": "web-server-01",
        "resource_type": "ecs",
        "has_duplicate": true,
        "duplicate_status": "DETECTED",
        "conflict_ip": "*********",
        "duplicate_info": {
          "existing_host": {
            "id": 123,
            "name": "web-server-01-old",
            "ip": "*********"
          },
          "differences": [
            {
              "field": "name",
              "existing_value": "web-server-01-old",
              "discovered_value": "web-server-01",
              "conflict_level": "minor"
            }
          ]
        }
      }
    ],
    "total": 10,
    "duplicate_stats": {
      "total_resources": 10,
      "duplicate_resources": 3,
      "resolved_duplicates": 0,
      "pending_duplicates": 3
    }
  }
}
```

### 2. 导入资源（支持重复处理）

```bash
POST /api/ams-ce/cloud/discoveries/{id}/import
```

**请求示例**:
```json
{
  "node_id": 123,
  "resource_mapping": {
    "1": {"cate": "server", "tenant": "prod", "note": "生产服务器"},
    "2": {"cate": "database", "tenant": "prod", "note": "生产数据库"}
  },
  "duplicate_actions": {
    "1": "OVERRIDE",
    "2": "IGNORE",
    "3": "SKIP"
  }
}
```

## 🔧 代码实现

### 资源处理器接口

```go
type ResourceHandler interface {
    // 从临时资源创建专门表记录
    CreateFromTemp(tempResource *models.CloudResourceTemp, mapping ImportMapping) (int64, error)
    
    // 从专门表同步到Resource表
    SyncToResource(sourceId int64, mapping ImportMapping) (*models.Resource, error)
    
    // 获取资源类型
    GetResourceType() string
    
    // 更新现有记录（用于重复处理）
    UpdateExisting(sourceId int64, tempResource *models.CloudResourceTemp, mapping ImportMapping) error
}
```

### 重复检测器

```go
type DuplicateDetector struct{}

func (d *DuplicateDetector) DetectDuplicatesInTempResources(discoveryId int64) error {
    // 获取临时资源并进行重复检测
    // 根据资源类型采用不同的检测策略
    // 标记重复状态和冲突信息
}
```

## 📋 完整导入流程和API接口

### 步骤1: 配置云厂商

#### 1.1 创建云厂商配置
```bash
POST /api/ams-ce/cloud/configs
Content-Type: application/json

{
  "name": "阿里云生产环境",
  "provider": "aliyun",
  "region": "cn-beijing",
  "access_key": "your_access_key",
  "secret_key": "your_secret_key",
  "description": "生产环境阿里云配置"
}
```

#### 1.2 测试云厂商连接
```bash
POST /api/ams-ce/cloud/configs/{config_id}/test
```

### 步骤2: 发现云资源

#### 2.1 启动资源发现
```bash
POST /api/ams-ce/cloud/discover
Content-Type: application/json

{
  "config_id": 1,
  "resource_types": ["ecs", "rds", "redis"],
  "filters": {
    "zone": "cn-beijing-a",
    "status": ["running"]
  }
}

# 响应示例
{
  "dat": {
    "id": 123,
    "status": "pending",
    "message": "Discovery started"
  }
}
```

#### 2.2 查看发现进度
```bash
GET /api/ams-ce/cloud/discoveries/{discovery_id}

# 响应示例
{
  "dat": {
    "id": 123,
    "status": "success",
    "total_count": 15,
    "imported_count": 0,
    "created_at": "2024-01-01T10:00:00Z"
  }
}
```

### 步骤3: 查看发现结果（包含重复检测）

#### 3.1 获取资源列表（包含重复信息）
```bash
GET /api/ams-ce/cloud/discoveries/{discovery_id}/resources/duplicates?limit=20&offset=0

# 响应示例
{
  "dat": {
    "list": [
      {
        "id": 1,
        "cloud_id": "i-1234567890abcdef0",
        "name": "web-server-01",
        "resource_type": "ecs",
        "region": "cn-beijing",
        "zone": "cn-beijing-a",
        "status": "running",
        "has_duplicate": true,
        "duplicate_status": "DETECTED",
        "conflict_ip": "*********",
        "duplicate_info": {
          "existing_host": {
            "id": 123,
            "name": "web-server-01-old",
            "ip": "*********",
            "data_source": "MANUAL"
          },
          "differences": [
            {
              "field": "name",
              "existing_value": "web-server-01-old",
              "discovered_value": "web-server-01",
              "conflict_level": "minor"
            },
            {
              "field": "cpu",
              "existing_value": "2",
              "discovered_value": "4",
              "conflict_level": "major"
            }
          ]
        },
        "selected": false,
        "imported": false
      }
    ],
    "total": 15,
    "duplicate_stats": {
      "total_resources": 15,
      "duplicate_resources": 3,
      "resolved_duplicates": 0,
      "pending_duplicates": 3
    }
  }
}
```

### 步骤4: 选择要导入的资源

#### 4.1 选择资源
```bash
PUT /api/ams-ce/cloud/discoveries/{discovery_id}/resources/select
Content-Type: application/json

{
  "resource_ids": [1, 2, 3, 4, 5]
}
```

### 步骤5: 导入资源（处理重复）

#### 5.1 导入选中的资源
```bash
POST /api/ams-ce/cloud/discoveries/{discovery_id}/import
Content-Type: application/json

{
  "node_id": 123,
  "resource_mapping": {
    "1": {
      "cate": "server",
      "tenant": "prod",
      "note": "生产Web服务器"
    },
    "2": {
      "cate": "database",
      "tenant": "prod",
      "note": "生产MySQL数据库"
    },
    "3": {
      "cate": "cache",
      "tenant": "prod",
      "note": "生产Redis缓存"
    }
  },
  "duplicate_actions": {
    "1": "OVERRIDE",  // 覆盖现有资源
    "2": "IGNORE",    // 忽略重复，导入新资源
    "3": "SKIP"       // 跳过导入
  }
}

# 响应示例
{
  "dat": {
    "success": true,
    "imported_count": 2,
    "message": "Resources imported successfully"
  }
}
```

### 步骤6: 验证导入结果

#### 6.1 查看节点下的资源
```bash
GET /api/rdb-ce/nodes/{node_id}/resources

# 响应示例
{
  "dat": [
    {
      "id": 456,
      "name": "web-server-01",
      "cate": "server",
      "tenant": "prod",
      "source_type": "host",
      "source_id": 789,
      "extend": {
        "cloud_id": "i-1234567890abcdef0",
        "cloud_provider": "aliyun",
        "cloud_region": "cn-beijing",
        "instance_type": "ecs.c5.large",
        "public_ip": "*******",
        "private_ip": "*********"
      }
    }
  ]
}
```

## 🔄 重复处理策略详解

### IGNORE（忽略重复）
- **行为**: 保留现有资源，创建新的资源记录
- **适用场景**: 确实需要两个相同IP的资源（如容器场景）
- **结果**: 系统中会有两个资源记录

### OVERRIDE（覆盖现有）
- **行为**: 用新发现的数据更新现有资源
- **适用场景**: 现有数据过时，需要用最新数据更新
- **结果**: 现有资源被更新，保持唯一性

### SKIP（跳过导入）
- **行为**: 不导入该资源，保持现有资源不变
- **适用场景**: 现有数据正确，不需要更新
- **结果**: 该资源不会被导入

## 🎯 核心特性

1. **分层存储**: 不同类型资源存储在专门表中，保持数据完整性
2. **统一接口**: 所有资源最终同步到resource表，保持API兼容性
3. **重复检测**: 自动检测重复资源，提供详细的冲突信息
4. **灵活处理**: 支持多种重复处理策略
5. **事件驱动**: 集成JumpServer同步事件
6. **向后兼容**: 不影响现有功能

## 🔄 数据流转

1. **发现阶段**: 云API → cloud_resource_temp
2. **检测阶段**: 重复检测 → 更新duplicate_status
3. **选择阶段**: 用户选择 → 更新selected字段
4. **导入阶段**: 
   - 创建专门表记录
   - 同步到resource表
   - 绑定到节点
   - 发布同步事件
5. **同步阶段**: JumpServer同步服务消费事件

## ⚠️ 注意事项

1. **数据库迁移**: 需要执行SQL迁移脚本添加新表和字段
2. **重复检测**: 目前主要基于IP地址和cloud_id，可根据需要扩展
3. **性能考虑**: 大量资源导入时建议分批处理
4. **错误处理**: 导入失败时会自动清理已创建的记录

## 🧪 测试建议

1. **单元测试**: 测试各个ResourceHandler的实现
2. **集成测试**: 测试完整的导入流程
3. **重复检测测试**: 测试各种重复场景
4. **性能测试**: 测试大量资源的导入性能
