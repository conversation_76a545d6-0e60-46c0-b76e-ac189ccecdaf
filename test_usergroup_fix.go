package main

import (
	"fmt"
	"log"

	"arboris/src/modules/jumpserver-sync/jumpserver"
)

func main() {
	// 创建JumpServer客户端（使用测试配置）
	client := jumpserver.NewClient("http://your-jumpserver-url", "your-token", "your-org-id")

	// 创建测试用户组
	testGroup := &jumpserver.UserGroup{
		Name:    "test-group-fix",
		Comment: "Test group for fix validation",
		Users:   []interface{}{}, // 空用户列表
		Labels:  []interface{}{}, // 空标签列表
	}

	fmt.Printf("Testing user group creation with fixed code...\n")
	fmt.Printf("Group name: %s\n", testGroup.Name)
	fmt.Printf("Group comment: %s\n", testGroup.Comment)
	fmt.Printf("Users: %v\n", testGroup.GetUsernames())

	// 尝试创建用户组
	createdGroup, err := client.CreateUserGroup(testGroup)
	if err != nil {
		log.Printf("Error creating user group: %v", err)
		return
	}

	fmt.Printf("User group created successfully!\n")
	fmt.Printf("Created group ID: %s\n", createdGroup.ID)
	fmt.Printf("Created group name: %s\n", createdGroup.Name)
}
