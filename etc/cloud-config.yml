# 云资源管理配置示例
# 这个文件展示了如何配置云资源管理功能

# 云资源管理全局配置
cloud:
  # 是否启用云资源管理功能
  enabled: true
  
  # 加密密钥配置（用于加密存储云厂商密钥）
  # 注意：这个密钥必须是32字节长度，生产环境请使用强密钥
  encryption_key: "arboris-cloud-key-32-characters"
  
  # 资源发现配置
  discovery:
    # 单次发现的最大资源数量
    max_resources_per_discovery: 1000
    
    # 发现记录保留天数
    retention_days: 30
    
    # 并发发现的最大goroutine数量
    max_concurrent_discoveries: 5
  
  # 资源同步配置
  sync:
    # 同步超时时间（秒）
    timeout: 300

    # 批量同步的最大资源数量
    batch_size: 100

    # 同步失败重试次数
    max_retries: 3

    # 重试间隔（秒）
    retry_interval: 60

  # 重复检测配置
  duplicate_detection:
    # 是否启用重复检测
    enabled: true

    # IP地址匹配配置
    ip_matching:
      # 优先使用私网IP作为唯一标识
      prefer_private_ip: true
      # 私网IP不存在时使用公网IP
      fallback_to_public_ip: true
      # 忽略没有IP的设备
      ignore_empty_ip: true

    # 通知配置
    notifications:
      enabled: true
      email_recipients: ["<EMAIL>"]
      webhook_url: ""

    # 自动处理规则（可选）
    auto_rules:
      # 相同数据源自动忽略
      auto_ignore_same_source: false
      # 自动用发现数据覆盖手动数据
      auto_override_manual_with_discovery: false

# 支持的云厂商配置
providers:
  # 金山云配置
  kingsoft:
    # 认证配置
    credentials:
      # 访问密钥ID
      access_key: ""
      # 访问密钥Secret
      secret_key: ""
      # 默认区域
      default_region: "cn-beijing-6"

    # 支持的地域列表
    regions:
      - cn-beijing-6
      - cn-shanghai-2
      - cn-guangzhou-1
      - cn-hongkong-2

    # 支持的资源类型
    resource_types:
      - ecs      # 云服务器
      - epc      # 裸金属服务器
      - rds      # 云数据库
      - redis    # 云缓存
      - slb      # 负载均衡

    # API配置
    api:
      # 默认端点
      endpoint: "https://ecs.cn-beijing-6.api.ksyun.com"

      # API版本
      version: "2016-03-04"

      # 请求超时时间（秒）
      timeout: 30

      # 重试配置
      max_retries: 3
      retry_delay: 1
  
  # 火山云配置
  volcano:
    # 认证配置
    credentials:
      # 访问密钥ID
      access_key: ""
      # 访问密钥Secret
      secret_key: ""
      # 默认区域
      default_region: "cn-beijing"

    # 支持的地域列表
    regions:
      - cn-beijing
      - cn-shanghai
      - cn-guangzhou
      - cn-hongkong

    # 支持的资源类型
    resource_types:
      - ecs      # 云服务器
      - rds      # 云数据库
      - redis    # 云缓存
      - clb      # 负载均衡

    # API配置
    api:
      # 默认端点
      endpoint: "https://ecs.volcengineapi.com"

      # API版本
      version: "2020-04-01"

      # 请求超时时间（秒）
      timeout: 30

      # 重试配置
      max_retries: 3
      retry_delay: 1

  # 阿里云配置
  aliyun:
    # 认证配置
    credentials:
      # 访问密钥ID
      access_key: ""
      # 访问密钥Secret
      secret_key: ""
      # 默认区域
      default_region: "cn-hangzhou"

    # 支持的地域列表
    regions:
      - cn-hangzhou
      - cn-shanghai
      - cn-beijing
      - cn-shenzhen
      - cn-hongkong

    # 支持的资源类型
    resource_types:
      - ecs      # 云服务器
      - rds      # 云数据库
      - redis    # 云缓存
      - slb      # 负载均衡
      - oss      # 对象存储
      - vpc      # 专有网络

    # API配置
    api:
      # 默认端点
      endpoint: "https://ecs.cn-hangzhou.aliyuncs.com"

      # API版本
      version: "2014-05-26"

      # 请求超时时间（秒）
      timeout: 30

      # 重试配置
      max_retries: 3
      retry_delay: 1

  # 腾讯云配置
  tencent:
    # 认证配置
    credentials:
      # 访问密钥ID (SecretId)
      access_key: ""
      # 访问密钥Secret (SecretKey)
      secret_key: ""
      # 默认区域
      default_region: "ap-beijing"

    # 支持的地域列表
    regions:
      - ap-beijing
      - ap-shanghai
      - ap-guangzhou
      - ap-chengdu
      - ap-hongkong

    # 支持的资源类型
    resource_types:
      - cvm      # 云服务器
      - cdb      # 云数据库
      - redis    # 云缓存
      - clb      # 负载均衡
      - cos      # 对象存储
      - vpc      # 私有网络

    # API配置
    api:
      # 默认端点
      endpoint: "https://cvm.tencentcloudapi.com"

      # API版本
      version: "2017-03-12"

      # 请求超时时间（秒）
      timeout: 30

      # 重试配置
      max_retries: 3
      retry_delay: 1

  # 天翼云配置
  ctyun:
    # 认证配置
    credentials:
      # 访问密钥ID
      access_key: ""
      # 访问密钥Secret
      secret_key: ""
      # 默认区域
      default_region: "cn-bj4"

    # 支持的地域列表
    regions:
      - cn-bj
      - cn-sh
      - cn-gz
      - cn-cd
      - cn-hk

    # 支持的资源类型
    resource_types:
      - ecs      # 云服务器
      - rds      # 云数据库
      - redis    # 云缓存
      - elb      # 负载均衡
      - obs      # 对象存储
      - vpc      # 虚拟私有云

    # API配置
    api:
      # 默认端点
      endpoint: "https://ctyun-api.cn"

      # API版本
      version: "v1"

      # 请求超时时间（秒）
      timeout: 30

      # 重试配置
      max_retries: 3
      retry_delay: 1

# 资源类型映射配置
resource_mapping:
  # 云服务器映射
  ecs:
    default_cate: "server"
    default_tenant: "default"
    
    # 字段映射
    fields:
      name: "InstanceName"
      ip: "PrivateIpAddress"
      public_ip: "PublicIpAddress"
      cpu: "Cpus"
      memory: "MemorySize"
      os: "OsName"
  
  # 云数据库映射
  rds:
    default_cate: "database"
    default_tenant: "default"
    
    # 字段映射
    fields:
      name: "DBInstanceName"
      engine: "Engine"
      version: "EngineVersion"
      storage: "AllocatedStorage"
  
  # 云缓存映射
  redis:
    default_cate: "cache"
    default_tenant: "default"
    
    # 字段映射
    fields:
      name: "CacheName"
      engine: "Engine"
      version: "EngineVersion"
      capacity: "Capacity"
  
  # 负载均衡映射
  slb:
    default_cate: "loadbalancer"
    default_tenant: "default"
    
    # 字段映射
    fields:
      name: "LoadBalancerName"
      type: "LoadBalancerType"
      status: "Status"
  
  clb:
    default_cate: "loadbalancer"
    default_tenant: "default"

    # 字段映射
    fields:
      name: "LoadBalancerName"
      type: "Type"
      status: "Status"

  # 腾讯云CVM映射
  cvm:
    default_cate: "server"
    default_tenant: "default"

    # 字段映射
    fields:
      name: "InstanceName"
      ip: "PrivateIpAddresses"
      public_ip: "PublicIpAddresses"
      cpu: "CPU"
      memory: "Memory"
      os: "OsName"

  # 腾讯云CDB映射
  cdb:
    default_cate: "database"
    default_tenant: "default"

    # 字段映射
    fields:
      name: "InstanceName"
      engine: "Engine"
      version: "EngineVersion"
      memory: "Memory"
      volume: "Volume"

  # 对象存储映射
  oss:
    default_cate: "storage"
    default_tenant: "default"

    # 字段映射
    fields:
      name: "Name"
      location: "Location"
      storage_class: "StorageClass"

  cos:
    default_cate: "storage"
    default_tenant: "default"

    # 字段映射
    fields:
      name: "Name"
      location: "Location"

  obs:
    default_cate: "storage"
    default_tenant: "default"

    # 字段映射
    fields:
      name: "BucketName"
      location: "Location"
      storage_class: "StorageClass"

  # VPC映射
  vpc:
    default_cate: "network"
    default_tenant: "default"

    # 字段映射
    fields:
      name: "VpcName"
      cidr_block: "CidrBlock"
      status: "Status"

  # ELB映射
  elb:
    default_cate: "loadbalancer"
    default_tenant: "default"

    # 字段映射
    fields:
      name: "LoadBalancerName"
      type: "LoadBalancerType"
      status: "Status"

# 安全配置
security:
  # 密钥存储配置
  key_storage:
    # 是否启用密钥加密
    encryption_enabled: true
    
    # 密钥轮换周期（天）
    rotation_period: 90
    
    # 是否记录密钥访问日志
    access_logging: true
  
  # 权限控制
  permissions:
    # 云厂商配置管理权限
    config_management: ["admin", "cloud_admin"]
    
    # 资源发现权限
    resource_discovery: ["admin", "cloud_admin", "cloud_user"]
    
    # 资源导入权限
    resource_import: ["admin", "cloud_admin", "cloud_user"]
    
    # 资源同步权限
    resource_sync: ["admin", "cloud_admin"]

# 监控和告警配置
monitoring:
  # 是否启用监控
  enabled: true
  
  # 指标收集间隔（秒）
  metrics_interval: 60
  
  # 告警配置
  alerts:
    # 发现失败告警
    discovery_failure:
      enabled: true
      threshold: 3  # 连续失败次数
    
    # 同步失败告警
    sync_failure:
      enabled: true
      threshold: 5  # 连续失败次数
    
    # 配置连接失败告警
    connection_failure:
      enabled: true
      threshold: 3  # 连续失败次数

# 日志配置
logging:
  # 云资源操作日志级别
  level: "info"
  
  # 是否记录API调用详情
  api_details: false
  
  # 是否记录敏感信息（密钥等）
  sensitive_data: false
  
  # 日志保留天数
  retention_days: 30

# 性能优化配置
performance:
  # 数据库连接池配置
  database:
    max_open_conns: 100
    max_idle_conns: 10
    conn_max_lifetime: 3600  # 秒
  
  # 缓存配置
  cache:
    # 是否启用缓存
    enabled: true
    
    # 缓存过期时间（秒）
    ttl: 300
    
    # 最大缓存条目数
    max_entries: 1000
  
  # 并发控制
  concurrency:
    # 最大并发API调用数
    max_api_calls: 10
    
    # 最大并发导入任务数
    max_import_tasks: 5
