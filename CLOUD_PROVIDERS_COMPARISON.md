# 云厂商对比和适配说明

## 📊 支持的云厂商对比

| 云厂商 | 代码标识 | 支持的资源类型 | 主要特点 | 状态 |
|--------|----------|----------------|----------|------|
| 金山云 | `kingsoft` | ECS, RDS, Redis, SLB | 国内老牌云厂商，稳定可靠 | ✅ 已支持 |
| 火山云 | `volcano` | ECS, RDS, Redis, CLB | 字节跳动旗下，技术先进 | ✅ 已支持 |
| 阿里云 | `aliyun` | ECS, RDS, Redis, SLB, OSS, VPC | 国内最大云厂商，生态完善 | ✅ 已支持 |
| 腾讯云 | `tencent` | CVM, CDB, Redis, CLB, COS, VPC | 腾讯旗下，游戏和社交优势 | ✅ 已支持 |
| 天翼云 | `ctyun` | ECS, RDS, Redis, ELB, OBS, VPC | 中国电信旗下，网络优势 | ✅ 已支持 |

## 🔧 资源类型映射

### 计算资源

| 云厂商 | 资源类型 | 中文名称 | API字段映射 |
|--------|----------|----------|-------------|
| 金山云 | `ecs` | 云服务器 | InstanceId, InstanceName, Status |
| 火山云 | `ecs` | 云服务器 | InstanceId, InstanceName, InstanceStatus |
| 阿里云 | `ecs` | 云服务器 | InstanceId, InstanceName, Status |
| 腾讯云 | `cvm` | 云服务器 | InstanceId, InstanceName, InstanceState |
| 天翼云 | `ecs` | 云服务器 | InstanceId, InstanceName, Status |

### 数据库资源

| 云厂商 | 资源类型 | 中文名称 | API字段映射 |
|--------|----------|----------|-------------|
| 金山云 | `rds` | 云数据库 | DBInstanceId, DBInstanceName, Status |
| 火山云 | `rds` | 云数据库 | InstanceId, InstanceName, InstanceStatus |
| 阿里云 | `rds` | 云数据库 | DBInstanceId, DBInstanceDescription, DBInstanceStatus |
| 腾讯云 | `cdb` | 云数据库 | InstanceId, InstanceName, Status |
| 天翼云 | `rds` | 云数据库 | InstanceId, InstanceName, Status |

### 缓存资源

| 云厂商 | 资源类型 | 中文名称 | API字段映射 |
|--------|----------|----------|-------------|
| 金山云 | `redis` | 云缓存 | CacheId, CacheName, Status |
| 火山云 | `redis` | 云缓存 | InstanceId, InstanceName, InstanceStatus |
| 阿里云 | `redis` | 云缓存 | InstanceId, InstanceName, InstanceStatus |
| 腾讯云 | `redis` | 云缓存 | InstanceId, InstanceName, Status |
| 天翼云 | `redis` | 云缓存 | InstanceId, InstanceName, Status |

### 负载均衡资源

| 云厂商 | 资源类型 | 中文名称 | API字段映射 |
|--------|----------|----------|-------------|
| 金山云 | `slb` | 负载均衡 | LoadBalancerId, LoadBalancerName, Status |
| 火山云 | `clb` | 负载均衡 | LoadBalancerId, LoadBalancerName, Status |
| 阿里云 | `slb` | 负载均衡 | LoadBalancerId, LoadBalancerName, LoadBalancerStatus |
| 腾讯云 | `clb` | 负载均衡 | LoadBalancerId, LoadBalancerName, Status |
| 天翼云 | `elb` | 负载均衡 | LoadBalancerId, LoadBalancerName, Status |

### 存储资源

| 云厂商 | 资源类型 | 中文名称 | API字段映射 |
|--------|----------|----------|-------------|
| 阿里云 | `oss` | 对象存储 | Name, Location, StorageClass |
| 腾讯云 | `cos` | 对象存储 | Name, Location |
| 天翼云 | `obs` | 对象存储 | BucketName, Location, StorageClass |

### 网络资源

| 云厂商 | 资源类型 | 中文名称 | API字段映射 |
|--------|----------|----------|-------------|
| 阿里云 | `vpc` | 专有网络 | VpcId, VpcName, Status, CidrBlock |
| 腾讯云 | `vpc` | 私有网络 | VpcId, VpcName, CidrBlock |
| 天翼云 | `vpc` | 虚拟私有云 | VpcId, VpcName, Status, CidrBlock |

## 🌍 地域支持

### 金山云地域
- `cn-beijing-6` - 北京六区
- `cn-shanghai-2` - 上海二区
- `cn-guangzhou-1` - 广州一区
- `cn-hongkong-2` - 香港二区

### 火山云地域
- `cn-beijing` - 华北2（北京）
- `cn-shanghai` - 华东2（上海）
- `cn-guangzhou` - 华南1（广州）
- `cn-hongkong` - 中国香港

### 阿里云地域
- `cn-hangzhou` - 华东1（杭州）
- `cn-shanghai` - 华东2（上海）
- `cn-beijing` - 华北2（北京）
- `cn-shenzhen` - 华南1（深圳）
- `cn-hongkong` - 中国香港

### 腾讯云地域
- `ap-beijing` - 北京
- `ap-shanghai` - 上海
- `ap-guangzhou` - 广州
- `ap-chengdu` - 成都
- `ap-hongkong` - 中国香港

### 天翼云地域
- `cn-bj` - 北京
- `cn-sh` - 上海
- `cn-gz` - 广州
- `cn-cd` - 成都
- `cn-hk` - 香港

## 🔌 API接口特点

### 认证方式

| 云厂商 | 认证方式 | 密钥格式 |
|--------|----------|----------|
| 金山云 | AccessKey + SecretKey | 标准格式 |
| 火山云 | AccessKey + SecretKey | 标准格式 |
| 阿里云 | AccessKey + SecretKey | 标准格式 |
| 腾讯云 | SecretId + SecretKey | 标准格式 |
| 天翼云 | AccessKey + SecretKey | 标准格式 |

### API端点

| 云厂商 | 默认端点 | 协议 |
|--------|----------|------|
| 金山云 | `https://ecs.cn-beijing-6.api.ksyun.com` | HTTPS |
| 火山云 | `https://ecs.volcengineapi.com` | HTTPS |
| 阿里云 | `https://ecs.cn-hangzhou.aliyuncs.com` | HTTPS |
| 腾讯云 | `https://cvm.tencentcloudapi.com` | HTTPS |
| 天翼云 | `https://ctyun-api.cn` | HTTPS |

### 状态码映射

#### 云服务器状态

| 状态 | 金山云 | 火山云 | 阿里云 | 腾讯云 | 天翼云 |
|------|--------|--------|--------|--------|--------|
| 运行中 | `running` | `RUNNING` | `Running` | `RUNNING` | `ACTIVE` |
| 已停止 | `stopped` | `STOPPED` | `Stopped` | `STOPPED` | `SHUTOFF` |
| 创建中 | `creating` | `PENDING` | `Starting` | `PENDING` | `BUILD` |

#### 数据库状态

| 状态 | 金山云 | 火山云 | 阿里云 | 腾讯云 | 天翼云 |
|------|--------|--------|--------|--------|--------|
| 运行中 | `running` | `Running` | `Running` | `1` | `ACTIVE` |
| 创建中 | `creating` | `Creating` | `Creating` | `0` | `BUILD` |
| 维护中 | `maintaining` | `Maintaining` | `Maintaining` | `-1` | `MAINTENANCE` |

## 🛠️ 实现细节

### 模拟数据说明

当前实现使用模拟数据进行测试，实际生产环境需要：

1. **集成真实SDK**
   - 金山云：使用官方Go SDK
   - 火山云：使用官方Go SDK
   - 阿里云：使用阿里云Go SDK
   - 腾讯云：使用腾讯云Go SDK
   - 天翼云：使用官方API或SDK

2. **错误处理**
   - API限流处理
   - 网络超时重试
   - 认证失败处理
   - 权限不足处理

3. **性能优化**
   - 并发请求控制
   - 结果缓存机制
   - 分页查询优化
   - 连接池管理

### 扩展新云厂商

要添加新的云厂商支持，需要：

1. **创建适配器文件**
   ```go
   // src/modules/ams/cloud/newprovider.go
   type NewProvider struct {
       config ProviderConfig
   }
   
   func (n *NewProvider) DiscoverResources(resourceType string, filters map[string]string) ([]CloudResource, error) {
       // 实现资源发现逻辑
   }
   ```

2. **更新provider.go**
   ```go
   case "newprovider":
       return NewNewProvider(config), nil
   ```

3. **添加测试用例**
   ```go
   func TestNewProvider(t *testing.T) {
       // 添加测试逻辑
   }
   ```

4. **更新配置文件**
   - 添加到支持的云厂商列表
   - 配置资源类型映射
   - 添加地域支持

## 📝 使用建议

### 生产环境配置

1. **安全配置**
   - 使用最小权限原则配置云厂商账号
   - 定期轮换AccessKey和SecretKey
   - 启用API访问日志记录

2. **性能配置**
   - 根据资源规模调整并发数
   - 设置合适的超时时间
   - 启用结果缓存

3. **监控告警**
   - 监控API调用成功率
   - 设置资源发现失败告警
   - 跟踪资源同步状态

### 故障排查

1. **连接问题**
   - 检查网络连通性
   - 验证API端点正确性
   - 确认防火墙规则

2. **认证问题**
   - 验证密钥格式正确性
   - 检查密钥权限范围
   - 确认密钥未过期

3. **数据问题**
   - 检查地域配置
   - 验证资源类型支持
   - 确认过滤条件正确

## 🔮 未来规划

1. **更多云厂商支持**
   - 华为云
   - 百度云
   - UCloud
   - 青云

2. **更多资源类型**
   - 容器服务
   - 函数计算
   - 消息队列
   - 大数据服务

3. **高级功能**
   - 资源成本分析
   - 资源使用监控
   - 自动化运维
   - 多云管理
