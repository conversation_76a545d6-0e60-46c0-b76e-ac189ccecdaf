# Arboris 数据库结构说明

## 📁 SQL文件说明

### 🆕 **推荐使用的文件**

#### `arboris_complete_updated.sql` - **完整更新版数据库结构**
- **用途**: 全新安装时使用的完整数据库结构
- **包含内容**:
  - AMS数据库 (`arboris_ams`): 主机管理、云资源专门表
  - RDB数据库 (`arboris_rdb`): 用户权限、节点资源管理
  - 云资源专门表: 数据库、缓存、负载均衡、存储、网络等
  - 资源类型映射表和初始数据
- **特点**: 
  - 无外键约束
  - 支持云资源分层存储
  - 包含完整的初始数据

#### `cloud_resource_migration.sql` - **现有环境迁移脚本**
- **用途**: 在现有数据库基础上添加云资源专门表
- **包含内容**:
  - 为host表添加云资源字段的ALTER语句
  - 创建所有云资源专门表
  - 安全的字段和索引添加（避免重复）
- **特点**: 
  - 使用IF NOT EXISTS避免重复创建
  - 适合生产环境升级

#### `cloud_resource_specialized_tables.sql` - **云资源专门表定义**
- **用途**: 仅包含云资源专门表的定义
- **包含内容**:
  - 云数据库、缓存、负载均衡、存储、网络等专门表
  - 资源类型映射表
  - 初始映射数据
- **特点**: 可单独使用或参考

#### `add_duplicate_detection_fields.sql` - **重复检测字段添加脚本**
- **用途**: 为现有云资源表添加重复检测相关字段
- **包含内容**:
  - 为所有云资源专门表添加重复检测字段
  - 为cloud_resource_temp表添加重复检测字段
  - 安全的字段添加（使用IF NOT EXISTS）
- **特点**: 适合在已有云资源表的环境中添加重复检测功能

### 📜 **原有文件**

#### `arboris_complete_no_fk.sql` - **原完整数据库结构**
- **状态**: 已过时，不推荐使用
- **问题**: 缺少云资源专门表，host表缺少云资源字段

#### `arboris_ams.sql` - **AMS数据库结构**
- **状态**: 基础版本，缺少云资源扩展

#### `arboris_rdb.sql` - **RDB数据库结构**
- **状态**: 基础版本

## 🚀 使用建议

### 全新安装
```bash
# 使用完整更新版
mysql -u root -p < sql/arboris_complete_updated.sql
```

### 现有环境升级
```bash
# 备份现有数据库
mysqldump -u root -p arboris_ams > backup_ams.sql
mysqldump -u root -p arboris_rdb > backup_rdb.sql

# 执行迁移脚本
mysql -u root -p arboris_ams < sql/cloud_resource_migration.sql

# 如果需要添加重复检测功能
mysql -u root -p arboris_ams < sql/add_duplicate_detection_fields.sql
```

## 🏗️ 数据库架构

### AMS数据库 (`arboris_ams`)
- **主机管理**: `host` (扩展支持云资源)
- **云资源专门表**:
  - `cloud_database` - 云数据库
  - `cloud_cache` - 云缓存
  - `cloud_loadbalancer` - 云负载均衡
  - `cloud_storage` - 云存储
  - `cloud_network` - 云网络
  - `cloud_resource_generic` - 通用云资源
- **云资源管理**:
  - `cloud_provider_config` - 云厂商配置
  - `cloud_resource_discovery` - 资源发现记录
  - `cloud_resource_temp` - 临时资源表
  - `cloud_resource_type_mapping` - 资源类型映射

### RDB数据库 (`arboris_rdb`)
- **用户管理**: `user`, `user_group`, `user_group_member`
- **权限管理**: `role`, `role_operation`
- **节点管理**: `node`, `node_admin`, `node_cate`
- **资源管理**: `resource`, `node_resource`, `node_role`
- **会话管理**: `session`

## 🔄 云资源导入流程

1. **发现阶段**: 云资源 → `cloud_resource_temp`
2. **重复检测阶段**: 检测IP冲突，标记重复状态
3. **用户选择阶段**: 前端展示重复信息，用户选择处理方式
4. **导入阶段**:
   - ECS/VM → `host` → `resource`
   - RDS → `cloud_database` → `resource`
   - Redis → `cloud_cache` → `resource`
   - 其他 → 对应专门表 → `resource`
5. **绑定阶段**: `resource` ↔ `node` (通过`node_resource`)

## 🔍 重复检测机制

### 检测逻辑
- **IP地址匹配**: 优先使用私网IP，备用公网IP
- **跨表检测**: 同时检测host表和resource表中的重复
- **状态标记**: 在临时表中标记重复状态和冲突信息

### 处理方式
- **导入新资源** (IGNORE): 忽略重复，创建新记录
- **覆盖现有资源** (OVERRIDE): 用新数据更新现有记录
- **跳过导入** (SKIP): 不处理此资源

### 重复信息字段
- `has_duplicate`: 是否有重复
- `duplicate_status`: 重复状态 (NONE/DETECTED/IGNORED/OVERRIDDEN)
- `duplicate_info`: 重复详细信息 (JSON格式)
- `conflict_ip`: 冲突的IP地址
- `existing_device_id`: 已存在设备ID
- `existing_device_type`: 已存在设备类型

## 📊 支持的云厂商和资源类型

| 云厂商 | 计算 | 数据库 | 缓存 | 负载均衡 | 存储 | 网络 |
|--------|------|--------|------|----------|------|------|
| 阿里云 | ECS | RDS | Redis | SLB | OSS | VPC |
| 腾讯云 | CVM | CDB | Redis | CLB | COS | VPC |
| 天翼云 | ECS | RDS | Redis | ELB | OBS | VPC |
| 金山云 | ECS | RDS | Redis | SLB | - | - |
| 火山云 | ECS | RDS | Redis | CLB | - | - |

## ⚠️ 注意事项

1. **备份**: 升级前务必备份现有数据
2. **测试**: 建议先在测试环境验证
3. **权限**: 确保MySQL用户有足够权限创建数据库和表
4. **字符集**: 使用utf8mb4字符集支持完整Unicode
5. **索引**: 已为常用查询字段添加索引

## 🔧 故障排除

### 常见问题
1. **字段已存在错误**: 迁移脚本使用了安全检查，正常情况下不会出现
2. **权限不足**: 确保MySQL用户有CREATE、ALTER权限
3. **字符集问题**: 确保MySQL支持utf8mb4字符集

### 检查命令
```sql
-- 检查数据库是否创建成功
SHOW DATABASES LIKE 'arboris_%';

-- 检查表是否创建成功
USE arboris_ams;
SHOW TABLES LIKE 'cloud_%';

-- 检查host表是否添加了云资源字段
DESCRIBE host;
```
