-- ========================================
-- 为云资源专门表添加重复检测字段
-- 用于在现有环境中添加重复检测功能
-- ========================================

-- 为cloud_resource_temp表添加重复检测字段（如果表存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES 
     WHERE table_name = 'cloud_resource_temp' 
     AND table_schema = DATABASE()) > 0,
    'ALTER TABLE `cloud_resource_temp` 
     ADD COLUMN IF NOT EXISTS `has_duplicate` tinyint(1) DEFAULT 0 COMMENT "是否有重复：0=无，1=有",
     ADD COLUMN IF NOT EXISTS `duplicate_status` varchar(20) DEFAULT "NONE" COMMENT "重复状态：NONE/DETECTED/IGNORED/OVERRIDDEN",
     ADD COLUMN IF NOT EXISTS `duplicate_info` text COMMENT "重复信息JSON",
     ADD COLUMN IF NOT EXISTS `conflict_ip` varchar(64) DEFAULT "" COMMENT "冲突的IP地址",
     ADD COLUMN IF NOT EXISTS `existing_device_id` bigint(20) DEFAULT 0 COMMENT "已存在设备ID",
     ADD COLUMN IF NOT EXISTS `existing_device_type` varchar(20) DEFAULT "" COMMENT "已存在设备类型：host/resource"',
    'SELECT "cloud_resource_temp table does not exist, skipping"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 为cloud_resource_temp表添加索引（如果表存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES 
     WHERE table_name = 'cloud_resource_temp' 
     AND table_schema = DATABASE()) > 0,
    'ALTER TABLE `cloud_resource_temp` 
     ADD KEY IF NOT EXISTS `idx_has_duplicate` (`has_duplicate`),
     ADD KEY IF NOT EXISTS `idx_duplicate_status` (`duplicate_status`),
     ADD KEY IF NOT EXISTS `idx_existing_device` (`existing_device_type`, `existing_device_id`)',
    'SELECT "cloud_resource_temp table does not exist, skipping index creation"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 为cloud_database表添加重复检测字段（如果表存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES 
     WHERE table_name = 'cloud_database' 
     AND table_schema = DATABASE()) > 0,
    'ALTER TABLE `cloud_database` 
     ADD COLUMN IF NOT EXISTS `data_source` varchar(20) DEFAULT "IMPORT" COMMENT "数据来源：MANUAL/IMPORT/AUTO_DISCOVERY",
     ADD COLUMN IF NOT EXISTS `has_duplicate` tinyint(1) DEFAULT 0 COMMENT "是否有重复：0=无，1=有",
     ADD COLUMN IF NOT EXISTS `duplicate_status` varchar(20) DEFAULT "NONE" COMMENT "重复状态：NONE/DETECTED/IGNORED/OVERRIDDEN",
     ADD COLUMN IF NOT EXISTS `duplicate_info` text COMMENT "重复信息JSON",
     ADD COLUMN IF NOT EXISTS `last_discovery_at` timestamp NULL COMMENT "最后发现时间"',
    'SELECT "cloud_database table does not exist, skipping"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 为cloud_database表添加索引（如果表存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES 
     WHERE table_name = 'cloud_database' 
     AND table_schema = DATABASE()) > 0,
    'ALTER TABLE `cloud_database` 
     ADD KEY IF NOT EXISTS `idx_data_source` (`data_source`),
     ADD KEY IF NOT EXISTS `idx_has_duplicate` (`has_duplicate`),
     ADD KEY IF NOT EXISTS `idx_duplicate_status` (`duplicate_status`)',
    'SELECT "cloud_database table does not exist, skipping index creation"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 为cloud_cache表添加重复检测字段（如果表存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES 
     WHERE table_name = 'cloud_cache' 
     AND table_schema = DATABASE()) > 0,
    'ALTER TABLE `cloud_cache` 
     ADD COLUMN IF NOT EXISTS `data_source` varchar(20) DEFAULT "IMPORT" COMMENT "数据来源：MANUAL/IMPORT/AUTO_DISCOVERY",
     ADD COLUMN IF NOT EXISTS `has_duplicate` tinyint(1) DEFAULT 0 COMMENT "是否有重复：0=无，1=有",
     ADD COLUMN IF NOT EXISTS `duplicate_status` varchar(20) DEFAULT "NONE" COMMENT "重复状态：NONE/DETECTED/IGNORED/OVERRIDDEN",
     ADD COLUMN IF NOT EXISTS `duplicate_info` text COMMENT "重复信息JSON",
     ADD COLUMN IF NOT EXISTS `last_discovery_at` timestamp NULL COMMENT "最后发现时间"',
    'SELECT "cloud_cache table does not exist, skipping"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 为cloud_cache表添加索引（如果表存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES 
     WHERE table_name = 'cloud_cache' 
     AND table_schema = DATABASE()) > 0,
    'ALTER TABLE `cloud_cache` 
     ADD KEY IF NOT EXISTS `idx_data_source` (`data_source`),
     ADD KEY IF NOT EXISTS `idx_has_duplicate` (`has_duplicate`),
     ADD KEY IF NOT EXISTS `idx_duplicate_status` (`duplicate_status`)',
    'SELECT "cloud_cache table does not exist, skipping index creation"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 为cloud_loadbalancer表添加重复检测字段（如果表存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES 
     WHERE table_name = 'cloud_loadbalancer' 
     AND table_schema = DATABASE()) > 0,
    'ALTER TABLE `cloud_loadbalancer` 
     ADD COLUMN IF NOT EXISTS `data_source` varchar(20) DEFAULT "IMPORT" COMMENT "数据来源：MANUAL/IMPORT/AUTO_DISCOVERY",
     ADD COLUMN IF NOT EXISTS `has_duplicate` tinyint(1) DEFAULT 0 COMMENT "是否有重复：0=无，1=有",
     ADD COLUMN IF NOT EXISTS `duplicate_status` varchar(20) DEFAULT "NONE" COMMENT "重复状态：NONE/DETECTED/IGNORED/OVERRIDDEN",
     ADD COLUMN IF NOT EXISTS `duplicate_info` text COMMENT "重复信息JSON",
     ADD COLUMN IF NOT EXISTS `last_discovery_at` timestamp NULL COMMENT "最后发现时间"',
    'SELECT "cloud_loadbalancer table does not exist, skipping"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 为cloud_loadbalancer表添加索引（如果表存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES 
     WHERE table_name = 'cloud_loadbalancer' 
     AND table_schema = DATABASE()) > 0,
    'ALTER TABLE `cloud_loadbalancer` 
     ADD KEY IF NOT EXISTS `idx_data_source` (`data_source`),
     ADD KEY IF NOT EXISTS `idx_has_duplicate` (`has_duplicate`),
     ADD KEY IF NOT EXISTS `idx_duplicate_status` (`duplicate_status`)',
    'SELECT "cloud_loadbalancer table does not exist, skipping index creation"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 为cloud_storage表添加重复检测字段（如果表存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES 
     WHERE table_name = 'cloud_storage' 
     AND table_schema = DATABASE()) > 0,
    'ALTER TABLE `cloud_storage` 
     ADD COLUMN IF NOT EXISTS `data_source` varchar(20) DEFAULT "IMPORT" COMMENT "数据来源：MANUAL/IMPORT/AUTO_DISCOVERY",
     ADD COLUMN IF NOT EXISTS `has_duplicate` tinyint(1) DEFAULT 0 COMMENT "是否有重复：0=无，1=有",
     ADD COLUMN IF NOT EXISTS `duplicate_status` varchar(20) DEFAULT "NONE" COMMENT "重复状态：NONE/DETECTED/IGNORED/OVERRIDDEN",
     ADD COLUMN IF NOT EXISTS `duplicate_info` text COMMENT "重复信息JSON",
     ADD COLUMN IF NOT EXISTS `last_discovery_at` timestamp NULL COMMENT "最后发现时间"',
    'SELECT "cloud_storage table does not exist, skipping"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 为cloud_storage表添加索引（如果表存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES 
     WHERE table_name = 'cloud_storage' 
     AND table_schema = DATABASE()) > 0,
    'ALTER TABLE `cloud_storage` 
     ADD KEY IF NOT EXISTS `idx_data_source` (`data_source`),
     ADD KEY IF NOT EXISTS `idx_has_duplicate` (`has_duplicate`),
     ADD KEY IF NOT EXISTS `idx_duplicate_status` (`duplicate_status`)',
    'SELECT "cloud_storage table does not exist, skipping index creation"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 为cloud_network表添加重复检测字段（如果表存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES 
     WHERE table_name = 'cloud_network' 
     AND table_schema = DATABASE()) > 0,
    'ALTER TABLE `cloud_network` 
     ADD COLUMN IF NOT EXISTS `data_source` varchar(20) DEFAULT "IMPORT" COMMENT "数据来源：MANUAL/IMPORT/AUTO_DISCOVERY",
     ADD COLUMN IF NOT EXISTS `has_duplicate` tinyint(1) DEFAULT 0 COMMENT "是否有重复：0=无，1=有",
     ADD COLUMN IF NOT EXISTS `duplicate_status` varchar(20) DEFAULT "NONE" COMMENT "重复状态：NONE/DETECTED/IGNORED/OVERRIDDEN",
     ADD COLUMN IF NOT EXISTS `duplicate_info` text COMMENT "重复信息JSON",
     ADD COLUMN IF NOT EXISTS `last_discovery_at` timestamp NULL COMMENT "最后发现时间"',
    'SELECT "cloud_network table does not exist, skipping"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 为cloud_network表添加索引（如果表存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES 
     WHERE table_name = 'cloud_network' 
     AND table_schema = DATABASE()) > 0,
    'ALTER TABLE `cloud_network` 
     ADD KEY IF NOT EXISTS `idx_data_source` (`data_source`),
     ADD KEY IF NOT EXISTS `idx_has_duplicate` (`has_duplicate`),
     ADD KEY IF NOT EXISTS `idx_duplicate_status` (`duplicate_status`)',
    'SELECT "cloud_network table does not exist, skipping index creation"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 为cloud_resource_generic表添加重复检测字段（如果表存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES 
     WHERE table_name = 'cloud_resource_generic' 
     AND table_schema = DATABASE()) > 0,
    'ALTER TABLE `cloud_resource_generic` 
     ADD COLUMN IF NOT EXISTS `data_source` varchar(20) DEFAULT "IMPORT" COMMENT "数据来源：MANUAL/IMPORT/AUTO_DISCOVERY",
     ADD COLUMN IF NOT EXISTS `has_duplicate` tinyint(1) DEFAULT 0 COMMENT "是否有重复：0=无，1=有",
     ADD COLUMN IF NOT EXISTS `duplicate_status` varchar(20) DEFAULT "NONE" COMMENT "重复状态：NONE/DETECTED/IGNORED/OVERRIDDEN",
     ADD COLUMN IF NOT EXISTS `duplicate_info` text COMMENT "重复信息JSON",
     ADD COLUMN IF NOT EXISTS `last_discovery_at` timestamp NULL COMMENT "最后发现时间"',
    'SELECT "cloud_resource_generic table does not exist, skipping"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 为cloud_resource_generic表添加索引（如果表存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES 
     WHERE table_name = 'cloud_resource_generic' 
     AND table_schema = DATABASE()) > 0,
    'ALTER TABLE `cloud_resource_generic` 
     ADD KEY IF NOT EXISTS `idx_data_source` (`data_source`),
     ADD KEY IF NOT EXISTS `idx_has_duplicate` (`has_duplicate`),
     ADD KEY IF NOT EXISTS `idx_duplicate_status` (`duplicate_status`)',
    'SELECT "cloud_resource_generic table does not exist, skipping index creation"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 完成提示
SELECT 'Duplicate detection fields added to cloud resource tables successfully!' as message;
