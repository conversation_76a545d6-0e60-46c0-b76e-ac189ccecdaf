-- ========================================
-- 云资源专门表迁移脚本
-- 用于在现有数据库中添加云资源专门表
-- 执行前请备份数据库
-- ========================================

-- 检查并为host表添加云资源相关字段
-- 使用IF NOT EXISTS避免重复添加字段的错误

-- 添加云资源基础字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'host' 
     AND table_schema = DATABASE() 
     AND column_name = 'cloud_id') > 0,
    'SELECT "cloud_id column already exists"',
    'ALTER TABLE `host` ADD COLUMN `cloud_id` varchar(128) DEFAULT "" COMMENT "云端主机ID"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'host' 
     AND table_schema = DATABASE() 
     AND column_name = 'cloud_provider') > 0,
    'SELECT "cloud_provider column already exists"',
    'ALTER TABLE `host` ADD COLUMN `cloud_provider` varchar(32) DEFAULT "" COMMENT "云厂商：aliyun/tencent/ctyun等"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'host' 
     AND table_schema = DATABASE() 
     AND column_name = 'cloud_region') > 0,
    'SELECT "cloud_region column already exists"',
    'ALTER TABLE `host` ADD COLUMN `cloud_region` varchar(64) DEFAULT "" COMMENT "云端地域"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'host' 
     AND table_schema = DATABASE() 
     AND column_name = 'cloud_zone') > 0,
    'SELECT "cloud_zone column already exists"',
    'ALTER TABLE `host` ADD COLUMN `cloud_zone` varchar(64) DEFAULT "" COMMENT "云端可用区"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加实例相关字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'host' 
     AND table_schema = DATABASE() 
     AND column_name = 'instance_type') > 0,
    'SELECT "instance_type column already exists"',
    'ALTER TABLE `host` ADD COLUMN `instance_type` varchar(64) DEFAULT "" COMMENT "实例规格类型"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'host' 
     AND table_schema = DATABASE() 
     AND column_name = 'image_id') > 0,
    'SELECT "image_id column already exists"',
    'ALTER TABLE `host` ADD COLUMN `image_id` varchar(128) DEFAULT "" COMMENT "镜像ID"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加网络相关字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'host' 
     AND table_schema = DATABASE() 
     AND column_name = 'vpc_id') > 0,
    'SELECT "vpc_id column already exists"',
    'ALTER TABLE `host` ADD COLUMN `vpc_id` varchar(128) DEFAULT "" COMMENT "VPC ID"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'host' 
     AND table_schema = DATABASE() 
     AND column_name = 'subnet_id') > 0,
    'SELECT "subnet_id column already exists"',
    'ALTER TABLE `host` ADD COLUMN `subnet_id` varchar(128) DEFAULT "" COMMENT "子网ID"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'host' 
     AND table_schema = DATABASE() 
     AND column_name = 'security_group_ids') > 0,
    'SELECT "security_group_ids column already exists"',
    'ALTER TABLE `host` ADD COLUMN `security_group_ids` text COMMENT "安全组ID列表(JSON)"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'host' 
     AND table_schema = DATABASE() 
     AND column_name = 'public_ip') > 0,
    'SELECT "public_ip column already exists"',
    'ALTER TABLE `host` ADD COLUMN `public_ip` varchar(64) DEFAULT "" COMMENT "公网IP"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'host' 
     AND table_schema = DATABASE() 
     AND column_name = 'private_ip') > 0,
    'SELECT "private_ip column already exists"',
    'ALTER TABLE `host` ADD COLUMN `private_ip` varchar(64) DEFAULT "" COMMENT "私网IP"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加其他云资源字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'host' 
     AND table_schema = DATABASE() 
     AND column_name = 'key_pair_name') > 0,
    'SELECT "key_pair_name column already exists"',
    'ALTER TABLE `host` ADD COLUMN `key_pair_name` varchar(128) DEFAULT "" COMMENT "密钥对名称"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'host' 
     AND table_schema = DATABASE() 
     AND column_name = 'charge_type') > 0,
    'SELECT "charge_type column already exists"',
    'ALTER TABLE `host` ADD COLUMN `charge_type` varchar(32) DEFAULT "" COMMENT "计费类型：prepaid/postpaid"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'host' 
     AND table_schema = DATABASE() 
     AND column_name = 'expired_time') > 0,
    'SELECT "expired_time column already exists"',
    'ALTER TABLE `host` ADD COLUMN `expired_time` timestamp NULL COMMENT "到期时间"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'host' 
     AND table_schema = DATABASE() 
     AND column_name = 'auto_renew') > 0,
    'SELECT "auto_renew column already exists"',
    'ALTER TABLE `host` ADD COLUMN `auto_renew` tinyint(1) DEFAULT 0 COMMENT "是否自动续费"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'host' 
     AND table_schema = DATABASE() 
     AND column_name = 'cloud_status') > 0,
    'SELECT "cloud_status column already exists"',
    'ALTER TABLE `host` ADD COLUMN `cloud_status` varchar(32) DEFAULT "" COMMENT "云端状态：running/stopped/starting等"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'host' 
     AND table_schema = DATABASE() 
     AND column_name = 'cloud_tags') > 0,
    'SELECT "cloud_tags column already exists"',
    'ALTER TABLE `host` ADD COLUMN `cloud_tags` text COMMENT "云端标签信息(JSON)"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'host' 
     AND table_schema = DATABASE() 
     AND column_name = 'cloud_raw_data') > 0,
    'SELECT "cloud_raw_data column already exists"',
    'ALTER TABLE `host` ADD COLUMN `cloud_raw_data` text COMMENT "云端原始数据(JSON)"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 为host表的云资源字段添加索引（如果不存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
     WHERE table_name = 'host' 
     AND table_schema = DATABASE() 
     AND index_name = 'idx_cloud_id') > 0,
    'SELECT "idx_cloud_id index already exists"',
    'ALTER TABLE `host` ADD KEY `idx_cloud_id` (`cloud_id`)'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
     WHERE table_name = 'host' 
     AND table_schema = DATABASE() 
     AND index_name = 'idx_cloud_provider') > 0,
    'SELECT "idx_cloud_provider index already exists"',
    'ALTER TABLE `host` ADD KEY `idx_cloud_provider` (`cloud_provider`)'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
     WHERE table_name = 'host' 
     AND table_schema = DATABASE() 
     AND index_name = 'idx_cloud_region') > 0,
    'SELECT "idx_cloud_region index already exists"',
    'ALTER TABLE `host` ADD KEY `idx_cloud_region` (`cloud_region`)'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
     WHERE table_name = 'host' 
     AND table_schema = DATABASE() 
     AND index_name = 'idx_instance_type') > 0,
    'SELECT "idx_instance_type index already exists"',
    'ALTER TABLE `host` ADD KEY `idx_instance_type` (`instance_type`)'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
     WHERE table_name = 'host' 
     AND table_schema = DATABASE() 
     AND index_name = 'idx_cloud_status') > 0,
    'SELECT "idx_cloud_status index already exists"',
    'ALTER TABLE `host` ADD KEY `idx_cloud_status` (`cloud_status`)'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- ========================================
-- 创建云资源专门表
-- ========================================

-- 创建云数据库资源表
CREATE TABLE IF NOT EXISTS `cloud_database` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT,
    `cloud_id` varchar(128) NOT NULL COMMENT '云端数据库ID',
    `name` varchar(256) NOT NULL COMMENT '数据库名称',
    `engine` varchar(64) NOT NULL COMMENT '数据库引擎：mysql/postgresql/mongodb/sqlserver等',
    `version` varchar(32) NOT NULL DEFAULT '' COMMENT '数据库版本',
    `instance_class` varchar(64) NOT NULL DEFAULT '' COMMENT '实例规格',
    `storage_size` int(11) DEFAULT 0 COMMENT '存储大小(GB)',
    `storage_type` varchar(32) DEFAULT '' COMMENT '存储类型：ssd/hdd等',
    `connection_string` varchar(512) DEFAULT '' COMMENT '连接字符串',
    `port` int(11) DEFAULT 3306 COMMENT '端口',
    `vpc_id` varchar(128) DEFAULT '' COMMENT 'VPC ID',
    `subnet_id` varchar(128) DEFAULT '' COMMENT '子网ID',
    `security_group_ids` text COMMENT '安全组ID列表(JSON)',
    `backup_retention_days` int(11) DEFAULT 7 COMMENT '备份保留天数',
    `maintenance_window` varchar(64) DEFAULT '' COMMENT '维护窗口',
    `multi_az` tinyint(1) DEFAULT 0 COMMENT '是否多可用区部署',
    `status` varchar(32) DEFAULT '' COMMENT '状态：running/stopped/creating等',
    `region` varchar(64) NOT NULL COMMENT '地域',
    `zone` varchar(64) DEFAULT '' COMMENT '可用区',
    `cloud_provider` varchar(32) NOT NULL COMMENT '云厂商：aliyun/tencent/ctyun等',
    `spec_info` text COMMENT '规格详细信息(JSON)',
    `network_info` text COMMENT '网络详细信息(JSON)',
    `tags` text COMMENT '标签信息(JSON)',
    `raw_data` text COMMENT '原始数据(JSON)',
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_cloud_id_provider` (`cloud_id`, `cloud_provider`),
    KEY `idx_name` (`name`),
    KEY `idx_engine` (`engine`),
    KEY `idx_region` (`region`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='云数据库资源表';

-- 创建云缓存资源表
CREATE TABLE IF NOT EXISTS `cloud_cache` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT,
    `cloud_id` varchar(128) NOT NULL COMMENT '云端缓存ID',
    `name` varchar(256) NOT NULL COMMENT '缓存名称',
    `engine` varchar(64) NOT NULL COMMENT '缓存引擎：redis/memcached等',
    `version` varchar(32) NOT NULL DEFAULT '' COMMENT '引擎版本',
    `instance_class` varchar(64) NOT NULL DEFAULT '' COMMENT '实例规格',
    `memory_size` int(11) DEFAULT 0 COMMENT '内存大小(MB)',
    `shard_count` int(11) DEFAULT 1 COMMENT '分片数量',
    `replica_count` int(11) DEFAULT 0 COMMENT '副本数量',
    `connection_string` varchar(512) DEFAULT '' COMMENT '连接字符串',
    `port` int(11) DEFAULT 6379 COMMENT '端口',
    `vpc_id` varchar(128) DEFAULT '' COMMENT 'VPC ID',
    `subnet_id` varchar(128) DEFAULT '' COMMENT '子网ID',
    `security_group_ids` text COMMENT '安全组ID列表(JSON)',
    `auth_enabled` tinyint(1) DEFAULT 0 COMMENT '是否启用认证',
    `ssl_enabled` tinyint(1) DEFAULT 0 COMMENT '是否启用SSL',
    `backup_enabled` tinyint(1) DEFAULT 0 COMMENT '是否启用备份',
    `status` varchar(32) DEFAULT '' COMMENT '状态：running/stopped/creating等',
    `region` varchar(64) NOT NULL COMMENT '地域',
    `zone` varchar(64) DEFAULT '' COMMENT '可用区',
    `cloud_provider` varchar(32) NOT NULL COMMENT '云厂商：aliyun/tencent/ctyun等',
    `spec_info` text COMMENT '规格详细信息(JSON)',
    `network_info` text COMMENT '网络详细信息(JSON)',
    `tags` text COMMENT '标签信息(JSON)',
    `raw_data` text COMMENT '原始数据(JSON)',
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_cloud_id_provider` (`cloud_id`, `cloud_provider`),
    KEY `idx_name` (`name`),
    KEY `idx_engine` (`engine`),
    KEY `idx_region` (`region`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='云缓存资源表';

-- 创建云负载均衡资源表
CREATE TABLE IF NOT EXISTS `cloud_loadbalancer` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT,
    `cloud_id` varchar(128) NOT NULL COMMENT '云端负载均衡ID',
    `name` varchar(256) NOT NULL COMMENT '负载均衡名称',
    `type` varchar(32) NOT NULL DEFAULT '' COMMENT '类型：application/network/classic',
    `scheme` varchar(32) NOT NULL DEFAULT '' COMMENT '方案：internet-facing/internal',
    `ip_address` varchar(128) DEFAULT '' COMMENT 'IP地址',
    `dns_name` varchar(256) DEFAULT '' COMMENT 'DNS名称',
    `bandwidth` int(11) DEFAULT 0 COMMENT '带宽(Mbps)',
    `vpc_id` varchar(128) DEFAULT '' COMMENT 'VPC ID',
    `subnet_ids` text COMMENT '子网ID列表(JSON)',
    `security_group_ids` text COMMENT '安全组ID列表(JSON)',
    `listeners` text COMMENT '监听器配置(JSON)',
    `backend_servers` text COMMENT '后端服务器列表(JSON)',
    `health_check` text COMMENT '健康检查配置(JSON)',
    `ssl_certificates` text COMMENT 'SSL证书信息(JSON)',
    `status` varchar(32) DEFAULT '' COMMENT '状态：active/inactive/creating等',
    `region` varchar(64) NOT NULL COMMENT '地域',
    `zone` varchar(64) DEFAULT '' COMMENT '可用区',
    `cloud_provider` varchar(32) NOT NULL COMMENT '云厂商：aliyun/tencent/ctyun等',
    `spec_info` text COMMENT '规格详细信息(JSON)',
    `network_info` text COMMENT '网络详细信息(JSON)',
    `tags` text COMMENT '标签信息(JSON)',
    `raw_data` text COMMENT '原始数据(JSON)',
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_cloud_id_provider` (`cloud_id`, `cloud_provider`),
    KEY `idx_name` (`name`),
    KEY `idx_type` (`type`),
    KEY `idx_region` (`region`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='云负载均衡资源表';

-- 创建云存储资源表
CREATE TABLE IF NOT EXISTS `cloud_storage` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT,
    `cloud_id` varchar(128) NOT NULL COMMENT '云端存储ID',
    `name` varchar(256) NOT NULL COMMENT '存储名称',
    `type` varchar(32) NOT NULL DEFAULT '' COMMENT '存储类型：oss/cos/obs/block/file',
    `storage_class` varchar(32) DEFAULT '' COMMENT '存储类别：standard/ia/archive等',
    `size` bigint(20) DEFAULT 0 COMMENT '存储大小(GB)',
    `used_size` bigint(20) DEFAULT 0 COMMENT '已使用大小(GB)',
    `bucket_name` varchar(256) DEFAULT '' COMMENT '存储桶名称',
    `endpoint` varchar(256) DEFAULT '' COMMENT '访问端点',
    `access_control` varchar(32) DEFAULT '' COMMENT '访问控制：private/public-read等',
    `versioning_enabled` tinyint(1) DEFAULT 0 COMMENT '是否启用版本控制',
    `encryption_enabled` tinyint(1) DEFAULT 0 COMMENT '是否启用加密',
    `lifecycle_rules` text COMMENT '生命周期规则(JSON)',
    `cors_rules` text COMMENT 'CORS规则(JSON)',
    `status` varchar(32) DEFAULT '' COMMENT '状态：active/inactive等',
    `region` varchar(64) NOT NULL COMMENT '地域',
    `zone` varchar(64) DEFAULT '' COMMENT '可用区',
    `cloud_provider` varchar(32) NOT NULL COMMENT '云厂商：aliyun/tencent/ctyun等',
    `spec_info` text COMMENT '规格详细信息(JSON)',
    `network_info` text COMMENT '网络详细信息(JSON)',
    `tags` text COMMENT '标签信息(JSON)',
    `raw_data` text COMMENT '原始数据(JSON)',
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_cloud_id_provider` (`cloud_id`, `cloud_provider`),
    KEY `idx_name` (`name`),
    KEY `idx_type` (`type`),
    KEY `idx_region` (`region`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='云存储资源表';

-- 创建云网络资源表
CREATE TABLE IF NOT EXISTS `cloud_network` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT,
    `cloud_id` varchar(128) NOT NULL COMMENT '云端网络ID',
    `name` varchar(256) NOT NULL COMMENT '网络名称',
    `type` varchar(32) NOT NULL DEFAULT '' COMMENT '网络类型：vpc/subnet/security_group/nat_gateway等',
    `cidr_block` varchar(64) DEFAULT '' COMMENT 'CIDR块',
    `vpc_id` varchar(128) DEFAULT '' COMMENT '所属VPC ID',
    `subnet_id` varchar(128) DEFAULT '' COMMENT '所属子网ID',
    `route_table_id` varchar(128) DEFAULT '' COMMENT '路由表ID',
    `internet_gateway_id` varchar(128) DEFAULT '' COMMENT '互联网网关ID',
    `nat_gateway_id` varchar(128) DEFAULT '' COMMENT 'NAT网关ID',
    `rules` text COMMENT '规则配置(JSON，如安全组规则)',
    `routes` text COMMENT '路由配置(JSON)',
    `is_default` tinyint(1) DEFAULT 0 COMMENT '是否为默认网络',
    `status` varchar(32) DEFAULT '' COMMENT '状态：available/pending等',
    `region` varchar(64) NOT NULL COMMENT '地域',
    `zone` varchar(64) DEFAULT '' COMMENT '可用区',
    `cloud_provider` varchar(32) NOT NULL COMMENT '云厂商：aliyun/tencent/ctyun等',
    `spec_info` text COMMENT '规格详细信息(JSON)',
    `network_info` text COMMENT '网络详细信息(JSON)',
    `tags` text COMMENT '标签信息(JSON)',
    `raw_data` text COMMENT '原始数据(JSON)',
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_cloud_id_provider` (`cloud_id`, `cloud_provider`),
    KEY `idx_name` (`name`),
    KEY `idx_type` (`type`),
    KEY `idx_vpc_id` (`vpc_id`),
    KEY `idx_region` (`region`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='云网络资源表';

-- 创建通用云资源表
CREATE TABLE IF NOT EXISTS `cloud_resource_generic` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT,
    `cloud_id` varchar(128) NOT NULL COMMENT '云端资源ID',
    `name` varchar(256) NOT NULL COMMENT '资源名称',
    `resource_type` varchar(64) NOT NULL COMMENT '资源类型：如cdn/domain/certificate等',
    `resource_subtype` varchar(64) DEFAULT '' COMMENT '资源子类型',
    `description` text COMMENT '资源描述',
    `configuration` text COMMENT '资源配置信息(JSON)',
    `endpoints` text COMMENT '访问端点信息(JSON)',
    `dependencies` text COMMENT '依赖资源信息(JSON)',
    `status` varchar(32) DEFAULT '' COMMENT '状态',
    `region` varchar(64) NOT NULL COMMENT '地域',
    `zone` varchar(64) DEFAULT '' COMMENT '可用区',
    `cloud_provider` varchar(32) NOT NULL COMMENT '云厂商：aliyun/tencent/ctyun等',
    `spec_info` text COMMENT '规格详细信息(JSON)',
    `network_info` text COMMENT '网络详细信息(JSON)',
    `tags` text COMMENT '标签信息(JSON)',
    `raw_data` text COMMENT '原始数据(JSON)',
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_cloud_id_provider` (`cloud_id`, `cloud_provider`),
    KEY `idx_name` (`name`),
    KEY `idx_resource_type` (`resource_type`),
    KEY `idx_region` (`region`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='通用云资源表';

-- 创建云资源类型映射表
CREATE TABLE IF NOT EXISTS `cloud_resource_type_mapping` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT,
    `cloud_provider` varchar(32) NOT NULL COMMENT '云厂商：aliyun/tencent/ctyun等',
    `cloud_resource_type` varchar(64) NOT NULL COMMENT '云厂商资源类型',
    `standard_resource_type` varchar(64) NOT NULL COMMENT '标准资源类型',
    `target_table` varchar(64) NOT NULL COMMENT '目标存储表：host/cloud_database/cloud_cache等',
    `category` varchar(32) NOT NULL COMMENT '资源分类：compute/database/cache/network/storage等',
    `description` varchar(256) DEFAULT '' COMMENT '描述',
    `is_active` tinyint(1) DEFAULT 1 COMMENT '是否启用',
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_provider_cloud_type` (`cloud_provider`, `cloud_resource_type`),
    KEY `idx_standard_type` (`standard_resource_type`),
    KEY `idx_target_table` (`target_table`),
    KEY `idx_category` (`category`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='云资源类型映射表';

-- 创建云资源临时表（用于预览和选择）
CREATE TABLE IF NOT EXISTS `cloud_resource_temp` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT,
    `discovery_id` bigint(20) NOT NULL COMMENT '发现记录ID',
    `cloud_id` varchar(128) NOT NULL COMMENT '云端资源ID',
    `name` varchar(256) NOT NULL COMMENT '资源名称',
    `resource_type` varchar(32) NOT NULL COMMENT '资源类型',
    `region` varchar(64) NOT NULL COMMENT '地域',
    `zone` varchar(64) DEFAULT '' COMMENT '可用区',
    `status` varchar(32) DEFAULT '' COMMENT '云端状态',
    `spec_info` text COMMENT '规格信息(JSON)',
    `network_info` text COMMENT '网络信息(JSON)',
    `tags` text COMMENT '标签信息(JSON)',
    `raw_data` text COMMENT '原始数据(JSON)',
    `selected` tinyint(1) DEFAULT 0 COMMENT '是否选中导入：0=未选中，1=选中',
    `imported` tinyint(1) DEFAULT 0 COMMENT '是否已导入：0=未导入，1=已导入',
    -- 重复检测相关字段
    `has_duplicate` tinyint(1) DEFAULT 0 COMMENT '是否有重复：0=无，1=有',
    `duplicate_status` varchar(20) DEFAULT 'NONE' COMMENT '重复状态：NONE/DETECTED/IGNORED/OVERRIDDEN',
    `duplicate_info` text COMMENT '重复信息JSON',
    `conflict_ip` varchar(64) DEFAULT '' COMMENT '冲突的IP地址',
    `existing_device_id` bigint(20) DEFAULT 0 COMMENT '已存在设备ID',
    `existing_device_type` varchar(20) DEFAULT '' COMMENT '已存在设备类型：host/resource',
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_discovery_id` (`discovery_id`),
    KEY `idx_cloud_id` (`cloud_id`),
    KEY `idx_resource_type` (`resource_type`),
    KEY `idx_selected` (`selected`),
    KEY `idx_imported` (`imported`),
    KEY `idx_has_duplicate` (`has_duplicate`),
    KEY `idx_duplicate_status` (`duplicate_status`),
    KEY `idx_existing_device` (`existing_device_type`, `existing_device_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='云资源临时表';

-- 插入默认的资源类型映射数据
INSERT IGNORE INTO `cloud_resource_type_mapping` (`cloud_provider`, `cloud_resource_type`, `standard_resource_type`, `target_table`, `category`, `description`) VALUES
-- 阿里云
('aliyun', 'ecs', 'compute_instance', 'host', 'compute', '阿里云ECS实例'),
('aliyun', 'rds', 'database_instance', 'cloud_database', 'database', '阿里云RDS数据库'),
('aliyun', 'redis', 'cache_instance', 'cloud_cache', 'cache', '阿里云Redis缓存'),
('aliyun', 'slb', 'load_balancer', 'cloud_loadbalancer', 'network', '阿里云负载均衡'),
('aliyun', 'oss', 'object_storage', 'cloud_storage', 'storage', '阿里云对象存储'),
('aliyun', 'vpc', 'virtual_network', 'cloud_network', 'network', '阿里云专有网络'),

-- 腾讯云
('tencent', 'cvm', 'compute_instance', 'host', 'compute', '腾讯云CVM实例'),
('tencent', 'cdb', 'database_instance', 'cloud_database', 'database', '腾讯云CDB数据库'),
('tencent', 'redis', 'cache_instance', 'cloud_cache', 'cache', '腾讯云Redis缓存'),
('tencent', 'clb', 'load_balancer', 'cloud_loadbalancer', 'network', '腾讯云负载均衡'),
('tencent', 'cos', 'object_storage', 'cloud_storage', 'storage', '腾讯云对象存储'),
('tencent', 'vpc', 'virtual_network', 'cloud_network', 'network', '腾讯云私有网络'),

-- 天翼云
('ctyun', 'ecs', 'compute_instance', 'host', 'compute', '天翼云ECS实例'),
('ctyun', 'rds', 'database_instance', 'cloud_database', 'database', '天翼云RDS数据库'),
('ctyun', 'redis', 'cache_instance', 'cloud_cache', 'cache', '天翼云Redis缓存'),
('ctyun', 'elb', 'load_balancer', 'cloud_loadbalancer', 'network', '天翼云负载均衡'),
('ctyun', 'obs', 'object_storage', 'cloud_storage', 'storage', '天翼云对象存储'),
('ctyun', 'vpc', 'virtual_network', 'cloud_network', 'network', '天翼云虚拟私有云'),

-- 金山云
('kingsoft', 'ecs', 'compute_instance', 'host', 'compute', '金山云ECS实例'),
('kingsoft', 'rds', 'database_instance', 'cloud_database', 'database', '金山云RDS数据库'),
('kingsoft', 'redis', 'cache_instance', 'cloud_cache', 'cache', '金山云Redis缓存'),
('kingsoft', 'slb', 'load_balancer', 'cloud_loadbalancer', 'network', '金山云负载均衡'),

-- 火山引擎
('volcano', 'ecs', 'compute_instance', 'host', 'compute', '火山引擎ECS实例'),
('volcano', 'rds', 'database_instance', 'cloud_database', 'database', '火山引擎RDS数据库'),
('volcano', 'redis', 'cache_instance', 'cloud_cache', 'cache', '火山引擎Redis缓存'),
('volcano', 'clb', 'load_balancer', 'cloud_loadbalancer', 'network', '火山引擎负载均衡');

-- ========================================
-- 迁移完成提示
-- ========================================
SELECT 'Cloud resource specialized tables migration completed successfully!' as message;
