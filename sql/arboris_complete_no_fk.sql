set names utf8;

-- 删除并重新创建数据库
drop database if exists arboris_ams;
create database arboris_ams;
use arboris_ams;

-- ========================================
-- AMS 主机管理相关表
-- ========================================

-- 主机表
CREATE TABLE `host` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `sn` char(128) NOT NULL DEFAULT '',
  `ip` char(15) NOT NULL,
  `ident` varchar(128) NOT NULL DEFAULT '',
  `name` varchar(128) NOT NULL DEFAULT '',
  `os_version` varchar(255) NOT NULL DEFAULT '',
  `kernel_version` varchar(255) NOT NULL DEFAULT '',
  `cpu_model` varchar(255) NOT NULL DEFAULT '',
  `cpu` varchar(255) NOT NULL DEFAULT '',
  `mem` varchar(255) NOT NULL DEFAULT '',
  `disk` varchar(255) NOT NULL DEFAULT '',
  `note` varchar(255) NOT NULL DEFAULT 'different with resource note',
  `cate` varchar(32) NOT NULL COMMENT 'host,vm,container,switch',
  `tenant` varchar(128) NOT NULL DEFAULT '',
  `clock` bigint NOT NULL COMMENT 'heartbeat timestamp',
  `gpu` varchar(255) NOT NULL DEFAULT '8',
  `gpu_model` varchar(255) NOT NULL DEFAULT '',
  `model` varchar(255) NOT NULL DEFAULT '""',
  `idc` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '""',
  `zone` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '""',
  `rack` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '""',
  `manufacturer` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '""',
  `data_source` varchar(20) DEFAULT 'MANUAL' COMMENT '数据来源：MANUAL/IMPORT/AUTO_DISCOVERY',
  `has_duplicate` tinyint(1) DEFAULT 0 COMMENT '是否有重复：0=无，1=有',
  `duplicate_status` varchar(20) DEFAULT 'NONE' COMMENT '重复状态：NONE/DETECTED/IGNORED/OVERRIDDEN',
  `duplicate_info` text COMMENT '重复信息JSON',
  `last_discovery_at` timestamp NULL COMMENT '最后发现时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `ip` (`ip`),
  UNIQUE KEY `ident` (`ident`),
  KEY `sn` (`sn`),
  KEY `name` (`name`),
  KEY `tenant` (`tenant`),
  KEY `idx_data_source` (`data_source`),
  KEY `idx_duplicate_status` (`duplicate_status`),
  KEY `idx_has_duplicate` (`has_duplicate`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;

-- 主机字段定义表
CREATE TABLE `host_field` (
    `id` int unsigned not null AUTO_INCREMENT,
    `field_ident` varchar(255) not null comment 'english identity',
    `field_name` varchar(255) not null comment 'chinese name',
    `field_type` varchar(64) not null,
    `field_required` tinyint(1) not null default 0,
    `field_extra` varchar(2048) not null default '',
    `field_cate` varchar(255) not null default 'Default',
    PRIMARY KEY (`id`),
    KEY (`field_cate`, `field_ident`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8;

-- 主机字段值表
CREATE TABLE `host_field_value` (
    `id` int unsigned not null AUTO_INCREMENT,
    `host_id` int unsigned not null,
    `field_ident` varchar(255) not null,
    `field_value` varchar(1024) not null default '',
    PRIMARY KEY (`id`),
    KEY (`host_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8;

-- 主机导入失败数据表
CREATE TABLE `host_import_failed_data` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `import_id` varchar(64) NOT NULL COMMENT '导入批次ID',
  `row_num` int NOT NULL COMMENT '原始CSV行号',
  `row_data` text NOT NULL COMMENT '原始行数据JSON',
  `error_msg` varchar(1024) NOT NULL COMMENT '失败原因',
  `created_at` bigint NOT NULL COMMENT '创建时间戳',
  `expires_at` bigint NOT NULL COMMENT '过期时间戳',
  PRIMARY KEY (`id`),
  KEY `idx_import_id` (`import_id`),
  KEY `idx_expires_at` (`expires_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='主机导入失败数据表';

-- ========================================
-- RDB 用户权限管理相关表
-- ========================================

-- 用户表
CREATE TABLE `user` (
    `id` int unsigned not null AUTO_INCREMENT,
    `uuid` varchar(128) not null comment 'use in cookie',
    `username` varchar(64) not null comment 'login name, cannot rename',
    `password` varchar(128) not null default '',
    `passwords` varchar(512) not null default '',
    `dispname` varchar(32) not null default '' comment 'display name, chinese name',
    `phone` varchar(16) not null default '',
    `email` varchar(64) not null default '',
    `im` varchar(64) not null default '',
    `portrait` varchar(2048) not null default '',
    `intro` varchar(2048) not null default '',
    `organization` varchar(255) not null default '',
    `typ` tinyint(1) not null default 0 comment '0: long-term account; 1: temporary account',
    `status` tinyint(1) not null default 0 comment '0: active, 1: inactive, 2: locked, 3: frozen, 5: writen-off',
    `is_root` tinyint(1) not null,
    `leader_id` int unsigned not null default 0,
    `leader_name` varchar(32) not null default '',
    `login_err_num` int unsigned not null default 0,
    `active_begin` bigint not null default 0,
    `active_end` bigint not null default 0,
    `locked_at` bigint not null default 0,
    `updated_at` bigint not null default 0,
    `pwd_updated_at` bigint not null default 0,
    `logged_at` bigint not null default 0,
    `create_at` timestamp not null default CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY (`username`),
    UNIQUE KEY (`uuid`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8;

-- 用户令牌表
CREATE TABLE `user_token` (
    `user_id` int unsigned not null,
    `username` varchar(128) not null,
    `token` varchar(128) not null,
    KEY (`user_id`),
    KEY (`username`),
    UNIQUE KEY (`token`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8;

-- 邀请表
CREATE TABLE `invite` (
    `id` int unsigned not null AUTO_INCREMENT,
    `token` varchar(128) not null,
    `expire` bigint not null,
    `creator` varchar(32) not null,
    PRIMARY KEY (`id`),
    UNIQUE KEY (`token`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8;

-- 团队表
CREATE TABLE `team` (
    `id` int unsigned not null AUTO_INCREMENT,
    `ident` varchar(255) not null,
    `name` varchar(255) not null default '',
    `note` varchar(255) not null default '',
    `mgmt` int(1) not null comment '0: member manage; 1: admin manage',
    `creator` int unsigned not null,
    `last_updated` timestamp not null default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY (`ident`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8;

-- 团队用户关联表
CREATE TABLE `team_user` (
    `team_id` int unsigned not null,
    `user_id` int unsigned not null,
    `is_admin` tinyint(1) not null,
    KEY (`team_id`),
    KEY (`user_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8;

-- 配置表
CREATE TABLE `configs` (
    `id` int unsigned not null AUTO_INCREMENT,
    `ckey` varchar(255) not null,
    `cval` varchar(255) not null default '',
    `last_updated` timestamp not null default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY (`ckey`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8;

-- 角色表
CREATE TABLE `role` (
    `id` int unsigned not null AUTO_INCREMENT,
    `name` varchar(128) not null default '',
    `note` varchar(255) not null default '',
    `cate` char(6) not null default '' comment 'category: global or local',
    PRIMARY KEY (`id`),
    UNIQUE KEY (`name`,`cate`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8;

-- 角色操作表
CREATE TABLE `role_operation` (
    `id` int unsigned not null AUTO_INCREMENT,
    `role_id` int unsigned not null,
    `operation` varchar(255) not null,
    PRIMARY KEY (`id`),
    KEY (`role_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8;

-- 角色全局用户关联表
CREATE TABLE `role_global_user` (
    `role_id` int unsigned not null,
    `user_id` int unsigned not null,
    KEY (`role_id`),
    KEY (`user_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8;

-- 登录日志表
CREATE TABLE `login_log` (
    `id` int unsigned not null AUTO_INCREMENT,
    `username` varchar(64) not null,
    `client` varchar(128) not null comment 'client ip',
    `clock` bigint not null comment 'login timestamp',
    `loginout` char(3) not null comment 'in or out',
    `err` varchar(128) not null comment 'err msg',
    PRIMARY KEY (`id`),
    KEY (`username`),
    KEY (`clock`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8;

-- 操作日志表
CREATE TABLE `operation_log` (
    `id` bigint unsigned not null AUTO_INCREMENT,
    `username` varchar(64) not null,
    `clock` bigint not null comment 'operation timestamp',
    `res_cl` char(16) not null default '' comment 'resource class',
    `res_id` varchar(128) not null default '',
    `detail` varchar(512) not null,
    PRIMARY KEY (`id`),
    KEY (`clock`),
    KEY (`res_cl`, `res_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8;

-- 白名单表
CREATE TABLE `white_list` (
  `id` bigint unsigned not null AUTO_INCREMENT,
  `start_ip` varchar(32) DEFAULT '0' NOT NULL,
  `end_ip` varchar(32) DEFAULT '0' NOT NULL,
  `start_ip_int` bigint DEFAULT '0' NOT NULL,
  `end_ip_int` bigint DEFAULT '0' NOT NULL,
  `start_time` bigint DEFAULT '0' NOT NULL,
  `end_time` bigint DEFAULT '0' NOT NULL,
  `created_at` bigint DEFAULT '0' NOT NULL,
  `updated_at` bigint DEFAULT '0' NOT NULL,
  `creator` varchar(64) DEFAULT '' NOT NULL,
  `updater` varchar(64) DEFAULT '' NOT NULL,
  PRIMARY KEY (`id`),
  KEY (`start_ip_int`, `end_ip_int`),
  KEY (`start_time`, `end_time`),
  KEY (`created_at`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8;

-- 会话表
CREATE TABLE `session` (
   `sid` char(128) NOT NULL,
   `access_token` char(128) DEFAULT '',
   `username` varchar(64) DEFAULT '',
   `remote_addr` varchar(32) DEFAULT '',
   `created_at` integer unsigned DEFAULT '0',
   `updated_at` integer unsigned DEFAULT '0' NOT NULL,
   PRIMARY KEY (`sid`),
   KEY (`access_token`),
   KEY (`username`),
   KEY (`updated_at`)
) ENGINE = InnoDB DEFAULT CHARACTER SET = utf8;

-- 统计表
CREATE TABLE `stats` (
    `name` varchar(64) not null,
    `value` bigint not null default 0,
    PRIMARY KEY (`name`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8;

-- 节点表
CREATE TABLE `node` (
    `id` int unsigned not null AUTO_INCREMENT,
    `pid` int unsigned not null,
    `ident` varchar(128) not null,
    `name` varchar(255) not null default '',
    `note` varchar(255) not null default '',
    `path` varchar(255) not null comment 'ident1.ident2.ident3',
    `leaf` tinyint(1) not null,
    `cate` char(128) not null default '' comment 'cluster,service,module,department,product...',
    `icon_color` char(7) not null default '' comment 'e.g. #108AC6',
    `icon_char` char(1) not null default '' comment 'cluster->C,service->S,module->M',
    `proxy` tinyint(1) not null default 0 comment '0:myself management, 1:other module management',
    `creator` varchar(64) not null,
    `last_updated` timestamp not null default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY (`path`),
    KEY (`cate`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8;

-- 插入默认节点
insert into node(id, ident, name, note, pid, path, leaf, cate, icon_color, icon_char, proxy, creator)
values (1, 'inner', '内置租户', '用于平台管理视角的资源监控', 0, 'inner', 0, 'tenant', '#de83cb', 'T', 0, 'root');

-- 节点分类表
CREATE TABLE `node_cate` (
    `id` int unsigned not null AUTO_INCREMENT,
    `ident` char(128) not null default '' comment 'cluster,service,module,department,product...',
    `name` varchar(255) not null default '',
    `icon_color` char(7) not null default '' comment 'e.g. #108AC6',
    `protected` tinyint(1) not null default 0 comment 'if =1, cannot delete',
    PRIMARY KEY (`id`),
    KEY (`ident`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8;

-- 插入默认节点分类
insert into node_cate(ident, name, icon_color, protected)
values ('tenant', '租户', '#de83cb', 1);
insert into node_cate(ident, name, icon_color, protected)
values ('organization', '团队', '#ff8e75', 1);
insert into node_cate(ident, name, icon_color, protected)
values ('project', '项目', '#f6bb4a', 1);
insert into node_cate(ident, name, icon_color, protected)
values ('module', '模块', '#6dc448', 1);
insert into node_cate(ident, name, icon_color, protected)
values ('cluster', '集群', '#94c7c6', 1);
insert into node_cate(ident, name, icon_color, protected)
values ('resource', '资源', '#a7aae6', 1);

-- 节点分类字段表
CREATE TABLE `node_cate_field` (
    `id` int unsigned not null AUTO_INCREMENT,
    `cate` char(32) not null default '' comment 'cluster,service,module,department,product...',
    `field_ident` varchar(255) not null comment 'english identity',
    `field_name` varchar(255) not null comment 'chinese name',
    `field_type` varchar(64) not null,
    `field_required` tinyint(1) not null default 0,
    `field_extra` varchar(2048) not null default '',
    `last_updated` timestamp not null default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY (`cate`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8;

-- 节点字段值表
CREATE TABLE `node_field_value` (
    `id` int unsigned not null AUTO_INCREMENT,
    `node_id` int unsigned not null,
    `field_ident` varchar(255) not null,
    `field_value` varchar(1024) not null default '',
    PRIMARY KEY (`id`),
    KEY (`node_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8;

-- 节点回收站表
CREATE TABLE `node_trash` (
    `id` int unsigned not null,
    `pid` int unsigned not null,
    `ident` varchar(128) not null,
    `name` varchar(255) not null default '',
    `note` varchar(255) not null default '',
    `path` varchar(255) not null comment 'ident1.ident2.ident3',
    `leaf` tinyint(1) not null,
    `cate` char(128) not null default '' comment 'cluster,service,module,department,product...',
    `icon_color` char(7) not null default '' comment 'e.g. #108AC6',
    `icon_char` char(1) not null default '' comment 'cluster->C,service->S,module->M',
    `proxy` tinyint(1) not null default 0 comment '0:myself management, 1:other module management',
    `creator` varchar(64) not null,
    `last_updated` timestamp not null default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY (`path`),
    KEY (`cate`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8;

-- 节点管理员表
CREATE TABLE `node_admin` (
    `node_id` int unsigned not null,
    `user_id` int unsigned not null,
    KEY (`node_id`),
    KEY (`user_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8;

-- 资源表
CREATE TABLE `resource` (
    `id` int unsigned not null AUTO_INCREMENT,
    `uuid` varchar(255) not null,
    `ident` varchar(255) not null,
    `name` varchar(255) not null default '',
    `labels` varchar(255) not null default '' comment 'e.g. flavor=2c4g300g,region=bj,os=windows',
    `note` varchar(255) not null default '',
    `extend` varchar(1024) not null default '' comment 'json',
    `cate` varchar(64) not null comment 'host,vm,container,switch,redis,mongo',
    `tenant` varchar(128) not null default '',
    `source_id` BIGINT COMMENT '源数据ID',
    `source_type` VARCHAR(50) COMMENT '资源类型',
    `cloud_provider` varchar(32) DEFAULT '' COMMENT '云厂商',
    `cloud_id` varchar(128) DEFAULT '' COMMENT '云端资源ID',
    `cloud_region` varchar(64) DEFAULT '' COMMENT '云端地域',
    `cloud_zone` varchar(64) DEFAULT '' COMMENT '云端可用区',
    `sync_status` varchar(32) DEFAULT '' COMMENT '同步状态：synced/out_of_sync/error',
    `last_sync_time` timestamp NULL COMMENT '最后同步时间',
    `data_source` varchar(20) DEFAULT 'MANUAL' COMMENT '数据来源：MANUAL/IMPORT/AUTO_DISCOVERY',
    `has_duplicate` tinyint(1) DEFAULT 0 COMMENT '是否有重复：0=无，1=有',
    `duplicate_status` varchar(20) DEFAULT 'NONE' COMMENT '重复状态：NONE/DETECTED/IGNORED/OVERRIDDEN',
    `duplicate_info` text COMMENT '重复信息JSON',
    `last_discovery_at` timestamp NULL COMMENT '最后发现时间',
    `last_updated` timestamp not null default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY (`uuid`),
    UNIQUE KEY (`ident`),
    KEY (`tenant`),
    KEY `idx_cloud_provider` (`cloud_provider`),
    KEY `idx_cloud_id` (`cloud_id`),
    KEY `idx_sync_status` (`sync_status`),
    KEY `idx_data_source` (`data_source`),
    KEY `idx_duplicate_status` (`duplicate_status`),
    KEY `idx_has_duplicate` (`has_duplicate`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8;

-- 节点资源关联表
CREATE TABLE `node_resource` (
    `node_id` int unsigned not null,
    `res_id` int unsigned not null,
    KEY (`node_id`),
    KEY (`res_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8;

-- 节点角色表
CREATE TABLE `node_role` (
    `id` int unsigned not null AUTO_INCREMENT,
    `node_id` int unsigned not null,
    `username` varchar(64) not null,
    `role_id` int unsigned not null,
    PRIMARY KEY (`id`),
    KEY (`node_id`),
    KEY (`role_id`),
    KEY (`username`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8;

-- ========================================
-- 云资源管理相关表
-- ========================================

-- 云厂商配置表
CREATE TABLE `cloud_provider_config` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT,
    `name` varchar(128) NOT NULL COMMENT '配置名称',
    `provider` varchar(32) NOT NULL COMMENT '云厂商：kingsoft/volcano/aliyun/tencent/ctyun',
    `region` varchar(64) NOT NULL COMMENT '地域',
    `access_key` varchar(256) NOT NULL COMMENT '访问密钥(加密存储)',
    `secret_key` varchar(512) NOT NULL COMMENT '密钥(加密存储)',
    `endpoint` varchar(256) DEFAULT '' COMMENT '自定义端点',
    `description` text COMMENT '描述',
    `creator` varchar(64) NOT NULL COMMENT '创建者',
    `status` tinyint(1) DEFAULT 1 COMMENT '状态：1=启用，0=禁用',
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_provider` (`provider`),
    KEY `idx_creator` (`creator`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='云厂商配置表';

-- 云资源发现记录表
CREATE TABLE `cloud_resource_discovery` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT,
    `config_id` bigint(20) NOT NULL COMMENT '云厂商配置ID',
    `resource_type` varchar(32) NOT NULL COMMENT '资源类型：ecs/rds/redis等',
    `discovery_time` timestamp NOT NULL COMMENT '发现时间',
    `total_count` int(11) DEFAULT 0 COMMENT '发现的资源总数',
    `imported_count` int(11) DEFAULT 0 COMMENT '已导入数量',
    `status` varchar(32) DEFAULT 'pending' COMMENT '状态：pending/success/failed',
    `error_message` text COMMENT '错误信息',
    `creator` varchar(64) NOT NULL COMMENT '操作者',
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_config_id` (`config_id`),
    KEY `idx_resource_type` (`resource_type`),
    KEY `idx_discovery_time` (`discovery_time`),
    KEY `idx_status` (`status`),
    KEY `idx_creator` (`creator`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='云资源发现记录表';

-- 云资源临时表（用于预览和选择）
CREATE TABLE `cloud_resource_temp` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT,
    `discovery_id` bigint(20) NOT NULL COMMENT '发现记录ID',
    `cloud_id` varchar(128) NOT NULL COMMENT '云端资源ID',
    `name` varchar(256) NOT NULL COMMENT '资源名称',
    `resource_type` varchar(32) NOT NULL COMMENT '资源类型',
    `region` varchar(64) NOT NULL COMMENT '地域',
    `zone` varchar(64) DEFAULT '' COMMENT '可用区',
    `status` varchar(32) DEFAULT '' COMMENT '云端状态',
    `spec_info` text COMMENT '规格信息(JSON)',
    `network_info` text COMMENT '网络信息(JSON)',
    `tags` text COMMENT '标签信息(JSON)',
    `raw_data` text COMMENT '原始数据(JSON)',
    `selected` tinyint(1) DEFAULT 0 COMMENT '是否选中导入：0=未选中，1=选中',
    `imported` tinyint(1) DEFAULT 0 COMMENT '是否已导入：0=未导入，1=已导入',
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_discovery_id` (`discovery_id`),
    KEY `idx_cloud_id` (`cloud_id`),
    KEY `idx_resource_type` (`resource_type`),
    KEY `idx_selected` (`selected`),
    KEY `idx_imported` (`imported`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='云资源临时表';

-- ========================================
-- 重复检测相关表
-- ========================================

-- 设备重复检测记录表
CREATE TABLE `device_duplicates` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT,
    `device_type` varchar(20) NOT NULL COMMENT '设备类型：host/resource',
    `device_id_1` bigint(20) NOT NULL COMMENT '设备1的ID',
    `device_id_2` bigint(20) NOT NULL COMMENT '设备2的ID',
    `conflict_ip` varchar(45) NOT NULL COMMENT '冲突的IP地址',
    `similarity_score` decimal(3,2) DEFAULT 1.00 COMMENT '相似度评分',
    `status` varchar(20) DEFAULT 'PENDING' COMMENT '状态：PENDING/CONFIRMED/REJECTED',
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `resolved_at` timestamp NULL COMMENT '解决时间',
    `resolved_by` varchar(64) DEFAULT '' COMMENT '解决者',
    `resolution_action` varchar(20) DEFAULT '' COMMENT '解决动作：OVERRIDE/IGNORE/MERGE',
    PRIMARY KEY (`id`),
    KEY `idx_device_type` (`device_type`),
    KEY `idx_device_id_1` (`device_id_1`),
    KEY `idx_device_id_2` (`device_id_2`),
    KEY `idx_conflict_ip` (`conflict_ip`),
    KEY `idx_status` (`status`),
    KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备重复检测记录表';

-- 设备合并历史表
CREATE TABLE `device_merge_history` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT,
    `device_type` varchar(20) NOT NULL COMMENT '设备类型：host/resource',
    `target_device_id` bigint(20) NOT NULL COMMENT '目标设备ID',
    `source_device_ids` text NOT NULL COMMENT '源设备ID列表(JSON)',
    `merge_strategy` text COMMENT '合并策略(JSON)',
    `field_changes` text COMMENT '字段变更记录(JSON)',
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `created_by` varchar(64) NOT NULL COMMENT '操作者',
    PRIMARY KEY (`id`),
    KEY `idx_device_type` (`device_type`),
    KEY `idx_target_device_id` (`target_device_id`),
    KEY `idx_created_at` (`created_at`),
    KEY `idx_created_by` (`created_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备合并历史表';

-- ========================================
-- 初始化配置数据
-- ========================================

-- 插入重复检测相关配置
INSERT IGNORE INTO `configs` (`ckey`, `cval`) VALUES
('duplicate_detection_enabled', 'true'),
('duplicate_detection_prefer_private_ip', 'true'),
('duplicate_detection_fallback_to_public_ip', 'true'),
('duplicate_detection_ignore_empty_ip', 'true'),
('duplicate_detection_auto_ignore_same_source', 'false'),
('duplicate_detection_auto_override_manual_with_discovery', 'false');

-- 数据库创建完成
SELECT 'Arboris database created successfully without foreign keys' as result;
