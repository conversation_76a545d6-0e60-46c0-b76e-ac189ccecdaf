set names utf8;

-- ========================================
-- Arboris 完整数据库结构 (更新版)
-- 包含AMS和RDB的所有必要表，以及新的云资源专门表
-- 无外键约束版本
-- ========================================

-- 删除并重新创建数据库
drop database if exists arboris_ams;
create database arboris_ams;
use arboris_ams;

-- ========================================
-- AMS 主机管理相关表
-- ========================================

-- 主机表 (扩展支持云资源)
CREATE TABLE `host` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `sn` char(128) NOT NULL DEFAULT '',
  `ip` char(15) NOT NULL,
  `ident` varchar(128) NOT NULL DEFAULT '',
  `name` varchar(128) NOT NULL DEFAULT '',
  `os_version` varchar(255) NOT NULL DEFAULT '',
  `kernel_version` varchar(255) NOT NULL DEFAULT '',
  `cpu_model` varchar(255) NOT NULL DEFAULT '',
  `cpu` varchar(255) NOT NULL DEFAULT '',
  `mem` varchar(255) NOT NULL DEFAULT '',
  `disk` varchar(255) NOT NULL DEFAULT '',
  `note` varchar(255) NOT NULL DEFAULT 'different with resource note',
  `cate` varchar(32) NOT NULL COMMENT 'host,vm,container,switch',
  `tenant` varchar(128) NOT NULL DEFAULT '',
  `clock` bigint NOT NULL COMMENT 'heartbeat timestamp',
  `gpu` varchar(255) NOT NULL DEFAULT '8',
  `gpu_model` varchar(255) NOT NULL DEFAULT '',
  `model` varchar(255) NOT NULL DEFAULT '',
  `idc` varchar(100) NOT NULL DEFAULT '',
  `zone` varchar(100) NOT NULL DEFAULT '',
  `rack` varchar(100) NOT NULL DEFAULT '',
  `manufacturer` varchar(128) NOT NULL DEFAULT '',
  -- 云资源相关字段
  `cloud_id` varchar(128) DEFAULT '' COMMENT '云端主机ID',
  `cloud_provider` varchar(32) DEFAULT '' COMMENT '云厂商：aliyun/tencent/ctyun等',
  `cloud_region` varchar(64) DEFAULT '' COMMENT '云端地域',
  `cloud_zone` varchar(64) DEFAULT '' COMMENT '云端可用区',
  `instance_type` varchar(64) DEFAULT '' COMMENT '实例规格类型',
  `image_id` varchar(128) DEFAULT '' COMMENT '镜像ID',
  `vpc_id` varchar(128) DEFAULT '' COMMENT 'VPC ID',
  `subnet_id` varchar(128) DEFAULT '' COMMENT '子网ID',
  `security_group_ids` text COMMENT '安全组ID列表(JSON)',
  `public_ip` varchar(64) DEFAULT '' COMMENT '公网IP',
  `private_ip` varchar(64) DEFAULT '' COMMENT '私网IP',
  `key_pair_name` varchar(128) DEFAULT '' COMMENT '密钥对名称',
  `charge_type` varchar(32) DEFAULT '' COMMENT '计费类型：prepaid/postpaid',
  `expired_time` timestamp NULL COMMENT '到期时间',
  `auto_renew` tinyint(1) DEFAULT 0 COMMENT '是否自动续费',
  `cloud_status` varchar(32) DEFAULT '' COMMENT '云端状态：running/stopped/starting等',
  `cloud_tags` text COMMENT '云端标签信息(JSON)',
  `cloud_raw_data` text COMMENT '云端原始数据(JSON)',
  -- 重复检测相关字段
  `data_source` varchar(20) DEFAULT 'MANUAL' COMMENT '数据来源：MANUAL/IMPORT/AUTO_DISCOVERY',
  `has_duplicate` tinyint(1) DEFAULT 0 COMMENT '是否有重复：0=无，1=有',
  `duplicate_status` varchar(20) DEFAULT 'NONE' COMMENT '重复状态：NONE/DETECTED/IGNORED/OVERRIDDEN',
  `duplicate_info` text COMMENT '重复信息JSON',
  `last_discovery_at` timestamp NULL COMMENT '最后发现时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `ip` (`ip`),
  UNIQUE KEY `ident` (`ident`),
  KEY `sn` (`sn`),
  KEY `name` (`name`),
  KEY `tenant` (`tenant`),
  KEY `idx_cloud_id` (`cloud_id`),
  KEY `idx_cloud_provider` (`cloud_provider`),
  KEY `idx_cloud_region` (`cloud_region`),
  KEY `idx_instance_type` (`instance_type`),
  KEY `idx_cloud_status` (`cloud_status`),
  KEY `idx_data_source` (`data_source`),
  KEY `idx_duplicate_status` (`duplicate_status`),
  KEY `idx_has_duplicate` (`has_duplicate`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 主机字段定义表
CREATE TABLE `host_field` (
    `id` int unsigned not null AUTO_INCREMENT,
    `field_ident` varchar(255) not null comment 'english identity',
    `field_name` varchar(255) not null comment 'chinese name',
    `field_type` varchar(64) not null,
    `field_required` tinyint(1) not null default 0,
    `field_extra` varchar(2048) not null default '',
    `field_cate` varchar(255) not null default 'Default',
    PRIMARY KEY (`id`),
    KEY (`field_cate`, `field_ident`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4;

-- 主机字段值表
CREATE TABLE `host_field_value` (
    `id` int unsigned not null AUTO_INCREMENT,
    `host_id` int unsigned not null,
    `field_ident` varchar(255) not null,
    `field_value` varchar(1024) not null default '',
    PRIMARY KEY (`id`),
    KEY (`host_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4;

-- 主机导入失败数据表
CREATE TABLE `host_import_failed_data` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `import_id` varchar(64) NOT NULL COMMENT '导入批次ID',
  `row_num` int NOT NULL COMMENT '原始CSV行号',
  `row_data` text NOT NULL COMMENT '原始行数据JSON',
  `error_msg` varchar(1024) NOT NULL COMMENT '失败原因',
  `created_at` bigint NOT NULL COMMENT '创建时间戳',
  `expires_at` bigint NOT NULL COMMENT '过期时间戳',
  PRIMARY KEY (`id`),
  KEY `idx_import_id` (`import_id`),
  KEY `idx_expires_at` (`expires_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ========================================
-- 云资源专门表
-- ========================================

-- 云数据库资源表
CREATE TABLE `cloud_database` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT,
    `cloud_id` varchar(128) NOT NULL COMMENT '云端数据库ID',
    `name` varchar(256) NOT NULL COMMENT '数据库名称',
    `engine` varchar(64) NOT NULL COMMENT '数据库引擎：mysql/postgresql/mongodb/sqlserver等',
    `version` varchar(32) NOT NULL DEFAULT '' COMMENT '数据库版本',
    `instance_class` varchar(64) NOT NULL DEFAULT '' COMMENT '实例规格',
    `storage_size` int(11) DEFAULT 0 COMMENT '存储大小(GB)',
    `storage_type` varchar(32) DEFAULT '' COMMENT '存储类型：ssd/hdd等',
    `connection_string` varchar(512) DEFAULT '' COMMENT '连接字符串',
    `port` int(11) DEFAULT 3306 COMMENT '端口',
    `vpc_id` varchar(128) DEFAULT '' COMMENT 'VPC ID',
    `subnet_id` varchar(128) DEFAULT '' COMMENT '子网ID',
    `security_group_ids` text COMMENT '安全组ID列表(JSON)',
    `backup_retention_days` int(11) DEFAULT 7 COMMENT '备份保留天数',
    `maintenance_window` varchar(64) DEFAULT '' COMMENT '维护窗口',
    `multi_az` tinyint(1) DEFAULT 0 COMMENT '是否多可用区部署',
    `status` varchar(32) DEFAULT '' COMMENT '状态：running/stopped/creating等',
    `region` varchar(64) NOT NULL COMMENT '地域',
    `zone` varchar(64) DEFAULT '' COMMENT '可用区',
    `cloud_provider` varchar(32) NOT NULL COMMENT '云厂商：aliyun/tencent/ctyun等',
    `spec_info` text COMMENT '规格详细信息(JSON)',
    `network_info` text COMMENT '网络详细信息(JSON)',
    `tags` text COMMENT '标签信息(JSON)',
    `raw_data` text COMMENT '原始数据(JSON)',
    -- 重复检测相关字段
    `data_source` varchar(20) DEFAULT 'IMPORT' COMMENT '数据来源：MANUAL/IMPORT/AUTO_DISCOVERY',
    `has_duplicate` tinyint(1) DEFAULT 0 COMMENT '是否有重复：0=无，1=有',
    `duplicate_status` varchar(20) DEFAULT 'NONE' COMMENT '重复状态：NONE/DETECTED/IGNORED/OVERRIDDEN',
    `duplicate_info` text COMMENT '重复信息JSON',
    `last_discovery_at` timestamp NULL COMMENT '最后发现时间',
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_cloud_id_provider` (`cloud_id`, `cloud_provider`),
    KEY `idx_name` (`name`),
    KEY `idx_engine` (`engine`),
    KEY `idx_region` (`region`),
    KEY `idx_status` (`status`),
    KEY `idx_data_source` (`data_source`),
    KEY `idx_has_duplicate` (`has_duplicate`),
    KEY `idx_duplicate_status` (`duplicate_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='云数据库资源表';

-- 云缓存资源表
CREATE TABLE `cloud_cache` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT,
    `cloud_id` varchar(128) NOT NULL COMMENT '云端缓存ID',
    `name` varchar(256) NOT NULL COMMENT '缓存名称',
    `engine` varchar(64) NOT NULL COMMENT '缓存引擎：redis/memcached等',
    `version` varchar(32) NOT NULL DEFAULT '' COMMENT '引擎版本',
    `instance_class` varchar(64) NOT NULL DEFAULT '' COMMENT '实例规格',
    `memory_size` int(11) DEFAULT 0 COMMENT '内存大小(MB)',
    `shard_count` int(11) DEFAULT 1 COMMENT '分片数量',
    `replica_count` int(11) DEFAULT 0 COMMENT '副本数量',
    `connection_string` varchar(512) DEFAULT '' COMMENT '连接字符串',
    `port` int(11) DEFAULT 6379 COMMENT '端口',
    `vpc_id` varchar(128) DEFAULT '' COMMENT 'VPC ID',
    `subnet_id` varchar(128) DEFAULT '' COMMENT '子网ID',
    `security_group_ids` text COMMENT '安全组ID列表(JSON)',
    `auth_enabled` tinyint(1) DEFAULT 0 COMMENT '是否启用认证',
    `ssl_enabled` tinyint(1) DEFAULT 0 COMMENT '是否启用SSL',
    `backup_enabled` tinyint(1) DEFAULT 0 COMMENT '是否启用备份',
    `status` varchar(32) DEFAULT '' COMMENT '状态：running/stopped/creating等',
    `region` varchar(64) NOT NULL COMMENT '地域',
    `zone` varchar(64) DEFAULT '' COMMENT '可用区',
    `cloud_provider` varchar(32) NOT NULL COMMENT '云厂商：aliyun/tencent/ctyun等',
    `spec_info` text COMMENT '规格详细信息(JSON)',
    `network_info` text COMMENT '网络详细信息(JSON)',
    `tags` text COMMENT '标签信息(JSON)',
    `raw_data` text COMMENT '原始数据(JSON)',
    -- 重复检测相关字段
    `data_source` varchar(20) DEFAULT 'IMPORT' COMMENT '数据来源：MANUAL/IMPORT/AUTO_DISCOVERY',
    `has_duplicate` tinyint(1) DEFAULT 0 COMMENT '是否有重复：0=无，1=有',
    `duplicate_status` varchar(20) DEFAULT 'NONE' COMMENT '重复状态：NONE/DETECTED/IGNORED/OVERRIDDEN',
    `duplicate_info` text COMMENT '重复信息JSON',
    `last_discovery_at` timestamp NULL COMMENT '最后发现时间',
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_cloud_id_provider` (`cloud_id`, `cloud_provider`),
    KEY `idx_name` (`name`),
    KEY `idx_engine` (`engine`),
    KEY `idx_region` (`region`),
    KEY `idx_status` (`status`),
    KEY `idx_data_source` (`data_source`),
    KEY `idx_has_duplicate` (`has_duplicate`),
    KEY `idx_duplicate_status` (`duplicate_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='云缓存资源表';

-- 云负载均衡资源表
CREATE TABLE `cloud_loadbalancer` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT,
    `cloud_id` varchar(128) NOT NULL COMMENT '云端负载均衡ID',
    `name` varchar(256) NOT NULL COMMENT '负载均衡名称',
    `type` varchar(32) NOT NULL DEFAULT '' COMMENT '类型：application/network/classic',
    `scheme` varchar(32) NOT NULL DEFAULT '' COMMENT '方案：internet-facing/internal',
    `ip_address` varchar(128) DEFAULT '' COMMENT 'IP地址',
    `dns_name` varchar(256) DEFAULT '' COMMENT 'DNS名称',
    `bandwidth` int(11) DEFAULT 0 COMMENT '带宽(Mbps)',
    `vpc_id` varchar(128) DEFAULT '' COMMENT 'VPC ID',
    `subnet_ids` text COMMENT '子网ID列表(JSON)',
    `security_group_ids` text COMMENT '安全组ID列表(JSON)',
    `listeners` text COMMENT '监听器配置(JSON)',
    `backend_servers` text COMMENT '后端服务器列表(JSON)',
    `health_check` text COMMENT '健康检查配置(JSON)',
    `ssl_certificates` text COMMENT 'SSL证书信息(JSON)',
    `status` varchar(32) DEFAULT '' COMMENT '状态：active/inactive/creating等',
    `region` varchar(64) NOT NULL COMMENT '地域',
    `zone` varchar(64) DEFAULT '' COMMENT '可用区',
    `cloud_provider` varchar(32) NOT NULL COMMENT '云厂商：aliyun/tencent/ctyun等',
    `spec_info` text COMMENT '规格详细信息(JSON)',
    `network_info` text COMMENT '网络详细信息(JSON)',
    `tags` text COMMENT '标签信息(JSON)',
    `raw_data` text COMMENT '原始数据(JSON)',
    -- 重复检测相关字段
    `data_source` varchar(20) DEFAULT 'IMPORT' COMMENT '数据来源：MANUAL/IMPORT/AUTO_DISCOVERY',
    `has_duplicate` tinyint(1) DEFAULT 0 COMMENT '是否有重复：0=无，1=有',
    `duplicate_status` varchar(20) DEFAULT 'NONE' COMMENT '重复状态：NONE/DETECTED/IGNORED/OVERRIDDEN',
    `duplicate_info` text COMMENT '重复信息JSON',
    `last_discovery_at` timestamp NULL COMMENT '最后发现时间',
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_cloud_id_provider` (`cloud_id`, `cloud_provider`),
    KEY `idx_name` (`name`),
    KEY `idx_type` (`type`),
    KEY `idx_region` (`region`),
    KEY `idx_status` (`status`),
    KEY `idx_data_source` (`data_source`),
    KEY `idx_has_duplicate` (`has_duplicate`),
    KEY `idx_duplicate_status` (`duplicate_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='云负载均衡资源表';

-- 云存储资源表
CREATE TABLE `cloud_storage` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT,
    `cloud_id` varchar(128) NOT NULL COMMENT '云端存储ID',
    `name` varchar(256) NOT NULL COMMENT '存储名称',
    `type` varchar(32) NOT NULL DEFAULT '' COMMENT '存储类型：oss/cos/obs/block/file',
    `storage_class` varchar(32) DEFAULT '' COMMENT '存储类别：standard/ia/archive等',
    `size` bigint(20) DEFAULT 0 COMMENT '存储大小(GB)',
    `used_size` bigint(20) DEFAULT 0 COMMENT '已使用大小(GB)',
    `bucket_name` varchar(256) DEFAULT '' COMMENT '存储桶名称',
    `endpoint` varchar(256) DEFAULT '' COMMENT '访问端点',
    `access_control` varchar(32) DEFAULT '' COMMENT '访问控制：private/public-read等',
    `versioning_enabled` tinyint(1) DEFAULT 0 COMMENT '是否启用版本控制',
    `encryption_enabled` tinyint(1) DEFAULT 0 COMMENT '是否启用加密',
    `lifecycle_rules` text COMMENT '生命周期规则(JSON)',
    `cors_rules` text COMMENT 'CORS规则(JSON)',
    `status` varchar(32) DEFAULT '' COMMENT '状态：active/inactive等',
    `region` varchar(64) NOT NULL COMMENT '地域',
    `zone` varchar(64) DEFAULT '' COMMENT '可用区',
    `cloud_provider` varchar(32) NOT NULL COMMENT '云厂商：aliyun/tencent/ctyun等',
    `spec_info` text COMMENT '规格详细信息(JSON)',
    `network_info` text COMMENT '网络详细信息(JSON)',
    `tags` text COMMENT '标签信息(JSON)',
    `raw_data` text COMMENT '原始数据(JSON)',
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_cloud_id_provider` (`cloud_id`, `cloud_provider`),
    KEY `idx_name` (`name`),
    KEY `idx_type` (`type`),
    KEY `idx_region` (`region`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='云存储资源表';

-- 云网络资源表
CREATE TABLE `cloud_network` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT,
    `cloud_id` varchar(128) NOT NULL COMMENT '云端网络ID',
    `name` varchar(256) NOT NULL COMMENT '网络名称',
    `type` varchar(32) NOT NULL DEFAULT '' COMMENT '网络类型：vpc/subnet/security_group/nat_gateway等',
    `cidr_block` varchar(64) DEFAULT '' COMMENT 'CIDR块',
    `vpc_id` varchar(128) DEFAULT '' COMMENT '所属VPC ID',
    `subnet_id` varchar(128) DEFAULT '' COMMENT '所属子网ID',
    `route_table_id` varchar(128) DEFAULT '' COMMENT '路由表ID',
    `internet_gateway_id` varchar(128) DEFAULT '' COMMENT '互联网网关ID',
    `nat_gateway_id` varchar(128) DEFAULT '' COMMENT 'NAT网关ID',
    `rules` text COMMENT '规则配置(JSON，如安全组规则)',
    `routes` text COMMENT '路由配置(JSON)',
    `is_default` tinyint(1) DEFAULT 0 COMMENT '是否为默认网络',
    `status` varchar(32) DEFAULT '' COMMENT '状态：available/pending等',
    `region` varchar(64) NOT NULL COMMENT '地域',
    `zone` varchar(64) DEFAULT '' COMMENT '可用区',
    `cloud_provider` varchar(32) NOT NULL COMMENT '云厂商：aliyun/tencent/ctyun等',
    `spec_info` text COMMENT '规格详细信息(JSON)',
    `network_info` text COMMENT '网络详细信息(JSON)',
    `tags` text COMMENT '标签信息(JSON)',
    `raw_data` text COMMENT '原始数据(JSON)',
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_cloud_id_provider` (`cloud_id`, `cloud_provider`),
    KEY `idx_name` (`name`),
    KEY `idx_type` (`type`),
    KEY `idx_vpc_id` (`vpc_id`),
    KEY `idx_region` (`region`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='云网络资源表';

-- 通用云资源表
CREATE TABLE `cloud_resource_generic` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT,
    `cloud_id` varchar(128) NOT NULL COMMENT '云端资源ID',
    `name` varchar(256) NOT NULL COMMENT '资源名称',
    `resource_type` varchar(64) NOT NULL COMMENT '资源类型：如cdn/domain/certificate等',
    `resource_subtype` varchar(64) DEFAULT '' COMMENT '资源子类型',
    `description` text COMMENT '资源描述',
    `configuration` text COMMENT '资源配置信息(JSON)',
    `endpoints` text COMMENT '访问端点信息(JSON)',
    `dependencies` text COMMENT '依赖资源信息(JSON)',
    `status` varchar(32) DEFAULT '' COMMENT '状态',
    `region` varchar(64) NOT NULL COMMENT '地域',
    `zone` varchar(64) DEFAULT '' COMMENT '可用区',
    `cloud_provider` varchar(32) NOT NULL COMMENT '云厂商：aliyun/tencent/ctyun等',
    `spec_info` text COMMENT '规格详细信息(JSON)',
    `network_info` text COMMENT '网络详细信息(JSON)',
    `tags` text COMMENT '标签信息(JSON)',
    `raw_data` text COMMENT '原始数据(JSON)',
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_cloud_id_provider` (`cloud_id`, `cloud_provider`),
    KEY `idx_name` (`name`),
    KEY `idx_resource_type` (`resource_type`),
    KEY `idx_region` (`region`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='通用云资源表';

-- ========================================
-- 云资源管理相关表
-- ========================================

-- 云厂商配置表
CREATE TABLE `cloud_provider_config` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT,
    `name` varchar(128) NOT NULL COMMENT '配置名称',
    `provider` varchar(32) NOT NULL COMMENT '云厂商：kingsoft/volcano/aliyun/tencent/ctyun',
    `region` varchar(64) NOT NULL COMMENT '地域',
    `access_key` varchar(256) NOT NULL COMMENT '访问密钥(加密存储)',
    `secret_key` varchar(512) NOT NULL COMMENT '密钥(加密存储)',
    `endpoint` varchar(256) DEFAULT '' COMMENT '自定义端点',
    `description` text COMMENT '描述',
    `creator` varchar(64) NOT NULL COMMENT '创建者',
    `status` tinyint(1) DEFAULT 1 COMMENT '状态：1=启用，0=禁用',
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_provider` (`provider`),
    KEY `idx_creator` (`creator`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='云厂商配置表';

-- 云资源发现记录表
CREATE TABLE `cloud_resource_discovery` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT,
    `config_id` bigint(20) NOT NULL COMMENT '云厂商配置ID',
    `resource_type` varchar(32) NOT NULL COMMENT '资源类型：ecs/rds/redis等',
    `discovery_time` timestamp NOT NULL COMMENT '发现时间',
    `total_count` int(11) DEFAULT 0 COMMENT '发现的资源总数',
    `imported_count` int(11) DEFAULT 0 COMMENT '已导入数量',
    `status` varchar(32) DEFAULT 'pending' COMMENT '状态：pending/success/failed',
    `error_message` text COMMENT '错误信息',
    `creator` varchar(64) NOT NULL COMMENT '操作者',
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_config_id` (`config_id`),
    KEY `idx_resource_type` (`resource_type`),
    KEY `idx_status` (`status`),
    KEY `idx_creator` (`creator`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='云资源发现记录表';

-- 云资源临时表（用于预览和选择）
CREATE TABLE `cloud_resource_temp` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT,
    `discovery_id` bigint(20) NOT NULL COMMENT '发现记录ID',
    `cloud_id` varchar(128) NOT NULL COMMENT '云端资源ID',
    `name` varchar(256) NOT NULL COMMENT '资源名称',
    `resource_type` varchar(32) NOT NULL COMMENT '资源类型',
    `region` varchar(64) NOT NULL COMMENT '地域',
    `zone` varchar(64) DEFAULT '' COMMENT '可用区',
    `status` varchar(32) DEFAULT '' COMMENT '云端状态',
    `spec_info` text COMMENT '规格信息(JSON)',
    `network_info` text COMMENT '网络信息(JSON)',
    `tags` text COMMENT '标签信息(JSON)',
    `raw_data` text COMMENT '原始数据(JSON)',
    `selected` tinyint(1) DEFAULT 0 COMMENT '是否选中导入：0=未选中，1=选中',
    `imported` tinyint(1) DEFAULT 0 COMMENT '是否已导入：0=未导入，1=已导入',
    -- 重复检测相关字段
    `has_duplicate` tinyint(1) DEFAULT 0 COMMENT '是否有重复：0=无，1=有',
    `duplicate_status` varchar(20) DEFAULT 'NONE' COMMENT '重复状态：NONE/DETECTED/IGNORED/OVERRIDDEN',
    `duplicate_info` text COMMENT '重复信息JSON',
    `conflict_ip` varchar(64) DEFAULT '' COMMENT '冲突的IP地址',
    `existing_device_id` bigint(20) DEFAULT 0 COMMENT '已存在设备ID',
    `existing_device_type` varchar(20) DEFAULT '' COMMENT '已存在设备类型：host/resource',
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_discovery_id` (`discovery_id`),
    KEY `idx_cloud_id` (`cloud_id`),
    KEY `idx_resource_type` (`resource_type`),
    KEY `idx_selected` (`selected`),
    KEY `idx_imported` (`imported`),
    KEY `idx_has_duplicate` (`has_duplicate`),
    KEY `idx_duplicate_status` (`duplicate_status`),
    KEY `idx_existing_device` (`existing_device_type`, `existing_device_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='云资源临时表';

-- 云资源类型映射表
CREATE TABLE `cloud_resource_type_mapping` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT,
    `cloud_provider` varchar(32) NOT NULL COMMENT '云厂商：aliyun/tencent/ctyun等',
    `cloud_resource_type` varchar(64) NOT NULL COMMENT '云厂商资源类型',
    `standard_resource_type` varchar(64) NOT NULL COMMENT '标准资源类型',
    `target_table` varchar(64) NOT NULL COMMENT '目标存储表：host/cloud_database/cloud_cache等',
    `category` varchar(32) NOT NULL COMMENT '资源分类：compute/database/cache/network/storage等',
    `description` varchar(256) DEFAULT '' COMMENT '描述',
    `is_active` tinyint(1) DEFAULT 1 COMMENT '是否启用',
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_provider_cloud_type` (`cloud_provider`, `cloud_resource_type`),
    KEY `idx_standard_type` (`standard_resource_type`),
    KEY `idx_target_table` (`target_table`),
    KEY `idx_category` (`category`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='云资源类型映射表';

-- ========================================
-- 切换到RDB数据库
-- ========================================

drop database if exists arboris_rdb;
create database arboris_rdb;
use arboris_rdb;

-- ========================================
-- RDB 用户权限管理相关表
-- ========================================

-- 用户表
CREATE TABLE `user` (
    `id` int unsigned not null AUTO_INCREMENT,
    `uuid` varchar(128) not null comment 'use in cookie',
    `username` varchar(64) not null comment 'login name, cannot rename',
    `password` varchar(128) not null default '',
    `passwords` varchar(512) not null default '',
    `dispname` varchar(32) not null default '' comment 'display name, chinese name',
    `phone` varchar(16) not null default '',
    `email` varchar(64) not null default '',
    `im` varchar(64) not null default '',
    `portrait` varchar(2048) not null default '',
    `intro` varchar(2048) not null default '',
    `organization` varchar(255) not null default '',
    `status` tinyint(1) not null default 0 comment '0:active, 1:inactive, 2:locked, 3:frozen, 4:writen-off',
    `is_root` tinyint(1) not null default 0,
    `create_at` bigint not null default 0,
    `create_by` varchar(64) not null default '',
    `update_at` bigint not null default 0,
    `update_by` varchar(64) not null default '',
    PRIMARY KEY (`id`),
    UNIQUE KEY (`username`),
    KEY (`dispname`),
    KEY (`phone`),
    KEY (`email`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4;

-- 用户组表
CREATE TABLE `user_group` (
    `id` int unsigned not null AUTO_INCREMENT,
    `name` varchar(128) not null default '',
    `note` varchar(255) not null default '',
    `create_at` bigint not null default 0,
    `create_by` varchar(64) not null default '',
    `update_at` bigint not null default 0,
    `update_by` varchar(64) not null default '',
    PRIMARY KEY (`id`),
    KEY (`name`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4;

-- 用户组成员表
CREATE TABLE `user_group_member` (
    `group_id` int unsigned not null,
    `user_id` int unsigned not null,
    KEY (`group_id`),
    KEY (`user_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4;

-- 角色表
CREATE TABLE `role` (
    `id` int unsigned not null AUTO_INCREMENT,
    `name` varchar(128) not null default '',
    `note` varchar(255) not null default '',
    `cate` char(6) not null default '' comment 'category: global or local',
    PRIMARY KEY (`id`),
    UNIQUE KEY (`name`,`cate`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4;

-- 角色操作表
CREATE TABLE `role_operation` (
    `role_id` int unsigned not null,
    `operation` varchar(255) not null,
    KEY (`role_id`),
    KEY (`operation`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4;

-- 会话表
CREATE TABLE `session` (
   `sid` char(128) NOT NULL,
   `access_token` char(128) DEFAULT '',
   `username` varchar(64) DEFAULT '',
   `remote_addr` varchar(32) DEFAULT '',
   `created_at` integer unsigned DEFAULT '0',
   `updated_at` integer unsigned DEFAULT '0' NOT NULL,
   PRIMARY KEY (`sid`),
   KEY (`access_token`),
   KEY (`username`),
   KEY (`updated_at`)
) ENGINE = InnoDB DEFAULT CHARACTER SET = utf8mb4;

-- ========================================
-- RDB 节点资源管理相关表
-- ========================================

-- 节点表
CREATE TABLE `node` (
    `id` int unsigned not null AUTO_INCREMENT,
    `pid` int unsigned not null,
    `ident` varchar(128) not null,
    `name` varchar(255) not null default '',
    `note` varchar(255) not null default '',
    `path` varchar(255) not null comment 'ident1.ident2.ident3',
    `leaf` tinyint(1) not null,
    `cate` char(128) not null default '' comment 'cluster,service,module,department,product...',
    `icon_color` char(7) not null default '' comment 'e.g. #108AC6',
    `icon_char` char(1) not null default '' comment 'cluster->C,service->S,module->M',
    `proxy` tinyint(1) not null default 0 comment '0:myself management, 1:other module management',
    `creator` varchar(64) not null,
    `last_updated` timestamp not null default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY (`path`),
    KEY (`cate`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4;

-- 节点管理员表
CREATE TABLE `node_admin` (
    `node_id` int unsigned not null,
    `user_id` int unsigned not null,
    KEY (`node_id`),
    KEY (`user_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4;

-- 资源表
CREATE TABLE `resource` (
    `id` int unsigned not null AUTO_INCREMENT,
    `uuid` varchar(255) not null,
    `ident` varchar(255) not null,
    `name` varchar(255) not null default '',
    `labels` varchar(255) not null default '' comment 'e.g. flavor=2c4g300g,region=bj,os=windows',
    `note` varchar(255) not null default '',
    `extend` varchar(1024) not null default '' comment 'json',
    `cate` varchar(64) not null comment 'host,vm,container,switch,redis,mongo',
    `tenant` varchar(128) not null default '',
    `source_id` BIGINT COMMENT '源数据ID',
    `source_type` VARCHAR(50) COMMENT '资源类型',
    `last_updated` timestamp not null default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY (`uuid`),
    UNIQUE KEY (`ident`),
    KEY (`tenant`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4;

-- 节点资源关联表
CREATE TABLE `node_resource` (
    `node_id` int unsigned not null,
    `res_id` int unsigned not null,
    KEY (`node_id`),
    KEY (`res_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4;

-- 节点角色表
CREATE TABLE `node_role` (
    `id` int unsigned not null AUTO_INCREMENT,
    `node_id` int unsigned not null,
    `username` varchar(64) not null,
    `role_id` int unsigned not null,
    PRIMARY KEY (`id`),
    KEY (`node_id`),
    KEY (`role_id`),
    KEY (`username`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4;

-- 节点分类表
CREATE TABLE `node_cate` (
    `id` int unsigned not null AUTO_INCREMENT,
    `ident` char(128) not null default '' comment 'cluster,service,module,department,product...',
    `name` varchar(255) not null default '',
    `icon_color` char(7) not null default '' comment 'e.g. #108AC6',
    `protected` tinyint(1) not null default 0 comment 'if =1, cannot delete',
    PRIMARY KEY (`id`),
    KEY (`ident`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4;

-- ========================================
-- 初始数据插入
-- ========================================

-- 插入默认节点
insert into node(id, ident, name, note, pid, path, leaf, cate, icon_color, icon_char, proxy, creator)
values (1, 'inner', '内置租户', '用于平台管理视角的资源监控', 0, 'inner', 0, 'tenant', '#de83cb', 'T', 0, 'root');

-- 插入默认节点分类
insert into node_cate(ident, name, icon_color, protected) values
('tenant', '租户', '#de83cb', 1),
('cluster', '集群', '#108AC6', 1),
('service', '服务', '#52C41A', 1),
('module', '模块', '#FA8C16', 1),
('department', '部门', '#722ED1', 1),
('product', '产品', '#EB2F96', 1);

-- 插入默认角色
insert into role(name, note, cate) values
('Admin', '管理员', 'global'),
('Standard', '标准用户', 'global'),
('Guest', '访客', 'global'),
('NodeAdmin', '节点管理员', 'local'),
('NodeMember', '节点成员', 'local');

-- 插入默认用户 (root用户，密码: root.2020)
insert into user(uuid, username, password, dispname, is_root, create_at, create_by, update_at, update_by) values
('root-uuid-0000-0000-000000000000', 'root', 'pbkdf2_sha256$260000$nV3hXUrrejrz$ozbQzPNOPqAgdAzlHFhXJyNlXJj2gDl9SUbVJ0NuElE=', 'Root User', 1, unix_timestamp(), 'system', unix_timestamp(), 'system');

-- ========================================
-- 切换回AMS数据库插入云资源类型映射数据
-- ========================================

use arboris_ams;

-- 插入默认的资源类型映射数据
INSERT INTO `cloud_resource_type_mapping` (`cloud_provider`, `cloud_resource_type`, `standard_resource_type`, `target_table`, `category`, `description`) VALUES
-- 阿里云
('aliyun', 'ecs', 'compute_instance', 'host', 'compute', '阿里云ECS实例'),
('aliyun', 'rds', 'database_instance', 'cloud_database', 'database', '阿里云RDS数据库'),
('aliyun', 'redis', 'cache_instance', 'cloud_cache', 'cache', '阿里云Redis缓存'),
('aliyun', 'slb', 'load_balancer', 'cloud_loadbalancer', 'network', '阿里云负载均衡'),
('aliyun', 'oss', 'object_storage', 'cloud_storage', 'storage', '阿里云对象存储'),
('aliyun', 'vpc', 'virtual_network', 'cloud_network', 'network', '阿里云专有网络'),

-- 腾讯云
('tencent', 'cvm', 'compute_instance', 'host', 'compute', '腾讯云CVM实例'),
('tencent', 'cdb', 'database_instance', 'cloud_database', 'database', '腾讯云CDB数据库'),
('tencent', 'redis', 'cache_instance', 'cloud_cache', 'cache', '腾讯云Redis缓存'),
('tencent', 'clb', 'load_balancer', 'cloud_loadbalancer', 'network', '腾讯云负载均衡'),
('tencent', 'cos', 'object_storage', 'cloud_storage', 'storage', '腾讯云对象存储'),
('tencent', 'vpc', 'virtual_network', 'cloud_network', 'network', '腾讯云私有网络'),

-- 天翼云
('ctyun', 'ecs', 'compute_instance', 'host', 'compute', '天翼云ECS实例'),
('ctyun', 'rds', 'database_instance', 'cloud_database', 'database', '天翼云RDS数据库'),
('ctyun', 'redis', 'cache_instance', 'cloud_cache', 'cache', '天翼云Redis缓存'),
('ctyun', 'elb', 'load_balancer', 'cloud_loadbalancer', 'network', '天翼云负载均衡'),
('ctyun', 'obs', 'object_storage', 'cloud_storage', 'storage', '天翼云对象存储'),
('ctyun', 'vpc', 'virtual_network', 'cloud_network', 'network', '天翼云虚拟私有云'),

-- 金山云
('kingsoft', 'ecs', 'compute_instance', 'host', 'compute', '金山云ECS实例'),
('kingsoft', 'rds', 'database_instance', 'cloud_database', 'database', '金山云RDS数据库'),
('kingsoft', 'redis', 'cache_instance', 'cloud_cache', 'cache', '金山云Redis缓存'),
('kingsoft', 'slb', 'load_balancer', 'cloud_loadbalancer', 'network', '金山云负载均衡'),

-- 火山引擎
('volcano', 'ecs', 'compute_instance', 'host', 'compute', '火山引擎ECS实例'),
('volcano', 'rds', 'database_instance', 'cloud_database', 'database', '火山引擎RDS数据库'),
('volcano', 'redis', 'cache_instance', 'cloud_cache', 'cache', '火山引擎Redis缓存'),
('volcano', 'clb', 'load_balancer', 'cloud_loadbalancer', 'network', '火山引擎负载均衡');

-- ========================================
-- 完成提示
-- ========================================
SELECT 'Arboris complete database structure created successfully!' as message,
       'AMS database: arboris_ams' as ams_db,
       'RDB database: arboris_rdb' as rdb_db,
       'Cloud resource specialized tables added' as cloud_tables,
       'Default data inserted' as initial_data;
