# 真实云厂商API使用指南

本文档详细说明如何使用真实的云厂商API进行资源发现和管理。

## 🔧 已实现的真实API调用

### ✅ 完全实现
- **阿里云 (Aliyun)** - 使用HTTP客户端调用真实API
- **腾讯云 (Tencent)** - 使用HTTP客户端调用真实API  
- **火山云 (Volcano)** - 使用HTTP客户端调用真实API
- **金山云 (Kingsoft)** - 使用HTTP客户端调用真实API
- **天翼云 (Ctyun)** - 使用HTTP客户端调用真实API

### 🔐 认证方式

| 云厂商 | 认证方式 | 密钥名称 | 签名算法 |
|--------|----------|----------|----------|
| 阿里云 | AccessKey + SecretKey | AccessKeyId, AccessKeySecret | HMAC-SHA1 |
| 腾讯云 | SecretId + SecretKey | SecretId, SecretKey | TC3-HMAC-SHA256 |
| 火山云 | AccessKey + SecretKey | AccessKeyId, SecretAccessKey | HMAC-SHA256 |
| 金山云 | AccessKey + SecretKey | AccessKeyId, SecretAccessKey | HMAC-SHA1 |
| 天翼云 | AccessKey + SecretKey | AccessKeyId, SecretAccessKey | HMAC-SHA256 |

## 🚀 快速开始

### 1. 环境变量配置

创建 `.env` 文件或设置环境变量：

```bash
# 阿里云配置
export ALIYUN_REGION="cn-hangzhou"
export ALIYUN_ACCESS_KEY="your_aliyun_access_key"
export ALIYUN_SECRET_KEY="your_aliyun_secret_key"

# 腾讯云配置
export TENCENT_REGION="ap-beijing"
export TENCENT_SECRET_ID="your_tencent_secret_id"
export TENCENT_SECRET_KEY="your_tencent_secret_key"

# 火山云配置
export VOLCANO_REGION="cn-beijing"
export VOLCANO_ACCESS_KEY="your_volcano_access_key"
export VOLCANO_SECRET_KEY="your_volcano_secret_key"

# 金山云配置
export KINGSOFT_REGION="cn-beijing-6"
export KINGSOFT_ACCESS_KEY="your_kingsoft_access_key"
export KINGSOFT_SECRET_KEY="your_kingsoft_secret_key"

# 天翼云配置
export CTYUN_REGION="cn-bj"
export CTYUN_ACCESS_KEY="your_ctyun_access_key"
export CTYUN_SECRET_KEY="your_ctyun_secret_key"
```

### 2. 运行示例代码

```bash
# 编译示例程序
go build -o cloud_example examples/real_cloud_api_example.go

# 运行示例
./cloud_example
```

### 3. 通过AMS API使用

```bash
# 1. 创建云厂商配置
curl -X POST http://localhost:8080/api/ams-ce/cloud/configs \
  -H "Content-Type: application/json" \
  -H "X-User-Token: your_token" \
  -d '{
    "name": "生产环境-阿里云",
    "provider": "aliyun",
    "region": "cn-hangzhou",
    "access_key": "your_aliyun_access_key",
    "secret_key": "your_aliyun_secret_key",
    "description": "生产环境阿里云配置"
  }'

# 2. 测试连接
curl -X POST http://localhost:8080/api/ams-ce/cloud/configs/1/test \
  -H "X-User-Token: your_token"

# 3. 发现资源
curl -X POST http://localhost:8080/api/ams-ce/cloud/discover \
  -H "Content-Type: application/json" \
  -H "X-User-Token: your_token" \
  -d '{
    "config_id": 1,
    "resource_types": ["ecs"],
    "filters": {
      "status": "Running",
      "zone": "cn-hangzhou-b"
    }
  }'
```

## 📋 支持的API接口

### 阿里云 (Aliyun)

| 资源类型 | API接口 | 实现状态 |
|----------|---------|----------|
| ECS | DescribeInstances | ✅ 已实现 |
| RDS | DescribeDBInstances | 🔄 待实现 |
| Redis | DescribeInstances | 🔄 待实现 |
| SLB | DescribeLoadBalancers | 🔄 待实现 |
| VPC | DescribeVpcs | 🔄 待实现 |

**API端点**: `https://ecs.{region}.aliyuncs.com`

**示例调用**:
```go
config := cloud.ProviderConfig{
    Provider:  "aliyun",
    Region:    "cn-hangzhou",
    AccessKey: "your_access_key",
    SecretKey: "your_secret_key",
}

provider := cloud.NewAliyunProvider(config)
resources, err := provider.DiscoverResources("ecs", map[string]string{
    "status": "Running",
})
```

### 腾讯云 (Tencent)

| 资源类型 | API接口 | 实现状态 |
|----------|---------|----------|
| CVM | DescribeInstances | ✅ 已实现 |
| CDB | DescribeDBInstances | 🔄 待实现 |
| Redis | DescribeInstances | 🔄 待实现 |
| CLB | DescribeLoadBalancers | 🔄 待实现 |
| VPC | DescribeVpcs | 🔄 待实现 |

**API端点**: `https://cvm.tencentcloudapi.com`

**示例调用**:
```go
config := cloud.ProviderConfig{
    Provider:  "tencent",
    Region:    "ap-beijing",
    AccessKey: "your_secret_id",
    SecretKey: "your_secret_key",
}

provider := cloud.NewTencentProvider(config)
resources, err := provider.DiscoverResources("cvm", map[string]string{
    "status": "RUNNING",
})
```

### 火山云 (Volcano)

| 资源类型 | API接口 | 实现状态 |
|----------|---------|----------|
| ECS | DescribeInstances | ✅ 已实现 |
| RDS | DescribeDBInstances | 🔄 待实现 |
| Redis | DescribeInstances | 🔄 待实现 |
| CLB | DescribeLoadBalancers | 🔄 待实现 |

**API端点**: `https://ecs.volcengineapi.com`

### 金山云 (Kingsoft)

| 资源类型 | API接口 | 实现状态 |
|----------|---------|----------|
| ECS | DescribeInstances | ✅ 已实现 |
| RDS | DescribeDBInstances | 🔄 待实现 |
| Redis | DescribeInstances | 🔄 待实现 |
| SLB | DescribeLoadBalancers | 🔄 待实现 |

**API端点**: `https://kec.{region}.api.ksyun.com`

### 天翼云 (Ctyun)

| 资源类型 | API接口 | 实现状态 |
|----------|---------|----------|
| ECS | DescribeInstances | ✅ 已实现 |
| RDS | DescribeDBInstances | 🔄 待实现 |
| Redis | DescribeInstances | 🔄 待实现 |
| ELB | DescribeLoadBalancers | 🔄 待实现 |
| VPC | DescribeVpcs | 🔄 待实现 |

**API端点**: `https://ctyun-api.cn`

## 🔍 过滤器支持

所有云厂商都支持以下过滤器：

| 过滤器 | 说明 | 示例值 |
|--------|------|--------|
| `zone` | 可用区过滤 | `cn-hangzhou-b`, `ap-beijing-1` |
| `status` | 状态过滤 | `Running`, `RUNNING`, `ACTIVE` |
| `name_like` | 名称模糊匹配 | `web`, `prod` |

**使用示例**:
```go
filters := map[string]string{
    "zone":      "cn-hangzhou-b",
    "status":    "Running,Stopped",  // 多个状态用逗号分隔
    "name_like": "web",              // 包含"web"的实例
}

resources, err := provider.DiscoverResources("ecs", filters)
```

## 🛠️ 高级配置

### 自定义端点

```go
config := cloud.ProviderConfig{
    Provider:  "aliyun",
    Region:    "cn-hangzhou",
    AccessKey: "your_access_key",
    SecretKey: "your_secret_key",
    Endpoint:  "https://ecs-vpc.cn-hangzhou.aliyuncs.com", // 自定义端点
}
```

### 超时和重试配置

HTTP客户端默认配置：
- **超时时间**: 30秒
- **重试次数**: 自动重试（由HTTP客户端处理）
- **签名算法**: 根据云厂商自动选择

### 错误处理

```go
resources, err := provider.DiscoverResources("ecs", filters)
if err != nil {
    // 处理不同类型的错误
    switch {
    case strings.Contains(err.Error(), "authentication"):
        log.Printf("认证失败，请检查AccessKey和SecretKey")
    case strings.Contains(err.Error(), "permission"):
        log.Printf("权限不足，请检查账号权限")
    case strings.Contains(err.Error(), "timeout"):
        log.Printf("请求超时，请稍后重试")
    default:
        log.Printf("未知错误: %v", err)
    }
    return
}
```

## 📊 性能优化建议

1. **批量查询**: 使用分页查询大量资源
2. **并发控制**: 避免同时发起过多API请求
3. **缓存结果**: 对不经常变化的资源进行缓存
4. **过滤优化**: 使用精确的过滤条件减少数据传输

## 🔒 安全最佳实践

1. **密钥管理**: 使用环境变量或密钥管理服务
2. **权限最小化**: 只授予必要的API权限
3. **定期轮换**: 定期更换AccessKey和SecretKey
4. **日志审计**: 记录所有API调用日志

## 🚨 故障排查

### 常见错误及解决方案

1. **认证失败**
   - 检查AccessKey和SecretKey是否正确
   - 确认密钥未过期
   - 验证签名算法是否正确

2. **权限不足**
   - 检查云厂商账号权限
   - 确认API权限已开通
   - 验证资源访问权限

3. **网络问题**
   - 检查网络连接
   - 验证API端点可达性
   - 确认防火墙规则

4. **参数错误**
   - 检查地域参数是否正确
   - 验证过滤器参数格式
   - 确认资源类型支持

## 📞 技术支持

如遇到问题，请：

1. 查看详细错误日志
2. 检查网络和权限配置
3. 参考云厂商官方API文档
4. 提交Issue到项目仓库
