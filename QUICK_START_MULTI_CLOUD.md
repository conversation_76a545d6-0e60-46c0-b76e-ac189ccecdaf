# 多云资源管理快速开始指南

本指南将帮助你快速配置和使用多云资源管理功能，支持金山云、火山云、阿里云、腾讯云和天翼云。

## 🚀 快速配置

### 1. 执行数据库迁移

```bash
mysql -u username -p database_name < sql/cloud_resource_migration.sql
```

### 2. 启动AMS服务

```bash
./ams
```

### 3. 配置各云厂商

#### 3.1 配置阿里云

```bash
curl -X POST http://localhost:8080/api/ams-ce/cloud/configs \
  -H "Content-Type: application/json" \
  -H "X-User-Token: your_token" \
  -d '{
    "name": "生产环境-阿里云",
    "provider": "aliyun",
    "region": "cn-hangzhou",
    "access_key": "your_aliyun_access_key",
    "secret_key": "your_aliyun_secret_key",
    "description": "生产环境阿里云配置"
  }'
```

#### 3.2 配置腾讯云

```bash
curl -X POST http://localhost:8080/api/ams-ce/cloud/configs \
  -H "Content-Type: application/json" \
  -H "X-User-Token: your_token" \
  -d '{
    "name": "生产环境-腾讯云",
    "provider": "tencent",
    "region": "ap-beijing",
    "access_key": "your_tencent_secret_id",
    "secret_key": "your_tencent_secret_key",
    "description": "生产环境腾讯云配置"
  }'
```

#### 3.3 配置天翼云

```bash
curl -X POST http://localhost:8080/api/ams-ce/cloud/configs \
  -H "Content-Type: application/json" \
  -H "X-User-Token: your_token" \
  -d '{
    "name": "生产环境-天翼云",
    "provider": "ctyun",
    "region": "cn-bj",
    "access_key": "your_ctyun_access_key",
    "secret_key": "your_ctyun_secret_key",
    "description": "生产环境天翼云配置"
  }'
```

## 🔍 资源发现示例

### 发现阿里云资源

```bash
# 发现阿里云ECS和OSS资源
curl -X POST http://localhost:8080/api/ams-ce/cloud/discover \
  -H "Content-Type: application/json" \
  -H "X-User-Token: your_token" \
  -d '{
    "config_id": 1,
    "resource_types": ["ecs", "oss", "vpc"],
    "filters": {
      "zone": "cn-hangzhou-b",
      "status": ["running"],
      "name_like": "prod"
    }
  }'
```

### 发现腾讯云资源

```bash
# 发现腾讯云CVM和COS资源
curl -X POST http://localhost:8080/api/ams-ce/cloud/discover \
  -H "Content-Type: application/json" \
  -H "X-User-Token: your_token" \
  -d '{
    "config_id": 2,
    "resource_types": ["cvm", "cos", "vpc"],
    "filters": {
      "zone": "ap-beijing-1",
      "status": ["running"],
      "name_like": "prod"
    }
  }'
```

### 发现天翼云资源

```bash
# 发现天翼云ECS和OBS资源
curl -X POST http://localhost:8080/api/ams-ce/cloud/discover \
  -H "Content-Type: application/json" \
  -H "X-User-Token: your_token" \
  -d '{
    "config_id": 3,
    "resource_types": ["ecs", "obs", "vpc"],
    "filters": {
      "zone": "cn-bj-1a",
      "status": ["active"],
      "name_like": "prod"
    }
  }'
```

## 📊 多云资源对比

### 各云厂商资源类型对比

| 功能 | 阿里云 | 腾讯云 | 天翼云 | 金山云 | 火山云 |
|------|--------|--------|--------|--------|--------|
| 云服务器 | ✅ ECS | ✅ CVM | ✅ ECS | ✅ ECS | ✅ ECS |
| 云数据库 | ✅ RDS | ✅ CDB | ✅ RDS | ✅ RDS | ✅ RDS |
| 云缓存 | ✅ Redis | ✅ Redis | ✅ Redis | ✅ Redis | ✅ Redis |
| 负载均衡 | ✅ SLB | ✅ CLB | ✅ ELB | ✅ SLB | ✅ CLB |
| 对象存储 | ✅ OSS | ✅ COS | ✅ OBS | ❌ | ❌ |
| 虚拟网络 | ✅ VPC | ✅ VPC | ✅ VPC | ❌ | ❌ |

### 地域支持对比

| 地域 | 阿里云 | 腾讯云 | 天翼云 | 金山云 | 火山云 |
|------|--------|--------|--------|--------|--------|
| 北京 | cn-beijing | ap-beijing | cn-bj | cn-beijing-6 | cn-beijing |
| 上海 | cn-shanghai | ap-shanghai | cn-sh | cn-shanghai-2 | cn-shanghai |
| 广州 | cn-shenzhen | ap-guangzhou | cn-gz | cn-guangzhou-1 | cn-guangzhou |
| 香港 | cn-hongkong | ap-hongkong | cn-hk | cn-hongkong-2 | cn-hongkong |

## 🛠️ 实际使用场景

### 场景1：多云ECS统一管理

```bash
# 1. 发现所有云厂商的ECS资源
for provider in aliyun tencent ctyun kingsoft volcano; do
  echo "发现${provider}的ECS资源..."
  curl -X POST http://localhost:8080/api/ams-ce/cloud/discover \
    -H "Content-Type: application/json" \
    -H "X-User-Token: your_token" \
    -d "{
      \"config_id\": ${config_id},
      \"resource_types\": [\"ecs\", \"cvm\"],
      \"filters\": {
        \"status\": [\"running\", \"active\"]
      }
    }"
done

# 2. 查看所有发现的资源
curl -X GET http://localhost:8080/api/ams-ce/cloud/discoveries \
  -H "X-User-Token: your_token"

# 3. 批量导入到统一节点
curl -X POST http://localhost:8080/api/ams-ce/cloud/discoveries/1/import \
  -H "Content-Type: application/json" \
  -H "X-User-Token: your_token" \
  -d '{
    "node_id": 100,
    "resource_mapping": {
      "1": {"cate": "server", "tenant": "prod", "note": "阿里云生产服务器"},
      "2": {"cate": "server", "tenant": "prod", "note": "腾讯云生产服务器"}
    }
  }'
```

### 场景2：多云存储资源管理

```bash
# 发现所有云厂商的存储资源
curl -X POST http://localhost:8080/api/ams-ce/cloud/discover \
  -H "Content-Type: application/json" \
  -H "X-User-Token: your_token" \
  -d '{
    "config_id": 1,
    "resource_types": ["oss", "cos", "obs"],
    "filters": {
      "name_like": "backup"
    }
  }'
```

### 场景3：多云网络资源统计

```bash
# 发现所有VPC资源
curl -X POST http://localhost:8080/api/ams-ce/cloud/discover \
  -H "Content-Type: application/json" \
  -H "X-User-Token: your_token" \
  -d '{
    "config_id": 1,
    "resource_types": ["vpc"],
    "filters": {}
  }'
```

## 🔧 高级配置

### 批量配置多个云厂商

创建配置脚本 `setup_multi_cloud.sh`：

```bash
#!/bin/bash

BASE_URL="http://localhost:8080/api/ams-ce"
TOKEN="your_user_token"

# 云厂商配置数组
declare -A PROVIDERS=(
    ["aliyun"]="cn-hangzhou"
    ["tencent"]="ap-beijing"
    ["ctyun"]="cn-bj"
    ["kingsoft"]="cn-beijing-6"
    ["volcano"]="cn-beijing"
)

# 批量创建配置
for provider in "${!PROVIDERS[@]}"; do
    region="${PROVIDERS[$provider]}"
    echo "配置${provider}..."
    
    curl -X POST "${BASE_URL}/cloud/configs" \
      -H "Content-Type: application/json" \
      -H "X-User-Token: ${TOKEN}" \
      -d "{
        \"name\": \"生产环境-${provider}\",
        \"provider\": \"${provider}\",
        \"region\": \"${region}\",
        \"access_key\": \"your_${provider}_access_key\",
        \"secret_key\": \"your_${provider}_secret_key\",
        \"description\": \"生产环境${provider}配置\"
      }"
    
    echo "✅ ${provider}配置完成"
done
```

### 批量资源发现脚本

创建发现脚本 `discover_all_resources.sh`：

```bash
#!/bin/bash

BASE_URL="http://localhost:8080/api/ams-ce"
TOKEN="your_user_token"

# 获取所有配置
configs=$(curl -s -X GET "${BASE_URL}/cloud/configs" \
  -H "X-User-Token: ${TOKEN}" | jq -r '.dat[].id')

# 为每个配置发现资源
for config_id in $configs; do
    echo "发现配置${config_id}的资源..."
    
    curl -X POST "${BASE_URL}/cloud/discover" \
      -H "Content-Type: application/json" \
      -H "X-User-Token: ${TOKEN}" \
      -d "{
        \"config_id\": ${config_id},
        \"resource_types\": [\"ecs\", \"cvm\", \"rds\", \"cdb\"],
        \"filters\": {
          \"status\": [\"running\", \"active\"]
        }
      }"
    
    echo "✅ 配置${config_id}发现完成"
done
```

## 📈 监控和维护

### 检查同步状态

```bash
# 查看所有云资源同步状态
curl -X GET "http://localhost:8080/api/ams-ce/cloud/resources?sync_status=out_of_sync" \
  -H "X-User-Token: your_token"

# 重新同步失败的资源
curl -X POST http://localhost:8080/api/ams-ce/cloud/resources/sync \
  -H "Content-Type: application/json" \
  -H "X-User-Token: your_token" \
  -d '{
    "resource_ids": [1, 2, 3]
  }'
```

### 清理过期数据

```bash
# 清理30天前的发现记录
curl -X DELETE "http://localhost:8080/api/ams-ce/cloud/discoveries?days=30" \
  -H "X-User-Token: your_token"
```

## 🚨 故障排查

### 常见问题解决

1. **连接测试失败**
   ```bash
   # 测试特定云厂商连接
   curl -X POST http://localhost:8080/api/ams-ce/cloud/configs/1/test \
     -H "X-User-Token: your_token"
   ```

2. **资源发现为空**
   - 检查地域配置是否正确
   - 验证过滤条件是否过于严格
   - 确认云厂商账号权限

3. **导入失败**
   - 检查目标节点是否存在
   - 验证资源映射配置
   - 确认没有重复导入

## 📞 技术支持

如遇到问题，请检查：

1. **日志文件** - 查看详细错误信息
2. **网络连接** - 确保能访问云厂商API
3. **权限配置** - 验证云厂商账号权限
4. **配置正确性** - 检查地域和密钥配置

更多详细信息请参考：
- [云资源管理功能说明](CLOUD_RESOURCE_MANAGEMENT.md)
- [云厂商对比说明](CLOUD_PROVIDERS_COMPARISON.md)
