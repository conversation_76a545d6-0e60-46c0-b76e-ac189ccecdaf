package models

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"encoding/base64"
	"fmt"
	"io"
	"time"
)

// CloudProviderConfig 云厂商配置
type CloudProviderConfig struct {
	Id          int64     `json:"id" xorm:"'id' pk autoincr"`
	Name        string    `json:"name" xorm:"'name'"`
	Provider    string    `json:"provider" xorm:"'provider'"` // kingsoft/volcano
	Region      string    `json:"region" xorm:"'region'"`
	AccessKey   string    `json:"access_key" xorm:"'access_key'"`
	SecretKey   string    `json:"secret_key" xorm:"'secret_key'"`
	Endpoint    string    `json:"endpoint" xorm:"'endpoint'"`
	Description string    `json:"description" xorm:"'description'"`
	Creator     string    `json:"creator" xorm:"'creator'"`
	Status      int       `json:"status" xorm:"'status'"` // 1=启用，0=禁用
	CreatedAt   time.Time `json:"created_at" xorm:"'created_at'"`
	UpdatedAt   time.Time `json:"updated_at" xorm:"'updated_at'"`
}

// 加密密钥，实际使用时应该从配置文件读取
var encryptionKey = []byte("arboris-cloud-key-32-characters")

// Save 保存云厂商配置
func (c *CloudProviderConfig) Save() error {
	// 加密敏感信息
	encryptedAccessKey, err := encrypt(c.AccessKey)
	if err != nil {
		return err
	}
	encryptedSecretKey, err := encrypt(c.SecretKey)
	if err != nil {
		return err
	}

	c.AccessKey = encryptedAccessKey
	c.SecretKey = encryptedSecretKey
	c.CreatedAt = time.Now()
	c.UpdatedAt = time.Now()

	_, err = DB["ams"].Insert(c)
	return err
}

// Update 更新云厂商配置
func (c *CloudProviderConfig) Update(cols ...string) error {
	// 如果更新包含敏感字段，需要加密
	for _, col := range cols {
		if col == "access_key" && c.AccessKey != "" {
			encrypted, err := encrypt(c.AccessKey)
			if err != nil {
				return err
			}
			c.AccessKey = encrypted
		}
		if col == "secret_key" && c.SecretKey != "" {
			encrypted, err := encrypt(c.SecretKey)
			if err != nil {
				return err
			}
			c.SecretKey = encrypted
		}
	}

	c.UpdatedAt = time.Now()
	_, err := DB["ams"].Where("id=?", c.Id).Cols(append(cols, "updated_at")...).Update(c)
	return err
}

// GetDecryptedKeys 获取解密后的密钥
func (c *CloudProviderConfig) GetDecryptedKeys() (accessKey, secretKey string, err error) {
	accessKey, err = decrypt(c.AccessKey)
	if err != nil {
		return "", "", err
	}
	secretKey, err = decrypt(c.SecretKey)
	if err != nil {
		return "", "", err
	}
	return accessKey, secretKey, nil
}

// CloudProviderConfigGet 获取单个云厂商配置
func CloudProviderConfigGet(where string, args ...interface{}) (*CloudProviderConfig, error) {
	var obj CloudProviderConfig
	has, err := DB["ams"].Where(where, args...).Get(&obj)
	if err != nil {
		return nil, err
	}
	if !has {
		return nil, nil
	}
	return &obj, nil
}

// CloudProviderConfigGets 获取云厂商配置列表
func CloudProviderConfigGets(where string, args ...interface{}) ([]CloudProviderConfig, error) {
	var objs []CloudProviderConfig
	err := DB["ams"].Where(where, args...).OrderBy("created_at desc").Find(&objs)
	return objs, err
}

// CloudProviderConfigDel 删除云厂商配置
func CloudProviderConfigDel(id int64) error {
	_, err := DB["ams"].Where("id=?", id).Delete(new(CloudProviderConfig))
	return err
}

// encrypt 加密函数
func encrypt(plaintext string) (string, error) {
	if plaintext == "" {
		return "", nil
	}

	block, err := aes.NewCipher(encryptionKey)
	if err != nil {
		return "", err
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", err
	}

	nonce := make([]byte, gcm.NonceSize())
	if _, err = io.ReadFull(rand.Reader, nonce); err != nil {
		return "", err
	}

	ciphertext := gcm.Seal(nonce, nonce, []byte(plaintext), nil)
	return base64.StdEncoding.EncodeToString(ciphertext), nil
}

// decrypt 解密函数
func decrypt(ciphertext string) (string, error) {
	if ciphertext == "" {
		return "", nil
	}

	data, err := base64.StdEncoding.DecodeString(ciphertext)
	if err != nil {
		return "", err
	}

	block, err := aes.NewCipher(encryptionKey)
	if err != nil {
		return "", err
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", err
	}

	nonceSize := gcm.NonceSize()
	if len(data) < nonceSize {
		return "", fmt.Errorf("ciphertext too short")
	}

	nonce, ciphertext_bytes := data[:nonceSize], data[nonceSize:]
	plaintext, err := gcm.Open(nil, nonce, ciphertext_bytes, nil)
	if err != nil {
		return "", err
	}

	return string(plaintext), nil
}
