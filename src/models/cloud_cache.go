package models

import (
	"encoding/json"
	"time"
)

// CloudCache 云缓存资源表
type CloudCache struct {
	Id               int64      `json:"id" xorm:"'id' pk autoincr"`
	CloudId          string     `json:"cloud_id" xorm:"'cloud_id'"`
	Name             string     `json:"name" xorm:"'name'"`
	Engine           string     `json:"engine" xorm:"'engine'"`
	Version          string     `json:"version" xorm:"'version'"`
	InstanceClass    string     `json:"instance_class" xorm:"'instance_class'"`
	MemorySize       int        `json:"memory_size" xorm:"'memory_size'"`
	ShardCount       int        `json:"shard_count" xorm:"'shard_count'"`
	ReplicaCount     int        `json:"replica_count" xorm:"'replica_count'"`
	ConnectionString string     `json:"connection_string" xorm:"'connection_string'"`
	Port             int        `json:"port" xorm:"'port'"`
	VpcId            string     `json:"vpc_id" xorm:"'vpc_id'"`
	SubnetId         string     `json:"subnet_id" xorm:"'subnet_id'"`
	SecurityGroupIds string     `json:"security_group_ids" xorm:"'security_group_ids'"` // JSON字符串
	AuthEnabled      bool       `json:"auth_enabled" xorm:"'auth_enabled'"`
	SslEnabled       bool       `json:"ssl_enabled" xorm:"'ssl_enabled'"`
	BackupEnabled    bool       `json:"backup_enabled" xorm:"'backup_enabled'"`
	Status           string     `json:"status" xorm:"'status'"`
	Region           string     `json:"region" xorm:"'region'"`
	Zone             string     `json:"zone" xorm:"'zone'"`
	CloudProvider    string     `json:"cloud_provider" xorm:"'cloud_provider'"`
	SpecInfo         string     `json:"spec_info" xorm:"'spec_info'"` // JSON字符串
	NetworkInfo      string     `json:"network_info" xorm:"'network_info'"` // JSON字符串
	Tags             string     `json:"tags" xorm:"'tags'"` // JSON字符串
	RawData          string     `json:"raw_data" xorm:"'raw_data'"` // JSON字符串
	// 重复检测相关字段
	DataSource       DataSource      `json:"data_source" xorm:"'data_source'"`
	HasDuplicate     bool            `json:"has_duplicate" xorm:"'has_duplicate'"`
	DuplicateStatus  DuplicateStatus `json:"duplicate_status" xorm:"'duplicate_status'"`
	DuplicateInfo    string          `json:"duplicate_info" xorm:"'duplicate_info'"` // JSON字符串
	LastDiscoveryAt  *time.Time      `json:"last_discovery_at" xorm:"'last_discovery_at'"`
	CreatedAt        time.Time       `json:"created_at" xorm:"'created_at'"`
	UpdatedAt        time.Time       `json:"updated_at" xorm:"'updated_at'"`
}

// Save 保存云缓存记录
func (c *CloudCache) Save() error {
	c.CreatedAt = time.Now()
	c.UpdatedAt = time.Now()
	_, err := DB["ams"].Insert(c)
	return err
}

// Update 更新云缓存记录
func (c *CloudCache) Update(cols ...string) error {
	c.UpdatedAt = time.Now()
	_, err := DB["ams"].Where("id=?", c.Id).Cols(append(cols, "updated_at")...).Update(c)
	return err
}

// CloudCacheGet 获取单个云缓存记录
func CloudCacheGet(where string, args ...interface{}) (*CloudCache, error) {
	var obj CloudCache
	has, err := DB["ams"].Where(where, args...).Get(&obj)
	if err != nil {
		return nil, err
	}
	if !has {
		return nil, nil
	}
	return &obj, nil
}

// CloudCacheGets 获取云缓存记录列表
func CloudCacheGets(where string, args ...interface{}) ([]CloudCache, error) {
	var objs []CloudCache
	err := DB["ams"].Where(where, args...).OrderBy("created_at desc").Find(&objs)
	return objs, err
}

// CloudCacheDel 删除云缓存记录
func CloudCacheDel(id int64) error {
	_, err := DB["ams"].Where("id=?", id).Delete(new(CloudCache))
	return err
}

// CloudCacheCount 获取云缓存记录总数
func CloudCacheCount(where string, args ...interface{}) (int64, error) {
	return DB["ams"].Where(where, args...).Count(new(CloudCache))
}

// MarkAsDuplicate 标记为重复设备
func (c *CloudCache) MarkAsDuplicate(info *DuplicateInfo) error {
	c.HasDuplicate = true
	c.DuplicateStatus = DuplicateStatusDetected
	return c.SetDuplicateInfo(info)
}

// ResolveDuplicate 解决重复问题
func (c *CloudCache) ResolveDuplicate(action DuplicateAction) {
	switch action {
	case ActionIgnore:
		c.DuplicateStatus = DuplicateStatusIgnored
	case ActionOverride:
		c.DuplicateStatus = DuplicateStatusOverridden
		c.HasDuplicate = false
		c.DuplicateInfo = ""
	default:
		c.DuplicateStatus = DuplicateStatusNone
		c.HasDuplicate = false
		c.DuplicateInfo = ""
	}
}

// SetDuplicateInfo 设置重复信息
func (c *CloudCache) SetDuplicateInfo(info *DuplicateInfo) error {
	infoJSON, err := json.Marshal(info)
	if err != nil {
		return err
	}
	c.DuplicateInfo = string(infoJSON)
	return nil
}

// GetDuplicateInfo 获取重复信息
func (c *CloudCache) GetDuplicateInfo() (*DuplicateInfo, error) {
	if c.DuplicateInfo == "" {
		return nil, nil
	}
	
	var info DuplicateInfo
	err := json.Unmarshal([]byte(c.DuplicateInfo), &info)
	if err != nil {
		return nil, err
	}
	
	return &info, nil
}
