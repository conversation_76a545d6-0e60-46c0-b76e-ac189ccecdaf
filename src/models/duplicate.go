package models

import (
	"encoding/json"
	"time"
)

// DuplicateStatus 重复状态枚举
type DuplicateStatus string

const (
	DuplicateStatusNone       DuplicateStatus = "NONE"       // 无重复
	DuplicateStatusDetected   DuplicateStatus = "DETECTED"   // 检测到重复
	DuplicateStatusIgnored    DuplicateStatus = "IGNORED"    // 用户选择忽略
	DuplicateStatusOverridden DuplicateStatus = "OVERRIDDEN" // 用户选择覆盖
)

// DataSource 数据来源枚举
type DataSource string

const (
	DataSourceManual    DataSource = "MANUAL"        // 手动添加
	DataSourceImport    DataSource = "IMPORT"        // 批量导入
	DataSourceDiscovery DataSource = "AUTO_DISCOVERY" // 自动发现
)

// DeviceType 设备类型枚举
type DeviceType string

const (
	DeviceTypeHost     DeviceType = "host"     // 主机设备
	DeviceTypeResource DeviceType = "resource" // 资源设备
)

// DuplicateAction 重复处理动作枚举
type DuplicateAction string

const (
	ActionOverride DuplicateAction = "OVERRIDE" // 用发现的数据覆盖
	ActionIgnore   DuplicateAction = "IGNORE"   // 忽略，保持现有数据
	ActionMerge    DuplicateAction = "MERGE"    // 合并（可选功能）
)

// ResolutionStatus 解决状态枚举
type ResolutionStatus string

const (
	ResolutionStatusPending   ResolutionStatus = "PENDING"   // 待处理
	ResolutionStatusConfirmed ResolutionStatus = "CONFIRMED" // 已确认重复
	ResolutionStatusRejected  ResolutionStatus = "REJECTED"  // 已拒绝（不是重复）
)

// DuplicateInfo 重复信息结构
type DuplicateInfo struct {
	ConflictIP       string            `json:"conflict_ip"`        // 冲突的IP地址
	ExistingDeviceID int64             `json:"existing_device_id"` // 已存在设备ID
	ExistingSource   DataSource        `json:"existing_source"`    // 已存在设备来源
	DiscoveredData   map[string]interface{} `json:"discovered_data"`    // 新发现的数据
	DetectedAt       time.Time         `json:"detected_at"`        // 检测时间
	Differences      []FieldDifference `json:"differences"`        // 字段差异
}

// FieldDifference 字段差异结构
type FieldDifference struct {
	Field           string      `json:"field"`            // 字段名
	ExistingValue   interface{} `json:"existing_value"`   // 现有值
	DiscoveredValue interface{} `json:"discovered_value"` // 发现的值
	IsConflict      bool        `json:"is_conflict"`      // 是否冲突
}

// DeviceDuplicate 设备重复检测记录
type DeviceDuplicate struct {
	Id               int64            `json:"id" xorm:"'id' pk autoincr"`
	DeviceType       DeviceType       `json:"device_type" xorm:"'device_type'"`
	DeviceId1        int64            `json:"device_id_1" xorm:"'device_id_1'"`
	DeviceId2        int64            `json:"device_id_2" xorm:"'device_id_2'"`
	ConflictIP       string           `json:"conflict_ip" xorm:"'conflict_ip'"`
	SimilarityScore  float64          `json:"similarity_score" xorm:"'similarity_score'"`
	Status           ResolutionStatus `json:"status" xorm:"'status'"`
	CreatedAt        time.Time        `json:"created_at" xorm:"'created_at' created"`
	ResolvedAt       *time.Time       `json:"resolved_at" xorm:"'resolved_at'"`
	ResolvedBy       string           `json:"resolved_by" xorm:"'resolved_by'"`
	ResolutionAction DuplicateAction  `json:"resolution_action" xorm:"'resolution_action'"`
}

// DeviceMergeHistory 设备合并历史记录
type DeviceMergeHistory struct {
	Id              int64                  `json:"id" xorm:"'id' pk autoincr"`
	DeviceType      DeviceType             `json:"device_type" xorm:"'device_type'"`
	TargetDeviceId  int64                  `json:"target_device_id" xorm:"'target_device_id'"`
	SourceDeviceIds string                 `json:"source_device_ids" xorm:"'source_device_ids'"` // JSON数组
	MergeStrategy   string                 `json:"merge_strategy" xorm:"'merge_strategy'"`       // JSON对象
	FieldChanges    string                 `json:"field_changes" xorm:"'field_changes'"`         // JSON对象
	CreatedAt       time.Time              `json:"created_at" xorm:"'created_at' created"`
	CreatedBy       string                 `json:"created_by" xorm:"'created_by'"`
}

// DuplicateDevice 重复设备信息（用于API响应）
type DuplicateDevice struct {
	Id              int64         `json:"id"`
	Name            string        `json:"name"`
	IP              string        `json:"ip"`
	DeviceType      DeviceType    `json:"device_type"`
	DataSource      DataSource    `json:"data_source"`
	DuplicateInfo   DuplicateInfo `json:"duplicate_info"`
	CreatedAt       time.Time     `json:"created_at"`
	LastDiscoveryAt *time.Time    `json:"last_discovery_at"`
}

// GetDuplicatesResponse 获取重复设备列表响应
type GetDuplicatesResponse struct {
	Total      int               `json:"total"`
	Duplicates []DuplicateDevice `json:"duplicates"`
}

// ResolveDuplicateRequest 处理重复设备请求
type ResolveDuplicateRequest struct {
	DeviceType DeviceType      `json:"device_type" binding:"required"`
	DeviceID   int64           `json:"device_id" binding:"required"`
	Action     DuplicateAction `json:"action" binding:"required"`
	KeepFields []string        `json:"keep_fields,omitempty"` // 覆盖时保留的字段
}

// ResolveDuplicateResponse 处理重复设备响应
type ResolveDuplicateResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message"`
	Device  interface{} `json:"device,omitempty"` // Host或Resource对象
}

// BatchResolveDuplicatesRequest 批量处理重复设备请求
type BatchResolveDuplicatesRequest struct {
	Operations []ResolveDuplicateRequest `json:"operations" binding:"required"`
}

// BatchResolveDuplicatesResponse 批量处理重复设备响应
type BatchResolveDuplicatesResponse struct {
	Success int                        `json:"success"`
	Failed  int                        `json:"failed"`
	Results []ResolveDuplicateResponse `json:"results"`
}

// MergeStrategy 合并策略
type MergeStrategy struct {
	BasicInfo    string            `json:"basic_info"`    // 基础信息策略：existing/discovered/merge
	BusinessInfo string            `json:"business_info"` // 业务信息策略：existing/discovered/merge
	Tags         string            `json:"tags"`          // 标签策略：existing/discovered/merge
	Status       string            `json:"status"`        // 状态策略：existing/discovered/merge
	CustomFields map[string]string `json:"custom_fields"` // 自定义字段策略
}

// FieldChange 字段变更记录
type FieldChange struct {
	Field    string      `json:"field"`     // 字段名
	OldValue interface{} `json:"old_value"` // 旧值
	NewValue interface{} `json:"new_value"` // 新值
	Source   string      `json:"source"`    // 数据来源：existing/discovered
}

// DuplicateDetectionConfig 重复检测配置
type DuplicateDetectionConfig struct {
	Enabled                        bool `json:"enabled"`
	PreferPrivateIP                bool `json:"prefer_private_ip"`
	FallbackToPublicIP             bool `json:"fallback_to_public_ip"`
	IgnoreEmptyIP                  bool `json:"ignore_empty_ip"`
	AutoIgnoreSameSource           bool `json:"auto_ignore_same_source"`
	AutoOverrideManualWithDiscovery bool `json:"auto_override_manual_with_discovery"`
}

// TableName 设置表名
func (DeviceDuplicate) TableName() string {
	return "device_duplicates"
}

func (DeviceMergeHistory) TableName() string {
	return "device_merge_history"
}

// ToJSON 将结构体转换为JSON字符串
func (di *DuplicateInfo) ToJSON() string {
	data, _ := json.Marshal(di)
	return string(data)
}

// FromJSON 从JSON字符串解析结构体
func (di *DuplicateInfo) FromJSON(jsonStr string) error {
	return json.Unmarshal([]byte(jsonStr), di)
}

// ToJSON 将合并策略转换为JSON字符串
func (ms *MergeStrategy) ToJSON() string {
	data, _ := json.Marshal(ms)
	return string(data)
}

// FromJSON 从JSON字符串解析合并策略
func (ms *MergeStrategy) FromJSON(jsonStr string) error {
	return json.Unmarshal([]byte(jsonStr), ms)
}

// FieldChangesToJSON 将字段变更记录转换为JSON字符串
func FieldChangesToJSON(fc []FieldChange) string {
	data, _ := json.Marshal(fc)
	return string(data)
}

// FromJSONToFieldChanges 从JSON字符串解析字段变更记录
func FromJSONToFieldChanges(jsonStr string) ([]FieldChange, error) {
	var changes []FieldChange
	err := json.Unmarshal([]byte(jsonStr), &changes)
	return changes, err
}
