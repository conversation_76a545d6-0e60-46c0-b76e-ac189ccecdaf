package models

import (
	"encoding/json"
	"time"
)

// CloudResourceDiscovery 云资源发现记录
type CloudResourceDiscovery struct {
	Id           int64     `json:"id" xorm:"'id' pk autoincr"`
	ConfigId     int64     `json:"config_id" xorm:"'config_id'"`
	ResourceType string    `json:"resource_type" xorm:"'resource_type'"`
	DiscoveryTime time.Time `json:"discovery_time" xorm:"'discovery_time'"`
	TotalCount   int       `json:"total_count" xorm:"'total_count'"`
	ImportedCount int      `json:"imported_count" xorm:"'imported_count'"`
	Status       string    `json:"status" xorm:"'status'"` // pending/success/failed
	ErrorMessage string    `json:"error_message" xorm:"'error_message'"`
	Creator      string    `json:"creator" xorm:"'creator'"`
	CreatedAt    time.Time `json:"created_at" xorm:"'created_at'"`
	UpdatedAt    time.Time `json:"updated_at" xorm:"'updated_at'"`
}

// Save 保存发现记录
func (d *CloudResourceDiscovery) Save() error {
	d.CreatedAt = time.Now()
	d.UpdatedAt = time.Now()
	_, err := DB["ams"].Insert(d)
	return err
}

// Update 更新发现记录
func (d *CloudResourceDiscovery) Update(cols ...string) error {
	d.UpdatedAt = time.Now()
	_, err := DB["ams"].Where("id=?", d.Id).Cols(append(cols, "updated_at")...).Update(d)
	return err
}

// CloudResourceDiscoveryGet 获取单个发现记录
func CloudResourceDiscoveryGet(where string, args ...interface{}) (*CloudResourceDiscovery, error) {
	var obj CloudResourceDiscovery
	has, err := DB["ams"].Where(where, args...).Get(&obj)
	if err != nil {
		return nil, err
	}
	if !has {
		return nil, nil
	}
	return &obj, nil
}

// CloudResourceDiscoveryGets 获取发现记录列表
func CloudResourceDiscoveryGets(where string, args ...interface{}) ([]CloudResourceDiscovery, error) {
	var objs []CloudResourceDiscovery
	err := DB["ams"].Where(where, args...).OrderBy("created_at desc").Find(&objs)
	return objs, err
}

// CloudResourceTemp 云资源临时表
type CloudResourceTemp struct {
	Id          int64     `json:"id" xorm:"'id' pk autoincr"`
	DiscoveryId int64     `json:"discovery_id" xorm:"'discovery_id'"`
	CloudId     string    `json:"cloud_id" xorm:"'cloud_id'"`
	Name        string    `json:"name" xorm:"'name'"`
	ResourceType string   `json:"resource_type" xorm:"'resource_type'"`
	Region      string    `json:"region" xorm:"'region'"`
	Zone        string    `json:"zone" xorm:"'zone'"`
	Status      string    `json:"status" xorm:"'status'"`
	SpecInfo    string    `json:"spec_info" xorm:"'spec_info'"` // JSON字符串
	NetworkInfo string    `json:"network_info" xorm:"'network_info'"` // JSON字符串
	Tags        string    `json:"tags" xorm:"'tags'"` // JSON字符串
	RawData     string    `json:"raw_data" xorm:"'raw_data'"` // JSON字符串
	Selected    int       `json:"selected" xorm:"'selected'"` // 0=未选中，1=选中
	Imported    int       `json:"imported" xorm:"'imported'"` // 0=未导入，1=已导入
	// 重复检测相关字段
	HasDuplicate       bool   `json:"has_duplicate" xorm:"'has_duplicate'"`
	DuplicateStatus    string `json:"duplicate_status" xorm:"'duplicate_status'"`
	DuplicateInfo      string `json:"duplicate_info" xorm:"'duplicate_info'"` // JSON字符串
	ConflictIP         string `json:"conflict_ip" xorm:"'conflict_ip'"`
	ExistingDeviceId   int64  `json:"existing_device_id" xorm:"'existing_device_id'"`
	ExistingDeviceType string `json:"existing_device_type" xorm:"'existing_device_type'"`
	CreatedAt   time.Time `json:"created_at" xorm:"'created_at'"`
}

// Save 保存临时资源
func (t *CloudResourceTemp) Save() error {
	t.CreatedAt = time.Now()
	_, err := DB["ams"].Insert(t)
	return err
}

// BatchSave 批量保存临时资源
func CloudResourceTempBatchSave(resources []CloudResourceTemp) error {
	if len(resources) == 0 {
		return nil
	}

	session := DB["ams"].NewSession()
	defer session.Close()

	if err := session.Begin(); err != nil {
		return err
	}

	for i := range resources {
		resources[i].CreatedAt = time.Now()
	}

	_, err := session.Insert(resources)
	if err != nil {
		session.Rollback()
		return err
	}

	return session.Commit()
}

// CloudResourceTempGet 获取单个临时资源
func CloudResourceTempGet(where string, args ...interface{}) (*CloudResourceTemp, error) {
	var obj CloudResourceTemp
	has, err := DB["ams"].Where(where, args...).Get(&obj)
	if err != nil {
		return nil, err
	}
	if !has {
		return nil, nil
	}
	return &obj, nil
}

// CloudResourceTempGets 获取临时资源列表
func CloudResourceTempGets(where string, args ...interface{}) ([]CloudResourceTemp, error) {
	var objs []CloudResourceTemp
	err := DB["ams"].Where(where, args...).OrderBy("created_at desc").Find(&objs)
	return objs, err
}

// CloudResourceTempGetsByDiscoveryId 根据发现ID获取临时资源列表
func CloudResourceTempGetsByDiscoveryId(discoveryId int64, limit, offset int) ([]CloudResourceTemp, int64, error) {
	var objs []CloudResourceTemp
	
	session := DB["ams"].Where("discovery_id=?", discoveryId)
	
	total, err := session.Count(new(CloudResourceTemp))
	if err != nil {
		return nil, 0, err
	}
	
	err = session.OrderBy("created_at desc").Limit(limit, offset).Find(&objs)
	return objs, total, err
}

// CloudResourceTempUpdateSelected 更新选中状态
func CloudResourceTempUpdateSelected(ids []int64, selected int) error {
	if len(ids) == 0 {
		return nil
	}
	
	_, err := DB["ams"].In("id", ids).Cols("selected").Update(&CloudResourceTemp{Selected: selected})
	return err
}

// CloudResourceTempGetSelected 获取选中的临时资源
func CloudResourceTempGetSelected(discoveryId int64) ([]CloudResourceTemp, error) {
	var objs []CloudResourceTemp
	err := DB["ams"].Where("discovery_id=? and selected=1", discoveryId).Find(&objs)
	return objs, err
}

// CloudResourceTempDelByDiscoveryId 根据发现ID删除临时资源
func CloudResourceTempDelByDiscoveryId(discoveryId int64) error {
	_, err := DB["ams"].Where("discovery_id=?", discoveryId).Delete(new(CloudResourceTemp))
	return err
}

// CloudResourceTempUpdate 更新临时资源
func CloudResourceTempUpdate(tempResource *CloudResourceTemp) error {
	_, err := DB["ams"].Where("id=?", tempResource.Id).Update(tempResource)
	return err
}

// MarkAsDuplicate 标记临时资源为重复
func (t *CloudResourceTemp) MarkAsDuplicate(conflictIP string, existingDeviceId int64, existingDeviceType string, duplicateInfo interface{}) error {
	t.HasDuplicate = true
	t.DuplicateStatus = "DETECTED"
	t.ConflictIP = conflictIP
	t.ExistingDeviceId = existingDeviceId
	t.ExistingDeviceType = existingDeviceType

	if duplicateInfo != nil {
		infoJSON, err := json.Marshal(duplicateInfo)
		if err != nil {
			return err
		}
		t.DuplicateInfo = string(infoJSON)
	}

	return nil
}

// ResolveDuplicate 解决重复问题
func (t *CloudResourceTemp) ResolveDuplicate(action string) {
	switch action {
	case "IGNORE":
		t.DuplicateStatus = "IGNORED"
	case "OVERRIDE":
		t.DuplicateStatus = "OVERRIDDEN"
	default:
		t.DuplicateStatus = "NONE"
		t.HasDuplicate = false
		t.DuplicateInfo = ""
		t.ConflictIP = ""
		t.ExistingDeviceId = 0
		t.ExistingDeviceType = ""
	}
}

// CloudResourceTempGetsByDiscoveryIdWithPaging 分页获取发现的临时资源
func CloudResourceTempGetsByDiscoveryIdWithPaging(discoveryId int64, limit, offset int) ([]CloudResourceTemp, error) {
	var objs []CloudResourceTemp
	err := DB["ams"].Where("discovery_id=?", discoveryId).
		OrderBy("created_at desc").
		Limit(limit, offset).
		Find(&objs)
	return objs, err
}

// CloudResourceTempCountByDiscoveryId 获取发现的临时资源总数
func CloudResourceTempCountByDiscoveryId(discoveryId int64) (int64, error) {
	return DB["ams"].Where("discovery_id=?", discoveryId).Count(new(CloudResourceTemp))
}

// CloudResourceTempCountByDiscoveryIdAndDuplicate 获取发现的重复资源数
func CloudResourceTempCountByDiscoveryIdAndDuplicate(discoveryId int64, hasDuplicate bool) (int64, error) {
	return DB["ams"].Where("discovery_id=? AND has_duplicate=?", discoveryId, hasDuplicate).Count(new(CloudResourceTemp))
}

// CloudResourceTempCountByDiscoveryIdAndStatus 获取发现的指定状态资源数
func CloudResourceTempCountByDiscoveryIdAndStatus(discoveryId int64, statuses []string) (int64, error) {
	if len(statuses) == 0 {
		return 0, nil
	}

	query := DB["ams"].Where("discovery_id=?", discoveryId)
	if len(statuses) == 1 {
		query = query.And("duplicate_status=?", statuses[0])
	} else {
		query = query.And("duplicate_status IN (?)", statuses)
	}

	return query.Count(new(CloudResourceTemp))
}

// CloudResourceTempGetsByDiscoveryIdSimple 简单获取发现的临时资源（不分页）
func CloudResourceTempGetsByDiscoveryIdSimple(discoveryId int64) ([]CloudResourceTemp, error) {
	var objs []CloudResourceTemp
	err := DB["ams"].Where("discovery_id=?", discoveryId).
		OrderBy("created_at desc").
		Find(&objs)
	return objs, err
}
