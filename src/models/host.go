package models

import (
	"encoding/json"
	"strings"
	"time"

	"xorm.io/xorm"

	"github.com/toolkits/pkg/str"
)

type Host struct {
	Id            int64  `json:"id" xorm:"'id' pk autoincr"`
	SN            string `json:"sn" xorm:"'sn'"`
	IP            string `json:"ip" xorm:"'ip'"`
	Ident         string `json:"ident" xorm:"'ident'"`
	Name          string `json:"name" xorm:"'name'"`
	OSVersion     string `json:"os_version" xorm:"'os_version'"`
	KernelVersion string `json:"kernel_version" xorm:"'kernel_version'"`
	CPUModel      string `json:"cpu_model" xorm:"'cpu_model'"`
	CPU           string `json:"cpu" xorm:"'cpu'"`
	Mem           string `json:"mem" xorm:"'mem'"`
	Disk          string `json:"disk" xorm:"'disk'"`
	Note          string `json:"note" xorm:"'note'"`
	Cate          string `json:"cate" xorm:"'cate'"`
	Tenant        string `json:"tenant" xorm:"'tenant'"`

	// 云资源相关字段
	CloudId          string     `json:"cloud_id" xorm:"'cloud_id'"`
	CloudProvider    string     `json:"cloud_provider" xorm:"'cloud_provider'"`
	CloudRegion      string     `json:"cloud_region" xorm:"'cloud_region'"`
	CloudZone        string     `json:"cloud_zone" xorm:"'cloud_zone'"`
	InstanceType     string     `json:"instance_type" xorm:"'instance_type'"`
	ImageId          string     `json:"image_id" xorm:"'image_id'"`
	VpcId            string     `json:"vpc_id" xorm:"'vpc_id'"`
	SubnetId         string     `json:"subnet_id" xorm:"'subnet_id'"`
	SecurityGroupIds string     `json:"security_group_ids" xorm:"'security_group_ids'"` // JSON字符串
	PublicIP         string     `json:"public_ip" xorm:"'public_ip'"`
	PrivateIP        string     `json:"private_ip" xorm:"'private_ip'"`
	KeyPairName      string     `json:"key_pair_name" xorm:"'key_pair_name'"`
	ChargeType       string     `json:"charge_type" xorm:"'charge_type'"`
	ExpiredTime      *time.Time `json:"expired_time" xorm:"'expired_time'"`
	AutoRenew        bool       `json:"auto_renew" xorm:"'auto_renew'"`
	CloudStatus      string     `json:"cloud_status" xorm:"'cloud_status'"`
	CloudTags        string     `json:"cloud_tags" xorm:"'cloud_tags'"` // JSON字符串
	CloudRawData     string     `json:"cloud_raw_data" xorm:"'cloud_raw_data'"` // JSON字符串

	// 重复检测相关字段
	DataSource       DataSource      `json:"data_source" xorm:"'data_source'"`
	HasDuplicate     bool            `json:"has_duplicate" xorm:"'has_duplicate'"`
	DuplicateStatus  DuplicateStatus `json:"duplicate_status" xorm:"'duplicate_status'"`
	DuplicateInfo    string          `json:"duplicate_info" xorm:"'duplicate_info'"` // JSON字符串
	LastDiscoveryAt  *time.Time      `json:"last_discovery_at" xorm:"'last_discovery_at'"`
	Clock         int64  `json:"clock" xorm:"'clock'"`
	GPU           string `json:"gpu" xorm:"'gpu'"`
	GPUModel      string `json:"gpu_model" xorm:"'gpu_model'"`
	Model         string `json:"model" xorm:"'model'"`
	IDC           string `json:"idc" xorm:"'idc'"`
	Zone          string `json:"zone" xorm:"'zone'"`
	Rack          string `json:"rack" xorm:"'rack'"`
	Manufacturer  string `json:"manufacturer" xorm:"'manufacturer'"`
}

func (h *Host) Save() error {
	_, err := DB["ams"].Insert(h)
	return err
}

func HostNew(sn, ip, ident, name, cate string, fields map[string]interface{}) (*Host, error) {
	host := new(Host)
	host.SN = sn
	host.IP = ip
	host.Ident = ident
	host.Name = name
	host.Cate = cate
	host.Clock = time.Now().Unix()

	session := DB["ams"].NewSession()
	defer session.Close()

	if err := session.Begin(); err != nil {
		return nil, err
	}

	if _, err := session.Insert(host); err != nil {
		session.Rollback()
		return nil, err
	}

	if len(fields) > 0 {
		if _, err := session.Table(new(Host)).ID(host.Id).Update(fields); err != nil {
			session.Rollback()
			return nil, err
		}
	}

	err := session.Commit()

	return host, err
}

// HostBatchNew 批量创建主机，提高导入性能
func HostBatchNew(hosts []*Host) error {
	if len(hosts) == 0 {
		return nil
	}

	session := DB["ams"].NewSession()
	defer session.Close()

	if err := session.Begin(); err != nil {
		return err
	}

	// 批量插入主机
	_, err := session.Insert(hosts)
	if err != nil {
		session.Rollback()
		return err
	}

	return session.Commit()
}

// HostCheckDuplicates 批量检查主机IP和Ident重复
func HostCheckDuplicates(ips, idents []string) (map[string]bool, error) {
	if len(ips) == 0 && len(idents) == 0 {
		return make(map[string]bool), nil
	}

	duplicateMap := make(map[string]bool)

	// 使用UNION查询一次性检查IP和Ident，减少数据库往返
	if len(ips) > 0 && len(idents) > 0 {
		// 构建UNION查询
		var results []struct {
			Value string `xorm:"value"`
		}

		// 使用原生SQL进行优化查询
		sql := "SELECT ip as value FROM host WHERE ip IN (?" + strings.Repeat(",?", len(ips)-1) + ") " +
			"UNION " +
			"SELECT ident as value FROM host WHERE ident IN (?" + strings.Repeat(",?", len(idents)-1) + ")"

		args := make([]interface{}, 0, len(ips)+len(idents))
		for _, ip := range ips {
			args = append(args, ip)
		}
		for _, ident := range idents {
			args = append(args, ident)
		}

		err := DB["ams"].SQL(sql, args...).Find(&results)
		if err != nil {
			return nil, err
		}

		for _, result := range results {
			duplicateMap[result.Value] = true
		}
	} else {
		// 单独处理IP或Ident的情况
		if len(ips) > 0 {
			var existingIPs []string
			err := DB["ams"].Table("host").In("ip", ips).Cols("ip").Find(&existingIPs)
			if err != nil {
				return nil, err
			}
			for _, ip := range existingIPs {
				duplicateMap[ip] = true
			}
		}

		if len(idents) > 0 {
			var existingIdents []string
			err := DB["ams"].Table("host").In("ident", idents).Cols("ident").Find(&existingIdents)
			if err != nil {
				return nil, err
			}
			for _, ident := range existingIdents {
				duplicateMap[ident] = true
			}
		}
	}

	return duplicateMap, nil
}

func (h *Host) Update(fields map[string]interface{}) error {
	_, err := DB["ams"].Table(new(Host)).ID(h.Id).Update(fields)
	return err
}

// Update 更新主机记录（支持列名）
func (h *Host) UpdateCols(cols ...string) error {
	_, err := DB["ams"].Where("id=?", h.Id).Cols(cols...).Update(h)
	return err
}

func (h *Host) Del() error {
	session := DB["ams"].NewSession()
	defer session.Close()

	if err := session.Begin(); err != nil {
		return err
	}

	// 先删除主机的自定义字段值
	if _, err := session.Where("host_id = ?", h.Id).Delete(new(HostFieldValue)); err != nil {
		session.Rollback()
		return err
	}

	// 再删除主机本身
	if _, err := session.Where("id = ?", h.Id).Delete(new(Host)); err != nil {
		session.Rollback()
		return err
	}

	return session.Commit()
}

func HostUpdateNote(ids []int64, note string) error {
	_, err := DB["ams"].Exec("UPDATE host SET note=? WHERE id in ("+str.IdsString(ids)+")", note)
	return err
}

func HostUpdateCate(ids []int64, cate string) error {
	_, err := DB["ams"].Exec("UPDATE host SET cate=? WHERE id in ("+str.IdsString(ids)+")", cate)
	return err
}

func HostUpdateTenant(ids []int64, tenant string) error {
	_, err := DB["ams"].Exec("UPDATE host SET tenant=? WHERE id in ("+str.IdsString(ids)+")", tenant)
	return err
}

func HostGet(where string, args ...interface{}) (*Host, error) {
	var obj Host
	has, err := DB["ams"].Where(where, args...).Get(&obj)
	if err != nil {
		return nil, err
	}

	if !has {
		return nil, nil
	}

	return &obj, nil
}

func HostGets(where string, args ...interface{}) (hosts []Host, err error) {
	if where != "" {
		err = DB["ams"].Where(where, args...).Find(&hosts)
	} else {
		err = DB["ams"].Find(&hosts)
	}
	return hosts, err
}

func HostByIds(ids []int64) (hosts []Host, err error) {
	if len(ids) == 0 {
		return
	}

	err = DB["ams"].In("id", ids).Find(&hosts)
	return
}

func HostIdsByIps(ips []string) (ids []int64, err error) {
	err = DB["ams"].Table(new(Host)).In("ip", ips).Select("id").Find(&ids)
	return
}

func HostTotalForAdmin(tenant, query, batch, field string) (int64, error) {
	return buildHostWhere(tenant, query, batch, field).Count()
}

func HostGetsForAdmin(tenant, query, batch, field string, limit, offset int) ([]Host, error) {
	var objs []Host
	err := buildHostWhere(tenant, query, batch, field).Limit(limit, offset).Find(&objs)
	return objs, err
}

func buildHostWhere(tenant, query, batch, field string) *xorm.Session {
	session := DB["ams"].Table(new(Host)).OrderBy("ident")

	if tenant == "0" {
		session = session.Where("tenant=?", "")
	} else if tenant != "" {
		session = session.Where("tenant=?", tenant)
	}

	if batch == "" && query != "" {
		arr := strings.Fields(query)
		for i := 0; i < len(arr); i++ {
			q := "%" + arr[i] + "%"
			session = session.Where("cate=? or sn=? or ident like ? or ip like ? or name like ? or note like ? or model like ? or idc like ? or manufacturer like ?", arr[i], arr[i], q, q, q, q, q, q, q)
		}
	}

	if batch != "" {
		arr := str.ParseLines(strings.Replace(batch, ",", "\n", -1))
		if len(arr) > 0 {
			session = session.In(field, arr)
		}
	}

	return session
}

// HostTotalForCustomField 获取自定义字段过滤后的总数
func HostTotalForCustomField(tenant, query, batch, fieldIdent string) (int64, error) {
	session := DB["ams"].Table("host_field_value").
		Join("INNER", "host", "host.id = host_field_value.host_id").
		Where("host_field_value.field_ident = ?", fieldIdent)

	if tenant == "0" {
		session = session.Where("host.tenant = ?", "")
	} else if tenant != "" {
		session = session.Where("host.tenant = ?", tenant)
	}

	if batch != "" {
		// 批量精确匹配
		values := str.ParseLines(strings.Replace(batch, ",", "\n", -1))
		if len(values) > 0 {
			session = session.In("host_field_value.field_value", values)
		}
	} else if query != "" {
		// 单值模糊匹配
		session = session.Where("host_field_value.field_value LIKE ?", "%"+query+"%")
	}

	return session.Count()
}

// HostGetsForCustomField 获取自定义字段过滤后的主机列表
func HostGetsForCustomField(tenant, query, batch, fieldIdent string, limit, offset int) ([]Host, error) {
	session := DB["ams"].Table("host").
		Join("INNER", "host_field_value", "host.id = host_field_value.host_id").
		Where("host_field_value.field_ident = ?", fieldIdent)

	if tenant == "0" {
		session = session.Where("host.tenant = ?", "")
	} else if tenant != "" {
		session = session.Where("host.tenant = ?", tenant)
	}

	if batch != "" {
		// 批量精确匹配
		values := str.ParseLines(strings.Replace(batch, ",", "\n", -1))
		if len(values) > 0 {
			session = session.In("host_field_value.field_value", values)
		}
	} else if query != "" {
		// 单值模糊匹配
		session = session.Where("host_field_value.field_value LIKE ?", "%"+query+"%")
	}

	var hosts []Host
	err := session.OrderBy("host.ident").Limit(limit, offset).Find(&hosts)
	return hosts, err
}

// GetDuplicateInfo 获取重复信息
func (h *Host) GetDuplicateInfo() (*DuplicateInfo, error) {
	if h.DuplicateInfo == "" {
		return nil, nil
	}

	var info DuplicateInfo
	err := json.Unmarshal([]byte(h.DuplicateInfo), &info)
	if err != nil {
		return nil, err
	}
	return &info, nil
}

// SetDuplicateInfo 设置重复信息
func (h *Host) SetDuplicateInfo(info *DuplicateInfo) error {
	if info == nil {
		h.DuplicateInfo = ""
		return nil
	}

	data, err := json.Marshal(info)
	if err != nil {
		return err
	}
	h.DuplicateInfo = string(data)
	return nil
}

// MarkAsDuplicate 标记为重复设备
func (h *Host) MarkAsDuplicate(info *DuplicateInfo) error {
	h.HasDuplicate = true
	h.DuplicateStatus = DuplicateStatusDetected
	return h.SetDuplicateInfo(info)
}

// ResolveDuplicate 解决重复问题
func (h *Host) ResolveDuplicate(action DuplicateAction) {
	switch action {
	case ActionIgnore:
		h.DuplicateStatus = DuplicateStatusIgnored
	case ActionOverride:
		h.DuplicateStatus = DuplicateStatusOverridden
		h.HasDuplicate = false
		h.DuplicateInfo = ""
	default:
		h.DuplicateStatus = DuplicateStatusNone
		h.HasDuplicate = false
		h.DuplicateInfo = ""
	}
}

// GetPrimaryIP 获取主要IP地址（用于重复检测）
func (h *Host) GetPrimaryIP() string {
	// 直接返回IP字段，host表中IP是主要标识
	return h.IP
}

// FindHostByIP 根据IP查找主机
func FindHostByIP(ip string) (*Host, error) {
	if ip == "" {
		return nil, nil
	}

	var host Host
	has, err := DB["ams"].Where("ip = ?", ip).Get(&host)
	if err != nil {
		return nil, err
	}
	if !has {
		return nil, nil
	}
	return &host, nil
}

// GetDuplicateHosts 获取有重复的主机列表
func GetDuplicateHosts(limit, offset int) ([]Host, error) {
	var hosts []Host
	err := DB["ams"].Where("has_duplicate = ?", true).
		OrderBy("last_discovery_at DESC").
		Limit(limit, offset).
		Find(&hosts)
	return hosts, err
}

// CountDuplicateHosts 统计有重复的主机数量
func CountDuplicateHosts() (int64, error) {
	return DB["ams"].Where("has_duplicate = ?", true).Count(&Host{})
}
