package models

import (
	"encoding/json"
	"time"
)

// CloudDatabase 云数据库资源表
type CloudDatabase struct {
	Id                  int64      `json:"id" xorm:"'id' pk autoincr"`
	CloudId             string     `json:"cloud_id" xorm:"'cloud_id'"`
	Name                string     `json:"name" xorm:"'name'"`
	Engine              string     `json:"engine" xorm:"'engine'"`
	Version             string     `json:"version" xorm:"'version'"`
	InstanceClass       string     `json:"instance_class" xorm:"'instance_class'"`
	StorageSize         int        `json:"storage_size" xorm:"'storage_size'"`
	StorageType         string     `json:"storage_type" xorm:"'storage_type'"`
	ConnectionString    string     `json:"connection_string" xorm:"'connection_string'"`
	Port                int        `json:"port" xorm:"'port'"`
	VpcId               string     `json:"vpc_id" xorm:"'vpc_id'"`
	SubnetId            string     `json:"subnet_id" xorm:"'subnet_id'"`
	SecurityGroupIds    string     `json:"security_group_ids" xorm:"'security_group_ids'"` // JSON字符串
	BackupRetentionDays int        `json:"backup_retention_days" xorm:"'backup_retention_days'"`
	MaintenanceWindow   string     `json:"maintenance_window" xorm:"'maintenance_window'"`
	MultiAz             bool       `json:"multi_az" xorm:"'multi_az'"`
	Status              string     `json:"status" xorm:"'status'"`
	Region              string     `json:"region" xorm:"'region'"`
	Zone                string     `json:"zone" xorm:"'zone'"`
	CloudProvider       string     `json:"cloud_provider" xorm:"'cloud_provider'"`
	SpecInfo            string     `json:"spec_info" xorm:"'spec_info'"` // JSON字符串
	NetworkInfo         string     `json:"network_info" xorm:"'network_info'"` // JSON字符串
	Tags                string     `json:"tags" xorm:"'tags'"` // JSON字符串
	RawData             string     `json:"raw_data" xorm:"'raw_data'"` // JSON字符串
	// 重复检测相关字段
	DataSource       DataSource      `json:"data_source" xorm:"'data_source'"`
	HasDuplicate     bool            `json:"has_duplicate" xorm:"'has_duplicate'"`
	DuplicateStatus  DuplicateStatus `json:"duplicate_status" xorm:"'duplicate_status'"`
	DuplicateInfo    string          `json:"duplicate_info" xorm:"'duplicate_info'"` // JSON字符串
	LastDiscoveryAt  *time.Time      `json:"last_discovery_at" xorm:"'last_discovery_at'"`
	CreatedAt        time.Time       `json:"created_at" xorm:"'created_at'"`
	UpdatedAt        time.Time       `json:"updated_at" xorm:"'updated_at'"`
}

// Save 保存云数据库记录
func (d *CloudDatabase) Save() error {
	d.CreatedAt = time.Now()
	d.UpdatedAt = time.Now()
	_, err := DB["ams"].Insert(d)
	return err
}

// Update 更新云数据库记录
func (d *CloudDatabase) Update(cols ...string) error {
	d.UpdatedAt = time.Now()
	_, err := DB["ams"].Where("id=?", d.Id).Cols(append(cols, "updated_at")...).Update(d)
	return err
}

// CloudDatabaseGet 获取单个云数据库记录
func CloudDatabaseGet(where string, args ...interface{}) (*CloudDatabase, error) {
	var obj CloudDatabase
	has, err := DB["ams"].Where(where, args...).Get(&obj)
	if err != nil {
		return nil, err
	}
	if !has {
		return nil, nil
	}
	return &obj, nil
}

// CloudDatabaseGets 获取云数据库记录列表
func CloudDatabaseGets(where string, args ...interface{}) ([]CloudDatabase, error) {
	var objs []CloudDatabase
	err := DB["ams"].Where(where, args...).OrderBy("created_at desc").Find(&objs)
	return objs, err
}

// CloudDatabaseDel 删除云数据库记录
func CloudDatabaseDel(id int64) error {
	_, err := DB["ams"].Where("id=?", id).Delete(new(CloudDatabase))
	return err
}

// CloudDatabaseCount 获取云数据库记录总数
func CloudDatabaseCount(where string, args ...interface{}) (int64, error) {
	return DB["ams"].Where(where, args...).Count(new(CloudDatabase))
}

// MarkAsDuplicate 标记为重复设备
func (d *CloudDatabase) MarkAsDuplicate(info *DuplicateInfo) error {
	d.HasDuplicate = true
	d.DuplicateStatus = DuplicateStatusDetected
	return d.SetDuplicateInfo(info)
}

// ResolveDuplicate 解决重复问题
func (d *CloudDatabase) ResolveDuplicate(action DuplicateAction) {
	switch action {
	case ActionIgnore:
		d.DuplicateStatus = DuplicateStatusIgnored
	case ActionOverride:
		d.DuplicateStatus = DuplicateStatusOverridden
		d.HasDuplicate = false
		d.DuplicateInfo = ""
	default:
		d.DuplicateStatus = DuplicateStatusNone
		d.HasDuplicate = false
		d.DuplicateInfo = ""
	}
}

// SetDuplicateInfo 设置重复信息
func (d *CloudDatabase) SetDuplicateInfo(info *DuplicateInfo) error {
	infoJSON, err := json.Marshal(info)
	if err != nil {
		return err
	}
	d.DuplicateInfo = string(infoJSON)
	return nil
}

// GetDuplicateInfo 获取重复信息
func (d *CloudDatabase) GetDuplicateInfo() (*DuplicateInfo, error) {
	if d.DuplicateInfo == "" {
		return nil, nil
	}
	
	var info DuplicateInfo
	err := json.Unmarshal([]byte(d.DuplicateInfo), &info)
	if err != nil {
		return nil, err
	}
	
	return &info, nil
}
