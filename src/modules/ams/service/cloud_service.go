package service

import (
	"encoding/json"
	"fmt"
	"log"
	"strconv"
	"time"

	"arboris/src/models"
	"arboris/src/modules/ams/cloud"
	jsEvents "arboris/src/modules/jumpserver-sync/events"
	rdbEvents "arboris/src/modules/rdb/events"
)

// CloudService 云资源服务
type CloudService struct{}

// NewCloudService 创建云资源服务实例
func NewCloudService() *CloudService {
	return &CloudService{}
}

// TestConnection 测试云厂商连接
func (s *CloudService) TestConnection(configId int64) error {
	config, err := models.CloudProviderConfigGet("id=?", configId)
	if err != nil {
		return fmt.Errorf("failed to get cloud config: %v", err)
	}
	if config == nil {
		return fmt.Errorf("cloud config not found")
	}

	// 获取解密后的密钥
	accessKey, secretKey, err := config.GetDecryptedKeys()
	if err != nil {
		return fmt.Errorf("failed to decrypt keys: %v", err)
	}

	// 创建云厂商实例
	provider, err := cloud.NewCloudProviderWithConfig(cloud.ProviderConfig{
		Provider:  config.Provider,
		Region:    config.Region,
		AccessKey: accessKey,
		SecretKey: secretKey,
		Endpoint:  config.Endpoint,
	})
	if err != nil {
		return err
	}

	return provider.TestConnection()
}

// DiscoverResources 发现云资源
func (s *CloudService) DiscoverResources(configId int64, resourceTypes []string, filters cloud.DiscoveryFilters, creator string) (*models.CloudResourceDiscovery, error) {
	config, err := models.CloudProviderConfigGet("id=?", configId)
	if err != nil {
		return nil, fmt.Errorf("failed to get cloud config: %v", err)
	}
	if config == nil {
		return nil, fmt.Errorf("cloud config not found")
	}

	// 获取解密后的密钥
	accessKey, secretKey, err := config.GetDecryptedKeys()
	if err != nil {
		return nil, fmt.Errorf("failed to decrypt keys: %v", err)
	}

	// 创建云厂商实例
	provider, err := cloud.NewCloudProviderWithConfig(cloud.ProviderConfig{
		Provider:  config.Provider,
		Region:    config.Region,
		AccessKey: accessKey,
		SecretKey: secretKey,
		Endpoint:  config.Endpoint,
	})
	if err != nil {
		return nil, err
	}

	// 创建发现记录
	discovery := &models.CloudResourceDiscovery{
		ConfigId:      configId,
		ResourceType:  fmt.Sprintf("%v", resourceTypes), // 将数组转为字符串
		DiscoveryTime: time.Now(),
		Status:        "pending",
		Creator:       creator,
	}

	if err := discovery.Save(); err != nil {
		return nil, fmt.Errorf("failed to save discovery record: %v", err)
	}

	// 异步发现资源
	go s.doDiscoverResources(discovery, provider, resourceTypes, filters)

	return discovery, nil
}

// doDiscoverResources 执行资源发现
func (s *CloudService) doDiscoverResources(discovery *models.CloudResourceDiscovery, provider cloud.CloudProvider, resourceTypes []string, filters cloud.DiscoveryFilters) {
	var allResources []models.CloudResourceTemp
	totalCount := 0

	filterMap := filters.ToMap()

	for _, resourceType := range resourceTypes {
		log.Printf("Discovering %s resources for discovery %d", resourceType, discovery.Id)

		resources, err := provider.DiscoverResources(resourceType, filterMap)
		if err != nil {
			log.Printf("Failed to discover %s resources: %v", resourceType, err)
			discovery.Status = "failed"
			discovery.ErrorMessage = fmt.Sprintf("Failed to discover %s: %v", resourceType, err)
			discovery.Update("status", "error_message")
			return
		}

		// 转换为临时资源记录
		for _, resource := range resources {
			specInfoJson, _ := json.Marshal(resource.SpecInfo)
			networkInfoJson, _ := json.Marshal(resource.NetworkInfo)
			tagsJson, _ := json.Marshal(resource.Tags)
			rawDataJson, _ := json.Marshal(resource.RawData)

			tempResource := models.CloudResourceTemp{
				DiscoveryId:  discovery.Id,
				CloudId:      resource.CloudID,
				Name:         resource.Name,
				ResourceType: resource.Type,
				Region:       resource.Region,
				Zone:         resource.Zone,
				Status:       resource.Status,
				SpecInfo:     string(specInfoJson),
				NetworkInfo:  string(networkInfoJson),
				Tags:         string(tagsJson),
				RawData:      string(rawDataJson),
				Selected:     0,
				Imported:     0,
			}

			allResources = append(allResources, tempResource)
		}

		totalCount += len(resources)
	}

	// 批量保存临时资源
	if err := models.CloudResourceTempBatchSave(allResources); err != nil {
		log.Printf("Failed to save temp resources: %v", err)
		discovery.Status = "failed"
		discovery.ErrorMessage = fmt.Sprintf("Failed to save resources: %v", err)
		discovery.Update("status", "error_message")
		return
	}

	// 更新发现记录
	discovery.TotalCount = totalCount
	discovery.Status = "success"
	if err := discovery.Update("total_count", "status"); err != nil {
		log.Printf("Failed to update discovery record: %v", err)
	}

	// 执行重复检测
	log.Printf("Starting duplicate detection for discovery %d", discovery.Id)
	if err := s.DetectDuplicatesInDiscovery(discovery.Id); err != nil {
		log.Printf("Failed to detect duplicates for discovery %d: %v", discovery.Id, err)
	} else {
		log.Printf("Duplicate detection completed for discovery %d", discovery.Id)
	}

	log.Printf("Discovery %d completed, found %d resources", discovery.Id, totalCount)
}

// GetDiscoveryResources 获取发现的资源列表
func (s *CloudService) GetDiscoveryResources(discoveryId int64, limit, offset int) ([]models.CloudResourceTemp, int64, error) {
	return models.CloudResourceTempGetsByDiscoveryId(discoveryId, limit, offset)
}

// UpdateResourceSelection 更新资源选择状态
func (s *CloudService) UpdateResourceSelection(resourceIds []int64, selected bool) error {
	selectedValue := 0
	if selected {
		selectedValue = 1
	}
	return models.CloudResourceTempUpdateSelected(resourceIds, selectedValue)
}

// ImportSelectedResources 导入选中的资源（支持分层导入和重复处理）
func (s *CloudService) ImportSelectedResources(discoveryId int64, nodeId int64, resourceMapping map[string]cloud.ImportMapping, duplicateActions map[string]string, creator string) error {
	// 获取选中的资源
	selectedResources, err := models.CloudResourceTempGetSelected(discoveryId)
	if err != nil {
		return fmt.Errorf("failed to get selected resources: %v", err)
	}

	if len(selectedResources) == 0 {
		return fmt.Errorf("no resources selected for import")
	}

	// 检查节点是否存在
	node, err := models.NodeGet("id=?", nodeId)
	if err != nil {
		return fmt.Errorf("failed to get node: %v", err)
	}
	if node == nil {
		return fmt.Errorf("node not found")
	}

	importedCount := 0

	// 逐个导入资源
	for _, tempResource := range selectedResources {
		mapping, exists := resourceMapping[strconv.FormatInt(tempResource.Id, 10)]
		if !exists {
			log.Printf("No mapping found for resource %d, using default", tempResource.Id)
			mapping = cloud.ImportMapping{
				Cate:   s.getDefaultCate(tempResource.ResourceType),
				Tenant: "default",
				Note:   "Imported from cloud",
			}
		}

		// 检查是否有重复
		if tempResource.HasDuplicate {
			action, hasAction := duplicateActions[strconv.FormatInt(tempResource.Id, 10)]
			if !hasAction || action == "SKIP" {
				log.Printf("Skipping duplicate resource %s", tempResource.CloudId)
				continue
			}

			// 处理重复资源
			err = s.handleDuplicateResource(&tempResource, action, mapping, nodeId, creator)
			if err != nil {
				log.Printf("Failed to handle duplicate resource %s: %v", tempResource.CloudId, err)
				continue
			}
		} else {
			// 正常导入新资源
			err = s.importNewResource(&tempResource, mapping, nodeId, creator)
			if err != nil {
				log.Printf("Failed to import new resource %s: %v", tempResource.CloudId, err)
				continue
			}
		}

		// 标记为已导入
		tempResource.Imported = 1
		models.CloudResourceTempUpdate(&tempResource)

		importedCount++
	}

	// 更新发现记录的导入数量
	discovery, _ := models.CloudResourceDiscoveryGet("id=?", discoveryId)
	if discovery != nil {
		discovery.ImportedCount = importedCount
		discovery.Update("imported_count")
	}

	log.Printf("Imported %d resources to node %d", importedCount, nodeId)
	return nil
}

// getDefaultCate 获取默认分类
func (s *CloudService) getDefaultCate(resourceType string) string {
	switch resourceType {
	case "ecs":
		return "server"
	case "rds":
		return "database"
	case "redis":
		return "cache"
	case "slb", "clb":
		return "loadbalancer"
	default:
		return "other"
	}
}

// getProviderFromDiscovery 从发现记录获取云厂商
func (s *CloudService) getProviderFromDiscovery(discoveryId int64) string {
	discovery, err := models.CloudResourceDiscoveryGet("id=?", discoveryId)
	if err != nil || discovery == nil {
		return ""
	}

	config, err := models.CloudProviderConfigGet("id=?", discovery.ConfigId)
	if err != nil || config == nil {
		return ""
	}

	return config.Provider
}

// SyncResourceStatus 同步资源状态
func (s *CloudService) SyncResourceStatus(resourceIds []int64) error {
	// TODO: 实现资源状态同步逻辑
	// 1. 获取资源列表
	// 2. 根据云厂商分组
	// 3. 调用对应的云厂商API获取最新状态
	// 4. 更新本地资源状态

	log.Printf("Syncing status for %d resources", len(resourceIds))
	return fmt.Errorf("sync resource status not implemented yet")
}

// CleanupExpiredDiscoveries 清理过期的发现记录
func (s *CloudService) CleanupExpiredDiscoveries(days int) error {
	// 删除指定天数前的发现记录和相关临时资源
	cutoffTime := time.Now().AddDate(0, 0, -days)

	// 获取过期的发现记录
	discoveries, err := models.CloudResourceDiscoveryGets("created_at < ?", cutoffTime)
	if err != nil {
		return err
	}

	for _, discovery := range discoveries {
		// 删除临时资源
		models.CloudResourceTempDelByDiscoveryId(discovery.Id)

		// 删除发现记录
		models.DB["ams"].Where("id=?", discovery.Id).Delete(new(models.CloudResourceDiscovery))
	}

	log.Printf("Cleaned up %d expired discovery records", len(discoveries))
	return nil
}

// publishResourceBindEvent 发布资源绑定事件
func (s *CloudService) publishResourceBindEvent(nodeId, resourceId int64) {
	if !rdbEvents.IsJSEnabled() {
		return
	}

	// 获取节点和资源信息
	node, err := models.NodeGet("id=?", nodeId)
	if err != nil || node == nil {
		log.Printf("Failed to get node %d for resource bind event: %v", nodeId, err)
		return
	}

	resource, err := models.ResourceGet("id=?", resourceId)
	if err != nil || resource == nil {
		log.Printf("Failed to get resource %d for resource bind event: %v", resourceId, err)
		return
	}

	bindData := &jsEvents.ResourceBindEventData{
		NodeID:       nodeId,
		ResourceID:   resourceId,
		NodePath:     node.Path,
		ResourceUUID: resource.UUID,
	}

	if err := rdbEvents.GetJSProducer().PublishResourceBindEvent(jsEvents.EventResourceBind, bindData); err != nil {
		log.Printf("Failed to publish resource bind event for resource %s to node %s: %v", resource.Ident, node.Path, err)
	} else {
		log.Printf("Published resource bind event for cloud resource %s to node %s", resource.Ident, node.Path)
	}
}

// DetectDuplicatesInDiscovery 检测发现中的重复资源
func (s *CloudService) DetectDuplicatesInDiscovery(discoveryId int64) error {
	detector := &cloud.DuplicateDetector{}
	return detector.DetectDuplicatesInTempResources(discoveryId)
}

// getResourceHandler 获取资源处理器
func (s *CloudService) getResourceHandler(resourceType string) cloud.ResourceHandler {
	switch resourceType {
	case "ecs", "vm":
		return &cloud.HostResourceHandler{}
	case "rds", "mysql", "postgresql", "mongodb":
		return &cloud.DatabaseResourceHandler{}
	case "redis", "memcached":
		return &cloud.CacheResourceHandler{}
	default:
		return nil
	}
}

// handleDuplicateResource 处理重复资源
func (s *CloudService) handleDuplicateResource(tempResource *models.CloudResourceTemp, action string, mapping cloud.ImportMapping, nodeId int64, creator string) error {
	switch action {
	case "IGNORE":
		// 忽略重复，导入新资源
		tempResource.ResolveDuplicate("IGNORED")
		return s.importNewResource(tempResource, mapping, nodeId, creator)
	case "OVERRIDE":
		// 覆盖现有资源
		tempResource.ResolveDuplicate("OVERRIDDEN")
		return s.overrideExistingResource(tempResource, mapping, creator)
	default:
		return fmt.Errorf("unknown duplicate action: %s", action)
	}
}

// importNewResource 导入新资源
func (s *CloudService) importNewResource(tempResource *models.CloudResourceTemp, mapping cloud.ImportMapping, nodeId int64, creator string) error {
	// 获取资源处理器
	handler := s.getResourceHandler(tempResource.ResourceType)
	if handler == nil {
		return fmt.Errorf("unsupported resource type: %s", tempResource.ResourceType)
	}

	// 1. 在专门表中创建记录
	sourceId, err := handler.CreateFromTemp(tempResource, mapping)
	if err != nil {
		return fmt.Errorf("failed to create specialized record: %v", err)
	}

	// 2. 同步到Resource表
	resource, err := handler.SyncToResource(sourceId, mapping)
	if err != nil {
		// 清理已创建的专门表记录
		s.cleanupSpecializedRecord(tempResource.ResourceType, sourceId)
		return fmt.Errorf("failed to sync to resource table: %v", err)
	}

	// 3. 绑定到节点
	if err := models.NodeResourceBind(nodeId, resource.Id); err != nil {
		// 清理已创建的记录
		models.ResourceDel(resource.Id)
		s.cleanupSpecializedRecord(tempResource.ResourceType, sourceId)
		return fmt.Errorf("failed to bind to node: %v", err)
	}

	// 4. 发布同步事件
	//go s.publishResourceCreateEvent(resource)
	go s.publishResourceBindEvent(nodeId, resource.Id)

	return nil
}

// overrideExistingResource 覆盖现有资源
func (s *CloudService) overrideExistingResource(tempResource *models.CloudResourceTemp, mapping cloud.ImportMapping, creator string) error {
	// 获取资源处理器
	handler := s.getResourceHandler(tempResource.ResourceType)
	if handler == nil {
		return fmt.Errorf("unsupported resource type: %s", tempResource.ResourceType)
	}

	// 更新现有记录
	if tempResource.ExistingDeviceType == "host" {
		return handler.UpdateExisting(tempResource.ExistingDeviceId, tempResource, mapping)
	} else if tempResource.ExistingDeviceType == "resource" {
		// 处理resource表中的现有记录
		return s.updateExistingResourceRecord(tempResource, mapping)
	}

	return fmt.Errorf("unknown existing device type: %s", tempResource.ExistingDeviceType)
}

// cleanupSpecializedRecord 清理专门表记录
func (s *CloudService) cleanupSpecializedRecord(resourceType string, sourceId int64) {
	switch resourceType {
	case "ecs", "vm":
		if host, err := models.HostGet("id=?", sourceId); err == nil && host != nil {
			host.Del()
		}
	case "rds", "mysql", "postgresql", "mongodb":
		models.CloudDatabaseDel(sourceId)
	case "redis", "memcached":
		models.CloudCacheDel(sourceId)
	}
}

// updateExistingResourceRecord 更新现有资源记录
func (s *CloudService) updateExistingResourceRecord(tempResource *models.CloudResourceTemp, mapping cloud.ImportMapping) error {
	// 获取现有resource记录
	resource, err := models.ResourceGet("id=?", tempResource.ExistingDeviceId)
	if err != nil {
		return fmt.Errorf("failed to get existing resource: %v", err)
	}
	if resource == nil {
		return fmt.Errorf("existing resource not found: %d", tempResource.ExistingDeviceId)
	}

	// 更新resource记录
	resource.Name = tempResource.Name
	resource.Note = mapping.Note
	// 可以根据需要更新更多字段

	err = resource.Update("name", "note")
	if err != nil {
		return fmt.Errorf("failed to update existing resource: %v", err)
	}

	return nil
}

// GetDiscoveryResourcesWithDuplicates 获取发现的资源列表（包含重复信息）
func (s *CloudService) GetDiscoveryResourcesWithDuplicates(discoveryId int64, limit, offset int) ([]models.CloudResourceTemp, int64, error) {
	// 获取资源列表
	resources, err := models.CloudResourceTempGetsByDiscoveryIdWithPaging(discoveryId, limit, offset)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get resources: %v", err)
	}

	// 获取总数
	total, err := models.CloudResourceTempCountByDiscoveryId(discoveryId)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get total count: %v", err)
	}

	return resources, total, nil
}

// GetDiscoveryDuplicateStats 获取发现的重复统计信息
func (s *CloudService) GetDiscoveryDuplicateStats(discoveryId int64) (map[string]interface{}, error) {
	// 获取总资源数
	totalCount, err := models.CloudResourceTempCountByDiscoveryId(discoveryId)
	if err != nil {
		return nil, fmt.Errorf("failed to get total count: %v", err)
	}

	// 获取重复资源数
	duplicateCount, err := models.CloudResourceTempCountByDiscoveryIdAndDuplicate(discoveryId, true)
	if err != nil {
		return nil, fmt.Errorf("failed to get duplicate count: %v", err)
	}

	// 获取已解决的重复数
	resolvedCount, err := models.CloudResourceTempCountByDiscoveryIdAndStatus(discoveryId, []string{"IGNORED", "OVERRIDDEN"})
	if err != nil {
		return nil, fmt.Errorf("failed to get resolved count: %v", err)
	}

	// 计算待处理的重复数
	pendingCount := duplicateCount - resolvedCount

	stats := map[string]interface{}{
		"total_resources":     totalCount,
		"duplicate_resources": duplicateCount,
		"resolved_duplicates": resolvedCount,
		"pending_duplicates":  pendingCount,
	}

	return stats, nil
}
