package duplicate

import (
	"fmt"
	"log"
	"time"

	"arboris/src/models"
	"arboris/src/modules/ams/cloud"
)

// DuplicateDetector 重复检测器
type DuplicateDetector struct {
	config *models.DuplicateDetectionConfig
}

// NewDuplicateDetector 创建重复检测器
func NewDuplicateDetector(config *models.DuplicateDetectionConfig) *DuplicateDetector {
	if config == nil {
		config = &models.DuplicateDetectionConfig{
			Enabled:                         true,
			PreferPrivateIP:                 true,
			FallbackToPublicIP:              true,
			IgnoreEmptyIP:                   true,
			AutoIgnoreSameSource:            false,
			AutoOverrideManualWithDiscovery: false,
		}
	}

	return &DuplicateDetector{
		config: config,
	}
}

// DetectHostDuplicates 检测主机重复
func (d *DuplicateDetector) DetectHostDuplicates(discoveredResources []cloud.CloudResource) error {
	if !d.config.Enabled {
		return nil
	}

	for _, resource := range discoveredResources {
		// 获取设备的主要IP地址
		primaryIP := d.getPrimaryIPFromCloudResource(resource)
		if primaryIP == "" && d.config.IgnoreEmptyIP {
			continue
		}

		// 查询数据库中是否存在相同IP的主机
		existingHost, err := models.FindHostByIP(primaryIP)
		if err != nil {
			log.Printf("Error checking duplicate for IP %s: %v", primaryIP, err)
			continue
		}

		if existingHost != nil {
			// 检查是否需要自动忽略
			if d.shouldAutoIgnore(existingHost.DataSource, models.DataSourceDiscovery) {
				continue
			}

			// 发现重复，标记状态
			err = d.markHostAsDuplicate(resource, existingHost, primaryIP)
			if err != nil {
				log.Printf("Error marking host duplicate: %v", err)
			}
		}
	}
	return nil
}

// getPrimaryIPFromCloudResource 从云资源中获取主要IP地址
func (d *DuplicateDetector) getPrimaryIPFromCloudResource(resource cloud.CloudResource) string {
	if d.config.PreferPrivateIP {
		// 优先使用私网IP
		if privateIP, ok := resource.NetworkInfo["private_ip"].(string); ok && privateIP != "" {
			return privateIP
		}
	}

	if d.config.FallbackToPublicIP {
		// 备用公网IP
		if publicIP, ok := resource.NetworkInfo["public_ip"].(string); ok && publicIP != "" {
			return publicIP
		}
	}

	return ""
}

// shouldAutoIgnore 检查是否应该自动忽略
func (d *DuplicateDetector) shouldAutoIgnore(existingSource, discoveredSource models.DataSource) bool {
	// 相同数据源自动忽略
	if d.config.AutoIgnoreSameSource && existingSource == discoveredSource {
		return true
	}

	return false
}

// markHostAsDuplicate 标记主机为重复
func (d *DuplicateDetector) markHostAsDuplicate(discovered cloud.CloudResource, existing *models.Host, conflictIP string) error {
	// 计算字段差异
	differences := d.calculateHostDifferences(discovered, existing)

	// 构建重复信息
	duplicateInfo := models.DuplicateInfo{
		ConflictIP:       conflictIP,
		ExistingDeviceID: existing.Id,
		ExistingSource:   existing.DataSource,
		DiscoveredData:   discovered.RawData,
		DetectedAt:       time.Now(),
		Differences:      differences,
	}

	// 标记为重复
	err := existing.MarkAsDuplicate(&duplicateInfo)
	if err != nil {
		return err
	}

	// 更新数据库
	_, err = models.DB["ams"].ID(existing.Id).Update(existing)
	if err != nil {
		return err
	}

	// 记录重复检测记录
	return d.recordDuplicateDetection(models.DeviceTypeHost, existing.Id, 0, conflictIP)
}

// calculateHostDifferences 计算主机字段差异
func (d *DuplicateDetector) calculateHostDifferences(discovered cloud.CloudResource, existing *models.Host) []models.FieldDifference {
	var differences []models.FieldDifference

	// 比较关键字段
	fields := map[string]interface{}{
		"name":       discovered.Name,
		"status":     discovered.Status,
		"cpu":        d.getFromSpecInfo(discovered, "cpu"),
		"memory":     d.getFromSpecInfo(discovered, "memory"),
		"zone":       discovered.Zone,
		"public_ip":  d.getFromNetworkInfo(discovered, "public_ip"),
		"private_ip": d.getFromNetworkInfo(discovered, "private_ip"),
	}

	existingFields := map[string]interface{}{
		"name":       existing.Name,
		"status":     "", // Host表没有status字段
		"cpu":        existing.CPU,
		"memory":     existing.Mem,
		"zone":       existing.Zone,
		"public_ip":  "", // 需要从其他地方获取
		"private_ip": existing.IP,
	}

	for field, discoveredValue := range fields {
		existingValue := existingFields[field]
		isConflict := !d.valuesEqual(existingValue, discoveredValue)

		differences = append(differences, models.FieldDifference{
			Field:           field,
			ExistingValue:   existingValue,
			DiscoveredValue: discoveredValue,
			IsConflict:      isConflict,
		})
	}

	return differences
}

// 辅助方法
func (d *DuplicateDetector) getFromSpecInfo(resource cloud.CloudResource, key string) interface{} {
	if resource.SpecInfo != nil {
		return resource.SpecInfo[key]
	}
	return nil
}

func (d *DuplicateDetector) getFromNetworkInfo(resource cloud.CloudResource, key string) interface{} {
	if resource.NetworkInfo != nil {
		return resource.NetworkInfo[key]
	}
	return nil
}

func (d *DuplicateDetector) getFromExtend(extend map[string]interface{}, key string) interface{} {
	if extend != nil {
		return extend[key]
	}
	return nil
}

func (d *DuplicateDetector) valuesEqual(a, b interface{}) bool {
	// 简单的值比较，可以根据需要扩展
	return fmt.Sprintf("%v", a) == fmt.Sprintf("%v", b)
}

// recordDuplicateDetection 记录重复检测
func (d *DuplicateDetector) recordDuplicateDetection(deviceType models.DeviceType, deviceId1, deviceId2 int64, conflictIP string) error {
	duplicate := &models.DeviceDuplicate{
		DeviceType:      deviceType,
		DeviceId1:       deviceId1,
		DeviceId2:       deviceId2,
		ConflictIP:      conflictIP,
		SimilarityScore: 1.0, // 基于IP的完全匹配
		Status:          models.ResolutionStatusPending,
		CreatedAt:       time.Now(),
	}

	_, err := models.DB["rdb"].Insert(duplicate)
	return err
}
