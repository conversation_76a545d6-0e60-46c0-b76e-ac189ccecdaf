package duplicate

import (
	"fmt"
	"time"

	"arboris/src/models"
)

// DuplicateService 重复检测服务
type DuplicateService struct {
	detector *DuplicateDetector
}

// NewDuplicateService 创建重复检测服务
func NewDuplicateService(config *models.DuplicateDetectionConfig) *DuplicateService {
	return &DuplicateService{
		detector: NewDuplicateDetector(config),
	}
}

// resolveHostDuplicate 解决主机重复问题
func (s *DuplicateService) resolveHostDuplicate(req *models.ResolveDuplicateRequest, operator string) (*models.ResolveDuplicateResponse, error) {
	// 获取主机信息
	var host models.Host
	has, err := models.DB["ams"].ID(req.DeviceID).Get(&host)
	if err != nil {
		return nil, err
	}
	if !has {
		return &models.ResolveDuplicateResponse{
			Success: false,
			Message: "Host not found",
		}, nil
	}

	// 获取重复信息
	duplicateInfo, err := host.GetDuplicateInfo()
	if err != nil {
		return nil, err
	}
	if duplicateInfo == nil {
		return &models.ResolveDuplicateResponse{
			Success: false,
			Message: "No duplicate information found",
		}, nil
	}

	switch req.Action {
	case models.ActionOverride:
		// 用发现的数据覆盖现有数据
		err = s.overrideHostWithDiscoveredData(&host, duplicateInfo, req.KeepFields)
		if err != nil {
			return nil, err
		}

	case models.ActionIgnore:
		// 忽略重复，保持现有数据
		host.ResolveDuplicate(models.ActionIgnore)

	default:
		return &models.ResolveDuplicateResponse{
			Success: false,
			Message: fmt.Sprintf("Unsupported action: %s", req.Action),
		}, nil
	}

	// 更新数据库
	_, err = models.DB["ams"].ID(host.Id).Update(&host)
	if err != nil {
		return nil, err
	}

	// 记录解决历史
	err = s.recordResolutionHistory(models.DeviceTypeHost, host.Id, req.Action, operator)
	if err != nil {
		return nil, err
	}

	return &models.ResolveDuplicateResponse{
		Success: true,
		Message: "Host duplicate resolved successfully",
		Device:  host,
	}, nil
}

// overrideHostWithDiscoveredData 用发现的数据覆盖主机数据
func (s *DuplicateService) overrideHostWithDiscoveredData(host *models.Host, duplicateInfo *models.DuplicateInfo, keepFields []string) error {
	// 创建保留字段映射
	keepFieldsMap := make(map[string]bool)
	for _, field := range keepFields {
		keepFieldsMap[field] = true
	}

	// 从发现的数据中更新字段
	discoveredData := duplicateInfo.DiscoveredData

	if !keepFieldsMap["name"] {
		if name, ok := discoveredData["name"].(string); ok {
			host.Name = name
		}
	}

	if !keepFieldsMap["cpu"] {
		if cpu, ok := discoveredData["cpu"].(string); ok {
			host.CPU = cpu
		}
	}

	if !keepFieldsMap["memory"] {
		if mem, ok := discoveredData["memory"].(string); ok {
			host.Mem = mem
		}
	}

	// 更新数据源和时间
	host.DataSource = models.DataSourceDiscovery
	now := time.Now()
	host.LastDiscoveryAt = &now

	// 解决重复状态
	host.ResolveDuplicate(models.ActionOverride)

	return nil
}

// recordResolutionHistory 记录解决历史
func (s *DuplicateService) recordResolutionHistory(deviceType models.DeviceType, deviceId int64, action models.DuplicateAction, operator string) error {
	history := &models.DeviceMergeHistory{
		DeviceType:      deviceType,
		TargetDeviceId:  deviceId,
		SourceDeviceIds: fmt.Sprintf("[%d]", deviceId), // 简化处理
		MergeStrategy:   fmt.Sprintf(`{"action": "%s"}`, action),
		FieldChanges:    "[]", // 简化处理
		CreatedAt:       time.Now(),
		CreatedBy:       operator,
	}

	_, err := models.DB["rdb"].Insert(history)
	return err
}

// 辅助方法：转换数据格式
func (s *DuplicateService) convertHostsToDuplicateDevices(hosts []models.Host) []models.DuplicateDevice {
	var devices []models.DuplicateDevice
	for _, host := range hosts {
		duplicateInfo, _ := host.GetDuplicateInfo()
		if duplicateInfo == nil {
			duplicateInfo = &models.DuplicateInfo{}
		}

		device := models.DuplicateDevice{
			Id:              host.Id,
			Name:            host.Name,
			IP:              host.IP,
			DeviceType:      models.DeviceTypeHost,
			DataSource:      host.DataSource,
			DuplicateInfo:   *duplicateInfo,
			CreatedAt:       time.Now(), // 简化处理
			LastDiscoveryAt: host.LastDiscoveryAt,
		}
		devices = append(devices, device)
	}
	return devices
}
