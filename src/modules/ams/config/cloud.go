package config

import (
	"fmt"
	"path"

	"github.com/toolkits/pkg/file"
	"github.com/toolkits/pkg/runner"
)

// CloudConfig 云配置结构
type CloudConfig struct {
	Providers map[string]ProviderConfig `yaml:"providers"`
}

// ProviderConfig 云厂商配置
type ProviderConfig struct {
	Credentials   CredentialsConfig `yaml:"credentials"`
	Regions       []string          `yaml:"regions"`
	ResourceTypes []string          `yaml:"resource_types"`
	API           APIConfig         `yaml:"api"`
}

// CredentialsConfig 认证配置
type CredentialsConfig struct {
	AccessKey     string `yaml:"access_key"`
	SecretKey     string `yaml:"secret_key"`
	DefaultRegion string `yaml:"default_region"`
}

// APIConfig API配置
type APIConfig struct {
	Endpoint   string `yaml:"endpoint"`
	Version    string `yaml:"version"`
	Timeout    int    `yaml:"timeout"`
	MaxRetries int    `yaml:"max_retries"`
	RetryDelay int    `yaml:"retry_delay"`
}

var CloudConf *CloudConfig

// ParseCloudConfig 解析云配置文件
func ParseCloudConfig() error {
	ymlFile := getCloudConfigFile()
	if ymlFile == "" {
		return fmt.Errorf("cloud configuration file not found")
	}

	var c CloudConfig
	err := file.ReadYaml(ymlFile, &c)
	if err != nil {
		return fmt.Errorf("cannot read cloud config yml[%s]: %v", ymlFile, err)
	}

	CloudConf = &c
	fmt.Println("cloud.config.file:", ymlFile)

	return nil
}

// getCloudConfigFile 获取云配置文件路径
func getCloudConfigFile() string {
	confdir := path.Join(runner.Cwd, "etc")

	// 优先查找本地配置文件
	yml := path.Join(confdir, "cloud-config.local.yml")
	if file.IsExist(yml) {
		return yml
	}

	// 查找默认配置文件
	yml = path.Join(confdir, "cloud-config.yml")
	if file.IsExist(yml) {
		return yml
	}

	// 查找示例配置文件
	yml = path.Join(confdir, "cloud-config-example.yml")
	if file.IsExist(yml) {
		return yml
	}

	return ""
}

// GetProviderConfig 获取指定云厂商的配置
func GetProviderConfig(provider string) (*ProviderConfig, error) {
	if CloudConf == nil {
		if err := ParseCloudConfig(); err != nil {
			return nil, err
		}
	}

	config, exists := CloudConf.Providers[provider]
	if !exists {
		return nil, fmt.Errorf("provider %s not found in configuration", provider)
	}

	// 验证必要的配置
	if config.Credentials.AccessKey == "" {
		return nil, fmt.Errorf("access_key not configured for provider %s", provider)
	}

	if config.Credentials.SecretKey == "" {
		return nil, fmt.Errorf("secret_key not configured for provider %s", provider)
	}

	if config.Credentials.DefaultRegion == "" {
		return nil, fmt.Errorf("default_region not configured for provider %s", provider)
	}

	return &config, nil
}

// GetKingsoftConfig 获取金山云配置
func GetKingsoftConfig() (*ProviderConfig, error) {
	return GetProviderConfig("kingsoft")
}

// GetVolcanoConfig 获取火山云配置
func GetVolcanoConfig() (*ProviderConfig, error) {
	return GetProviderConfig("volcano")
}

// GetAliyunConfig 获取阿里云配置
func GetAliyunConfig() (*ProviderConfig, error) {
	return GetProviderConfig("aliyun")
}

// GetTencentConfig 获取腾讯云配置
func GetTencentConfig() (*ProviderConfig, error) {
	return GetProviderConfig("tencent")
}

// GetCtyunConfig 获取天翼云配置
func GetCtyunConfig() (*ProviderConfig, error) {
	return GetProviderConfig("ctyun")
}

// GetAllProviders 获取所有已配置的云厂商列表
func GetAllProviders() []string {
	if CloudConf == nil {
		if err := ParseCloudConfig(); err != nil {
			return []string{}
		}
	}

	var providers []string
	for provider, config := range CloudConf.Providers {
		// 只返回已配置认证信息的厂商
		if config.Credentials.AccessKey != "" && config.Credentials.SecretKey != "" {
			providers = append(providers, provider)
		}
	}

	return providers
}

// IsProviderConfigured 检查指定云厂商是否已配置
func IsProviderConfigured(provider string) bool {
	config, err := GetProviderConfig(provider)
	if err != nil {
		return false
	}

	return config.Credentials.AccessKey != "" && config.Credentials.SecretKey != ""
}

// ValidateProviderConfig 验证云厂商配置
func ValidateProviderConfig(provider string) error {
	config, err := GetProviderConfig(provider)
	if err != nil {
		return err
	}

	// 验证必要字段
	if config.Credentials.AccessKey == "" {
		return fmt.Errorf("access_key is required for provider %s", provider)
	}

	if config.Credentials.SecretKey == "" {
		return fmt.Errorf("secret_key is required for provider %s", provider)
	}

	if config.Credentials.DefaultRegion == "" {
		return fmt.Errorf("default_region is required for provider %s", provider)
	}

	// 验证区域是否在支持列表中
	regionSupported := false
	for _, region := range config.Regions {
		if region == config.Credentials.DefaultRegion {
			regionSupported = true
			break
		}
	}

	if !regionSupported {
		return fmt.Errorf("default_region %s is not in supported regions for provider %s", 
			config.Credentials.DefaultRegion, provider)
	}

	return nil
}

// GetProviderCredentials 获取云厂商认证信息
func GetProviderCredentials(provider string) (accessKey, secretKey, region string, err error) {
	config, err := GetProviderConfig(provider)
	if err != nil {
		return "", "", "", err
	}

	return config.Credentials.AccessKey, config.Credentials.SecretKey, config.Credentials.DefaultRegion, nil
}

// GetProviderRegions 获取云厂商支持的区域列表
func GetProviderRegions(provider string) ([]string, error) {
	config, err := GetProviderConfig(provider)
	if err != nil {
		return nil, err
	}

	return config.Regions, nil
}

// GetProviderResourceTypes 获取云厂商支持的资源类型
func GetProviderResourceTypes(provider string) ([]string, error) {
	config, err := GetProviderConfig(provider)
	if err != nil {
		return nil, err
	}

	return config.ResourceTypes, nil
}

// GetProviderAPIConfig 获取云厂商API配置
func GetProviderAPIConfig(provider string) (*APIConfig, error) {
	config, err := GetProviderConfig(provider)
	if err != nil {
		return nil, err
	}

	return &config.API, nil
}

// SetProviderCredentials 设置云厂商认证信息（用于动态配置）
func SetProviderCredentials(provider, accessKey, secretKey, region string) error {
	if CloudConf == nil {
		if err := ParseCloudConfig(); err != nil {
			return err
		}
	}

	if CloudConf.Providers == nil {
		CloudConf.Providers = make(map[string]ProviderConfig)
	}

	config := CloudConf.Providers[provider]
	config.Credentials.AccessKey = accessKey
	config.Credentials.SecretKey = secretKey
	if region != "" {
		config.Credentials.DefaultRegion = region
	}
	CloudConf.Providers[provider] = config

	return nil
}

// ReloadCloudConfig 重新加载云配置
func ReloadCloudConfig() error {
	return ParseCloudConfig()
}
