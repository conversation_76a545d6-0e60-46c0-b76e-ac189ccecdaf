package http

import (
	"log"

	"github.com/gin-gonic/gin"
	"github.com/toolkits/pkg/ginx"

	"arboris/src/models"
	"arboris/src/modules/ams/cloud"
	"arboris/src/modules/ams/service"
)

// Router 路由结构体
type Router struct{}

// NewRouter 创建路由实例
func NewRouter() *Router {
	return &Router{}
}

// 云厂商配置相关路由
func (rt *Router) cloudConfigRoutes(r *gin.RouterGroup) {
	r.GET("/cloud/configs", rt.cloudConfigList)
	r.POST("/cloud/configs", rt.cloudConfigCreate)
	r.GET("/cloud/configs/:id", rt.cloudConfigGet)
	r.PUT("/cloud/configs/:id", rt.cloudConfigUpdate)
	r.DELETE("/cloud/configs/:id", rt.cloudConfigDelete)
	r.POST("/cloud/configs/:id/test", rt.cloudConfigTest)
}

// 云资源发现相关路由
func (rt *Router) cloudDiscoveryRoutes(r *gin.RouterGroup) {
	r.POST("/cloud/discover", rt.cloudDiscover)
	r.GET("/cloud/discoveries", rt.cloudDiscoveryList)
	r.GET("/cloud/discoveries/:id", rt.cloudDiscoveryGet)
	r.GET("/cloud/discoveries/:id/resources", rt.cloudDiscoveryResources)
	r.GET("/cloud/discoveries/:id/resources/duplicates", rt.cloudDiscoveryResourcesWithDuplicates)
	r.PUT("/cloud/discoveries/:id/resources/select", rt.cloudResourceSelect)
	r.POST("/cloud/discoveries/:id/import", rt.cloudResourceImport)
}

// 云资源管理相关路由（已简化，使用分层导入架构）
// 资源管理通过分层导入流程处理，不再需要单独的资源管理接口

// 云厂商配置管理

// cloudConfigList 获取云厂商配置列表
func (rt *Router) cloudConfigList(c *gin.Context) {
	username := headerUsername(c)
	if username == "" {
		bomb("unauthorized")
	}

	configs, err := models.CloudProviderConfigGets("1=1")
	dangerous(err)

	// 隐藏敏感信息
	for i := range configs {
		configs[i].AccessKey = "***"
		configs[i].SecretKey = "***"
	}

	ginx.NewRender(c).Data(configs, nil)
}

// cloudConfigCreate 创建云厂商配置
func (rt *Router) cloudConfigCreate(c *gin.Context) {
	username := headerUsername(c)
	if username == "" {
		bomb("unauthorized")
	}

	var req models.CloudProviderConfig
	bind(c, &req)

	// 验证必填字段
	if req.Name == "" {
		bomb("name is required")
	}
	if req.Provider == "" {
		bomb("provider is required")
	}
	if req.Region == "" {
		bomb("region is required")
	}
	if req.AccessKey == "" {
		bomb("access_key is required")
	}
	if req.SecretKey == "" {
		bomb("secret_key is required")
	}

	// 验证云厂商类型
	supportedProviders := cloud.GetSupportedProviders()
	found := false
	for _, provider := range supportedProviders {
		if provider == req.Provider {
			found = true
			break
		}
	}
	if !found {
		bomb("unsupported provider: %s", req.Provider)
	}

	req.Creator = username
	req.Status = 1 // 默认启用

	dangerous(req.Save())

	// 隐藏敏感信息
	req.AccessKey = "***"
	req.SecretKey = "***"

	ginx.NewRender(c).Data(req, nil)
}

// cloudConfigGet 获取云厂商配置详情
func (rt *Router) cloudConfigGet(c *gin.Context) {
	username := headerUsername(c)
	if username == "" {
		bomb("unauthorized")
	}

	id := urlParamInt64(c, "id")
	config, err := models.CloudProviderConfigGet("id=?", id)
	dangerous(err)

	if config == nil {
		bomb("cloud config not found")
	}

	// 隐藏敏感信息
	config.AccessKey = "***"
	config.SecretKey = "***"

	ginx.NewRender(c).Data(config, nil)
}

// cloudConfigUpdate 更新云厂商配置
func (rt *Router) cloudConfigUpdate(c *gin.Context) {
	username := headerUsername(c)
	if username == "" {
		bomb("unauthorized")
	}

	id := urlParamInt64(c, "id")
	config, err := models.CloudProviderConfigGet("id=?", id)
	dangerous(err)

	if config == nil {
		bomb("cloud config not found")
	}

	var req models.CloudProviderConfig
	bind(c, &req)

	// 更新字段
	updateCols := []string{}

	if req.Name != "" {
		config.Name = req.Name
		updateCols = append(updateCols, "name")
	}
	if req.Region != "" {
		config.Region = req.Region
		updateCols = append(updateCols, "region")
	}
	if req.AccessKey != "" && req.AccessKey != "***" {
		config.AccessKey = req.AccessKey
		updateCols = append(updateCols, "access_key")
	}
	if req.SecretKey != "" && req.SecretKey != "***" {
		config.SecretKey = req.SecretKey
		updateCols = append(updateCols, "secret_key")
	}
	if req.Endpoint != config.Endpoint {
		config.Endpoint = req.Endpoint
		updateCols = append(updateCols, "endpoint")
	}
	if req.Description != config.Description {
		config.Description = req.Description
		updateCols = append(updateCols, "description")
	}
	if req.Status != config.Status {
		config.Status = req.Status
		updateCols = append(updateCols, "status")
	}

	if len(updateCols) > 0 {
		dangerous(config.Update(updateCols...))
	}

	// 隐藏敏感信息
	config.AccessKey = "***"
	config.SecretKey = "***"

	ginx.NewRender(c).Data(config, nil)
}

// cloudConfigDelete 删除云厂商配置
func (rt *Router) cloudConfigDelete(c *gin.Context) {
	username := headerUsername(c)
	if username == "" {
		bomb("unauthorized")
	}

	id := urlParamInt64(c, "id")

	// 检查是否有关联的发现记录
	discoveries, err := models.CloudResourceDiscoveryGets("config_id=?", id)
	dangerous(err)

	if len(discoveries) > 0 {
		bomb("cannot delete config with existing discovery records")
	}

	dangerous(models.CloudProviderConfigDel(id))

	ginx.NewRender(c).Data("ok", nil)
}

// cloudConfigTest 测试云厂商连接
func (rt *Router) cloudConfigTest(c *gin.Context) {
	username := headerUsername(c)
	if username == "" {
		bomb("unauthorized")
	}

	id := urlParamInt64(c, "id")

	cloudService := service.NewCloudService()
	err := cloudService.TestConnection(id)
	if err != nil {
		ginx.NewRender(c).Data(gin.H{
			"success": false,
			"message": err.Error(),
		}, nil)
		return
	}

	ginx.NewRender(c).Data(gin.H{
		"success": true,
		"message": "connection test successful",
	}, nil)
}

// 云资源发现

// cloudDiscover 发现云资源
func (rt *Router) cloudDiscover(c *gin.Context) {
	username := headerUsername(c)
	if username == "" {
		bomb("unauthorized")
	}

	var req struct {
		ConfigId      int64                  `json:"config_id"`
		ResourceTypes []string               `json:"resource_types"`
		Filters       cloud.DiscoveryFilters `json:"filters"`
	}
	bind(c, &req)

	if req.ConfigId == 0 {
		bomb("config_id is required")
	}
	if len(req.ResourceTypes) == 0 {
		bomb("resource_types is required")
	}

	cloudService := service.NewCloudService()
	discovery, err := cloudService.DiscoverResources(req.ConfigId, req.ResourceTypes, req.Filters, username)
	dangerous(err)

	ginx.NewRender(c).Data(discovery, nil)
}

// cloudDiscoveryList 获取发现记录列表
func (rt *Router) cloudDiscoveryList(c *gin.Context) {
	username := headerUsername(c)
	if username == "" {
		bomb("unauthorized")
	}

	discoveries, err := models.CloudResourceDiscoveryGets("1=1")
	dangerous(err)

	ginx.NewRender(c).Data(discoveries, nil)
}

// cloudDiscoveryGet 获取发现记录详情
func (rt *Router) cloudDiscoveryGet(c *gin.Context) {
	username := headerUsername(c)
	if username == "" {
		bomb("unauthorized")
	}

	id := urlParamInt64(c, "id")
	discovery, err := models.CloudResourceDiscoveryGet("id=?", id)
	dangerous(err)

	if discovery == nil {
		bomb("discovery record not found")
	}

	ginx.NewRender(c).Data(discovery, nil)
}

// cloudDiscoveryResources 获取发现的资源列表
func (rt *Router) cloudDiscoveryResources(c *gin.Context) {
	username := headerUsername(c)
	if username == "" {
		bomb("unauthorized")
	}

	id := urlParamInt64(c, "id")

	// 分页参数
	limit := queryInt(c, "limit", 20)
	offset := queryInt(c, "offset", 0)

	if limit > 100 {
		limit = 100
	}

	cloudService := service.NewCloudService()
	resources, total, err := cloudService.GetDiscoveryResources(id, limit, offset)
	dangerous(err)

	ginx.NewRender(c).Data(gin.H{
		"list":  resources,
		"total": total,
	}, nil)
}

// cloudResourceSelect 选择/取消选择资源
func (rt *Router) cloudResourceSelect(c *gin.Context) {
	username := headerUsername(c)
	if username == "" {
		bomb("unauthorized")
	}

	var req struct {
		ResourceIds []int64 `json:"resource_ids"`
		Selected    bool    `json:"selected"`
	}
	bind(c, &req)

	if len(req.ResourceIds) == 0 {
		bomb("resource_ids is required")
	}

	cloudService := service.NewCloudService()
	err := cloudService.UpdateResourceSelection(req.ResourceIds, req.Selected)
	dangerous(err)

	ginx.NewRender(c).Data("ok", nil)
}

// cloudResourceImport 导入选中的资源
func (rt *Router) cloudResourceImport(c *gin.Context) {
	username := headerUsername(c)
	if username == "" {
		bomb("unauthorized")
	}

	id := urlParamInt64(c, "id")

	var req cloud.CloudResourceImportRequest
	bind(c, &req)

	if req.NodeId == 0 {
		bomb("node_id is required")
	}

	cloudService := service.NewCloudService()
	err := cloudService.ImportSelectedResources(id, req.NodeId, req.ResourceMapping, req.DuplicateActions, username)
	dangerous(err)

	ginx.NewRender(c).Data("ok", nil)
}

// 云资源管理（使用分层导入架构，通过发现和导入流程管理资源）

// cloudDiscoveryResourcesWithDuplicates 获取发现的资源列表（包含重复信息）
func (rt *Router) cloudDiscoveryResourcesWithDuplicates(c *gin.Context) {
	username := headerUsername(c)
	if username == "" {
		bomb("unauthorized")
	}

	id := urlParamInt64(c, "id")
	limit := queryInt(c, "limit", 20)
	offset := queryInt(c, "offset", 0)

	if limit > 100 {
		limit = 100
	}

	cloudService := service.NewCloudService()
	resources, total, err := cloudService.GetDiscoveryResourcesWithDuplicates(id, limit, offset)
	dangerous(err)

	// 统计重复信息
	duplicateStats, err := cloudService.GetDiscoveryDuplicateStats(id)
	if err != nil {
		log.Printf("Failed to get duplicate stats: %v", err)
		duplicateStats = map[string]interface{}{
			"total_resources":     total,
			"duplicate_resources": 0,
			"resolved_duplicates": 0,
			"pending_duplicates":  0,
		}
	}

	ginx.NewRender(c).Data(gin.H{
		"list":            resources,
		"total":           total,
		"duplicate_stats": duplicateStats,
	}, nil)
}
