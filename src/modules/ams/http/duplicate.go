package http

import (
	"github.com/gin-gonic/gin"

	"arboris/src/models"
	"arboris/src/modules/ams/duplicate"
)

// DuplicateHandler 重复检测处理器
type DuplicateHandler struct {
	service *duplicate.DuplicateService
}

// NewDuplicateHandler 创建重复检测处理器
func NewDuplicateHandler() *DuplicateHandler {
	// 从配置中加载重复检测配置
	config := loadDuplicateDetectionConfig()
	service := duplicate.NewDuplicateService(config)

	return &DuplicateHandler{
		service: service,
	}
}

// 辅助函数
func getOperatorFromContext(c *gin.Context) string {
	// 从认证中间件获取用户信息
	if user, exists := c.Get("user"); exists {
		if userModel, ok := user.(*models.User); ok {
			return userModel.Username
		}
	}

	// 如果没有认证信息，返回默认值
	return "system"
}

func loadDuplicateDetectionConfig() *models.DuplicateDetectionConfig {
	// 从数据库配置表加载配置
	config := &models.DuplicateDetectionConfig{
		Enabled:                         true,
		PreferPrivateIP:                 true,
		FallbackToPublicIP:              true,
		IgnoreEmptyIP:                   true,
		AutoIgnoreSameSource:            false,
		AutoOverrideManualWithDiscovery: false,
	}

	// 尝试从数据库加载配置
	if enabledConfig, err := models.ConfigsGet("duplicate_detection_enabled"); err == nil && enabledConfig != "" {
		config.Enabled = enabledConfig == "true"
	}

	if preferPrivateIPConfig, err := models.ConfigsGet("duplicate_detection_prefer_private_ip"); err == nil && preferPrivateIPConfig != "" {
		config.PreferPrivateIP = preferPrivateIPConfig == "true"
	}

	if fallbackToPublicIPConfig, err := models.ConfigsGet("duplicate_detection_fallback_to_public_ip"); err == nil && fallbackToPublicIPConfig != "" {
		config.FallbackToPublicIP = fallbackToPublicIPConfig == "true"
	}

	if ignoreEmptyIPConfig, err := models.ConfigsGet("duplicate_detection_ignore_empty_ip"); err == nil && ignoreEmptyIPConfig != "" {
		config.IgnoreEmptyIP = ignoreEmptyIPConfig == "true"
	}

	if autoIgnoreSameSourceConfig, err := models.ConfigsGet("duplicate_detection_auto_ignore_same_source"); err == nil && autoIgnoreSameSourceConfig != "" {
		config.AutoIgnoreSameSource = autoIgnoreSameSourceConfig == "true"
	}

	if autoOverrideConfig, err := models.ConfigsGet("duplicate_detection_auto_override_manual_with_discovery"); err == nil && autoOverrideConfig != "" {
		config.AutoOverrideManualWithDiscovery = autoOverrideConfig == "true"
	}

	return config
}
