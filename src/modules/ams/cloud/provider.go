package cloud

import (
	"fmt"
	"time"
)

// CloudResource 云资源结构
type CloudResource struct {
	CloudID     string                 `json:"cloud_id"`
	Name        string                 `json:"name"`
	Type        string                 `json:"type"`
	Region      string                 `json:"region"`
	Zone        string                 `json:"zone"`
	Status      string                 `json:"status"`
	SpecInfo    map[string]interface{} `json:"spec_info"`
	NetworkInfo map[string]interface{} `json:"network_info"`
	Tags        map[string]string      `json:"tags"`
	RawData     map[string]interface{} `json:"raw_data"`
	CreatedTime time.Time              `json:"created_time"`
	UpdatedTime time.Time              `json:"updated_time"`
}

// CloudProvider 云厂商接口
type CloudProvider interface {
	// 测试连接
	TestConnection() error
	
	// 获取支持的资源类型
	GetSupportedResourceTypes() []string
	
	// 发现资源
	DiscoverResources(resourceType string, filters map[string]string) ([]CloudResource, error)
	
	// 获取资源详情
	GetResourceDetail(resourceType, resourceId string) (*CloudResource, error)
	
	// 同步资源状态
	SyncResourceStatus(resources []CloudResource) error
}

// ProviderConfig 云厂商配置
type ProviderConfig struct {
	Provider  string
	Region    string
	AccessKey string
	SecretKey string
	Endpoint  string
}

// NewCloudProvider 创建云厂商实例（从配置文件读取认证信息）
func NewCloudProvider(provider string) (CloudProvider, error) {
	switch provider {
	case "kingsoft":
		return NewKingsoftProvider()
	case "volcano":
		return NewVolcanoProvider()
	case "aliyun":
		return NewAliyunProvider()
	case "tencent":
		return NewTencentProvider()
	case "ctyun":
		return NewCtyunProvider()
	default:
		return nil, fmt.Errorf("unsupported cloud provider: %s", provider)
	}
}

// NewCloudProviderWithConfig 使用指定配置创建云厂商实例（兼容旧接口）
func NewCloudProviderWithConfig(config ProviderConfig) (CloudProvider, error) {
	switch config.Provider {
	case "kingsoft":
		return NewKingsoftProviderWithConfig(config), nil
	case "volcano":
		return NewVolcanoProviderWithConfig(config), nil
	case "aliyun":
		return NewAliyunProviderWithConfig(config), nil
	case "tencent":
		return NewTencentProviderWithConfig(config), nil
	case "ctyun":
		return NewCtyunProviderWithConfig(config), nil
	default:
		return nil, fmt.Errorf("unsupported cloud provider: %s", config.Provider)
	}
}

// GetSupportedProviders 获取支持的云厂商列表
func GetSupportedProviders() []string {
	return []string{"kingsoft", "volcano", "aliyun", "tencent", "ctyun"}
}

// GetSupportedResourceTypes 获取支持的资源类型
func GetSupportedResourceTypes(provider string) []string {
	switch provider {
	case "kingsoft":
		return []string{"ecs", "epc", "rds", "redis", "slb"}
	case "volcano":
		return []string{"ecs", "rds", "redis", "clb"}
	case "aliyun":
		return []string{"ecs", "rds", "redis", "slb", "vpc"}
	case "tencent":
		return []string{"cvm", "cdb", "redis", "clb", "vpc"}
	case "ctyun":
		return []string{"ecs", "rds", "redis", "elb", "vpc"}
	default:
		return []string{}
	}
}

// ResourceTypeMapping 资源类型映射
var ResourceTypeMapping = map[string]map[string]string{
	"kingsoft": {
		"ecs":   "云服务器",
		"rds":   "云数据库",
		"redis": "云缓存",
		"slb":   "负载均衡",
	},
	"volcano": {
		"ecs":   "云服务器",
		"rds":   "云数据库",
		"redis": "云缓存",
		"clb":   "负载均衡",
	},
	"aliyun": {
		"ecs":   "云服务器",
		"rds":   "云数据库",
		"redis": "云缓存",
		"slb":   "负载均衡",
		"vpc":   "专有网络",
	},
	"tencent": {
		"cvm":   "云服务器",
		"cdb":   "云数据库",
		"redis": "云缓存",
		"clb":   "负载均衡",
		"vpc":   "私有网络",
	},
	"ctyun": {
		"ecs":   "云服务器",
		"rds":   "云数据库",
		"redis": "云缓存",
		"elb":   "负载均衡",
		"vpc":   "虚拟私有云",
	},
}

// GetResourceTypeName 获取资源类型中文名
func GetResourceTypeName(provider, resourceType string) string {
	if mapping, ok := ResourceTypeMapping[provider]; ok {
		if name, exists := mapping[resourceType]; exists {
			return name
		}
	}
	return resourceType
}

// DiscoveryFilters 发现过滤器
type DiscoveryFilters struct {
	Zone     string   `json:"zone"`
	Status   []string `json:"status"`
	Tags     map[string]string `json:"tags"`
	NameLike string   `json:"name_like"`
}

// ToMap 转换为map
func (f DiscoveryFilters) ToMap() map[string]string {
	result := make(map[string]string)
	
	if f.Zone != "" {
		result["zone"] = f.Zone
	}
	
	if len(f.Status) > 0 {
		// 将状态数组转换为逗号分隔的字符串
		statusStr := ""
		for i, status := range f.Status {
			if i > 0 {
				statusStr += ","
			}
			statusStr += status
		}
		result["status"] = statusStr
	}
	
	if f.NameLike != "" {
		result["name_like"] = f.NameLike
	}
	
	// 处理标签
	for key, value := range f.Tags {
		result["tag:"+key] = value
	}
	
	return result
}

// ImportMapping 导入映射配置
type ImportMapping struct {
	Cate   string `json:"cate"`
	Tenant string `json:"tenant"`
	Note   string `json:"note"`
}

// CloudResourceImportRequest 云资源导入请求
type CloudResourceImportRequest struct {
	NodeId           int64                    `json:"node_id"`
	ResourceMapping  map[string]ImportMapping `json:"resource_mapping"`  // key为临时资源ID
	DuplicateActions map[string]string        `json:"duplicate_actions"` // key为临时资源ID，value为处理动作：IGNORE/OVERRIDE/SKIP
}

// CloudResourceSyncRequest 云资源同步请求
type CloudResourceSyncRequest struct {
	ResourceIds []int64 `json:"resource_ids"`
}
