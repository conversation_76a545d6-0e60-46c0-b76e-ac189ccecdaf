package cloud

import (
	"encoding/json"
	"fmt"
	"log"

	"arboris/src/models"
)

// DuplicateDetector 重复检测器
type DuplicateDetector struct{}

// DuplicateInfo 重复信息结构
type DuplicateInfo struct {
	ConflictIP         string                 `json:"conflict_ip"`
	ExistingHost       *models.Host           `json:"existing_host,omitempty"`
	ExistingResource   *models.Resource       `json:"existing_resource,omitempty"`
	DetectedAt         string                 `json:"detected_at"`
	Differences        []DifferenceInfo       `json:"differences"`
}

// DifferenceInfo 差异信息
type DifferenceInfo struct {
	Field           string `json:"field"`
	ExistingValue   string `json:"existing_value"`
	DiscoveredValue string `json:"discovered_value"`
	ConflictLevel   string `json:"conflict_level"` // minor, major, critical
}

// DetectDuplicatesInTempResources 检测临时资源中的重复
func (d *DuplicateDetector) DetectDuplicatesInTempResources(discoveryId int64) error {
	// 获取发现的临时资源
	tempResources, err := models.CloudResourceTempGetsByDiscoveryIdSimple(discoveryId)
	if err != nil {
		return fmt.Errorf("failed to get temp resources: %v", err)
	}

	for _, tempResource := range tempResources {
		// 根据资源类型进行重复检测
		switch tempResource.ResourceType {
		case "ecs", "vm":
			err = d.detectHostDuplicate(&tempResource)
		case "rds", "mysql", "postgresql", "mongodb":
			err = d.detectDatabaseDuplicate(&tempResource)
		case "redis", "memcached":
			err = d.detectCacheDuplicate(&tempResource)
		default:
			// 对于其他类型，暂时跳过重复检测
			continue
		}
		
		if err != nil {
			log.Printf("Error detecting duplicate for resource %s: %v", tempResource.CloudId, err)
		}
	}
	
	return nil
}

// detectHostDuplicate 检测主机重复
func (d *DuplicateDetector) detectHostDuplicate(tempResource *models.CloudResourceTemp) error {
	// 从网络信息中提取IP
	primaryIP := d.extractPrimaryIP(tempResource.NetworkInfo)
	if primaryIP == "" {
		return nil // 没有IP信息，无法检测重复
	}

	// 检查host表中是否存在相同IP
	existingHost, err := models.HostGet("ip=?", primaryIP)
	if err != nil {
		return fmt.Errorf("failed to check existing host: %v", err)
	}

	if existingHost != nil {
		// 构建重复信息
		duplicateInfo := &DuplicateInfo{
			ConflictIP:    primaryIP,
			ExistingHost:  existingHost,
			DetectedAt:    fmt.Sprintf("%v", tempResource.CreatedAt),
			Differences:   d.calculateHostDifferences(tempResource, existingHost),
		}

		// 标记为重复
		err = tempResource.MarkAsDuplicate(primaryIP, existingHost.Id, "host", duplicateInfo)
		if err != nil {
			return fmt.Errorf("failed to mark as duplicate: %v", err)
		}

		// 更新数据库
		return models.CloudResourceTempUpdate(tempResource)
	}

	// 检查resource表中是否也存在相同IP的资源
	existingResource, err := d.findResourceByIP(primaryIP)
	if err != nil {
		return fmt.Errorf("failed to check existing resource: %v", err)
	}

	if existingResource != nil {
		// 构建重复信息
		duplicateInfo := &DuplicateInfo{
			ConflictIP:       primaryIP,
			ExistingResource: existingResource,
			DetectedAt:       fmt.Sprintf("%v", tempResource.CreatedAt),
			Differences:      d.calculateResourceDifferences(tempResource, existingResource),
		}

		// 标记为重复
		err = tempResource.MarkAsDuplicate(primaryIP, existingResource.Id, "resource", duplicateInfo)
		if err != nil {
			return fmt.Errorf("failed to mark as duplicate: %v", err)
		}

		// 更新数据库
		return models.CloudResourceTempUpdate(tempResource)
	}

	return nil
}

// detectDatabaseDuplicate 检测数据库重复
func (d *DuplicateDetector) detectDatabaseDuplicate(tempResource *models.CloudResourceTemp) error {
	// 对于数据库资源，主要通过cloud_id检测重复
	existingDatabase, err := models.CloudDatabaseGet("cloud_id=? AND cloud_provider=?", 
		tempResource.CloudId, d.getCloudProvider(tempResource))
	if err != nil {
		return fmt.Errorf("failed to check existing database: %v", err)
	}

	if existingDatabase != nil {
		// 构建重复信息
		duplicateInfo := &DuplicateInfo{
			ConflictIP:   "", // 数据库可能没有直接的IP冲突
			DetectedAt:   fmt.Sprintf("%v", tempResource.CreatedAt),
			Differences:  d.calculateDatabaseDifferences(tempResource, existingDatabase),
		}

		// 标记为重复
		err = tempResource.MarkAsDuplicate("", existingDatabase.Id, "cloud_database", duplicateInfo)
		if err != nil {
			return fmt.Errorf("failed to mark as duplicate: %v", err)
		}

		// 更新数据库
		return models.CloudResourceTempUpdate(tempResource)
	}

	return nil
}

// detectCacheDuplicate 检测缓存重复
func (d *DuplicateDetector) detectCacheDuplicate(tempResource *models.CloudResourceTemp) error {
	// 对于缓存资源，主要通过cloud_id检测重复
	existingCache, err := models.CloudCacheGet("cloud_id=? AND cloud_provider=?", 
		tempResource.CloudId, d.getCloudProvider(tempResource))
	if err != nil {
		return fmt.Errorf("failed to check existing cache: %v", err)
	}

	if existingCache != nil {
		// 构建重复信息
		duplicateInfo := &DuplicateInfo{
			ConflictIP:   "", // 缓存可能没有直接的IP冲突
			DetectedAt:   fmt.Sprintf("%v", tempResource.CreatedAt),
			Differences:  d.calculateCacheDifferences(tempResource, existingCache),
		}

		// 标记为重复
		err = tempResource.MarkAsDuplicate("", existingCache.Id, "cloud_cache", duplicateInfo)
		if err != nil {
			return fmt.Errorf("failed to mark as duplicate: %v", err)
		}

		// 更新数据库
		return models.CloudResourceTempUpdate(tempResource)
	}

	return nil
}

// extractPrimaryIP 从网络信息中提取主要IP
func (d *DuplicateDetector) extractPrimaryIP(networkInfoJSON string) string {
	if networkInfoJSON == "" {
		return ""
	}

	var networkInfo map[string]interface{}
	err := json.Unmarshal([]byte(networkInfoJSON), &networkInfo)
	if err != nil {
		return ""
	}

	// 优先使用私网IP
	if privateIP, ok := networkInfo["private_ip"].(string); ok && privateIP != "" {
		return privateIP
	}

	// 备用公网IP
	if publicIP, ok := networkInfo["public_ip"].(string); ok && publicIP != "" {
		return publicIP
	}

	// 尝试从IP列表中获取
	if ips, ok := networkInfo["ip_addresses"].([]interface{}); ok && len(ips) > 0 {
		if ip, ok := ips[0].(string); ok {
			return ip
		}
	}

	return ""
}

// findResourceByIP 通过IP查找资源
func (d *DuplicateDetector) findResourceByIP(ip string) (*models.Resource, error) {
	// 在resource表的extend字段中搜索IP
	resources, err := models.ResourceGets("")
	if err != nil {
		return nil, err
	}

	for _, resource := range resources {
		if d.resourceContainsIP(&resource, ip) {
			return &resource, nil
		}
	}

	return nil, nil
}

// resourceContainsIP 检查资源是否包含指定IP
func (d *DuplicateDetector) resourceContainsIP(resource *models.Resource, ip string) bool {
	if resource.Extend == "" {
		return false
	}

	var extend map[string]interface{}
	err := json.Unmarshal([]byte(resource.Extend), &extend)
	if err != nil {
		return false
	}

	// 检查各种IP字段
	ipFields := []string{"ip", "private_ip", "public_ip", "internal_ip", "external_ip"}
	for _, field := range ipFields {
		if value, ok := extend[field].(string); ok && value == ip {
			return true
		}
	}

	return false
}

// calculateHostDifferences 计算主机差异
func (d *DuplicateDetector) calculateHostDifferences(tempResource *models.CloudResourceTemp, existingHost *models.Host) []DifferenceInfo {
	var differences []DifferenceInfo

	// 比较名称
	if tempResource.Name != existingHost.Name {
		differences = append(differences, DifferenceInfo{
			Field:           "name",
			ExistingValue:   existingHost.Name,
			DiscoveredValue: tempResource.Name,
			ConflictLevel:   "minor",
		})
	}

	// 比较状态
	if tempResource.Status != existingHost.CloudStatus {
		differences = append(differences, DifferenceInfo{
			Field:           "status",
			ExistingValue:   existingHost.CloudStatus,
			DiscoveredValue: tempResource.Status,
			ConflictLevel:   "major",
		})
	}

	// 可以添加更多字段的比较...

	return differences
}

// calculateResourceDifferences 计算资源差异
func (d *DuplicateDetector) calculateResourceDifferences(tempResource *models.CloudResourceTemp, existingResource *models.Resource) []DifferenceInfo {
	var differences []DifferenceInfo

	// 比较名称
	if tempResource.Name != existingResource.Name {
		differences = append(differences, DifferenceInfo{
			Field:           "name",
			ExistingValue:   existingResource.Name,
			DiscoveredValue: tempResource.Name,
			ConflictLevel:   "minor",
		})
	}

	return differences
}

// calculateDatabaseDifferences 计算数据库差异
func (d *DuplicateDetector) calculateDatabaseDifferences(tempResource *models.CloudResourceTemp, existingDatabase *models.CloudDatabase) []DifferenceInfo {
	var differences []DifferenceInfo

	// 比较名称
	if tempResource.Name != existingDatabase.Name {
		differences = append(differences, DifferenceInfo{
			Field:           "name",
			ExistingValue:   existingDatabase.Name,
			DiscoveredValue: tempResource.Name,
			ConflictLevel:   "minor",
		})
	}

	// 比较状态
	if tempResource.Status != existingDatabase.Status {
		differences = append(differences, DifferenceInfo{
			Field:           "status",
			ExistingValue:   existingDatabase.Status,
			DiscoveredValue: tempResource.Status,
			ConflictLevel:   "major",
		})
	}

	return differences
}

// calculateCacheDifferences 计算缓存差异
func (d *DuplicateDetector) calculateCacheDifferences(tempResource *models.CloudResourceTemp, existingCache *models.CloudCache) []DifferenceInfo {
	var differences []DifferenceInfo

	// 比较名称
	if tempResource.Name != existingCache.Name {
		differences = append(differences, DifferenceInfo{
			Field:           "name",
			ExistingValue:   existingCache.Name,
			DiscoveredValue: tempResource.Name,
			ConflictLevel:   "minor",
		})
	}

	// 比较状态
	if tempResource.Status != existingCache.Status {
		differences = append(differences, DifferenceInfo{
			Field:           "status",
			ExistingValue:   existingCache.Status,
			DiscoveredValue: tempResource.Status,
			ConflictLevel:   "major",
		})
	}

	return differences
}

// getCloudProvider 获取云厂商信息
func (d *DuplicateDetector) getCloudProvider(tempResource *models.CloudResourceTemp) string {
	discovery, err := models.CloudResourceDiscoveryGet("id=?", tempResource.DiscoveryId)
	if err != nil || discovery == nil {
		return ""
	}
	
	config, err := models.CloudProviderConfigGet("id=?", discovery.ConfigId)
	if err != nil || config == nil {
		return ""
	}
	
	return config.Provider
}
