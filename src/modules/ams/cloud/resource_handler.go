package cloud

import (
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"arboris/src/models"
)

// ResourceHandler 资源处理器接口
type ResourceHandler interface {
	// 从临时资源创建专门表记录
	CreateFromTemp(tempResource *models.CloudResourceTemp, mapping ImportMapping) (int64, error)
	
	// 从专门表同步到Resource表
	SyncToResource(sourceId int64, mapping ImportMapping) (*models.Resource, error)
	
	// 获取资源类型
	GetResourceType() string
	
	// 更新现有记录（用于重复处理）
	UpdateExisting(sourceId int64, tempResource *models.CloudResourceTemp, mapping ImportMapping) error
}

// HostResourceHandler ECS/VM资源处理器
type HostResourceHandler struct{}

func (h *HostResourceHandler) GetResourceType() string {
	return "compute_instance"
}

func (h *HostResourceHandler) CreateFromTemp(tempResource *models.CloudResourceTemp, mapping ImportMapping) (int64, error) {
	// 解析云资源数据
	var specInfo map[string]interface{}
	if tempResource.SpecInfo != "" {
		json.Unmarshal([]byte(tempResource.SpecInfo), &specInfo)
	}
	
	var networkInfo map[string]interface{}
	if tempResource.NetworkInfo != "" {
		json.Unmarshal([]byte(tempResource.NetworkInfo), &networkInfo)
	}

	// 创建Host记录
	host := &models.Host{
		Ident:         tempResource.CloudId,
		Name:          tempResource.Name,
		IP:            h.getIPFromNetworkInfo(networkInfo),
		Cate:          "vm", // 云主机默认为vm
		Tenant:        mapping.Tenant,
		Note:          mapping.Note,
		// 从specInfo中提取CPU、内存等信息
		CPU:           h.extractCPU(specInfo),
		Mem:           h.extractMemory(specInfo),
		Disk:          h.extractDisk(specInfo),
		// 云资源特有字段
		CloudId:       tempResource.CloudId,
		CloudProvider: h.getCloudProvider(tempResource),
		CloudRegion:   tempResource.Region,
		CloudZone:     tempResource.Zone,
		InstanceType:  h.getStringFromSpec(specInfo, "instance_type"),
		ImageId:       h.getStringFromSpec(specInfo, "image_id"),
		VpcId:         h.getStringFromNetwork(networkInfo, "vpc_id"),
		SubnetId:      h.getStringFromNetwork(networkInfo, "subnet_id"),
		PublicIP:      h.getStringFromNetwork(networkInfo, "public_ip"),
		PrivateIP:     h.getStringFromNetwork(networkInfo, "private_ip"),
		CloudStatus:   tempResource.Status,
		CloudTags:     tempResource.Tags,
		CloudRawData:  tempResource.RawData,
		DataSource:    models.DataSourceDiscovery,
	}

	err := host.Save()
	if err != nil {
		return 0, fmt.Errorf("failed to save host: %v", err)
	}

	return host.Id, nil
}

func (h *HostResourceHandler) SyncToResource(sourceId int64, mapping ImportMapping) (*models.Resource, error) {
	// 获取Host记录
	host, err := models.HostGet("id=?", sourceId)
	if err != nil {
		return nil, fmt.Errorf("failed to get host: %v", err)
	}
	if host == nil {
		return nil, fmt.Errorf("host not found: %d", sourceId)
	}

	// 创建Resource记录
	resource := &models.Resource{
		UUID:       fmt.Sprintf("host-%d", host.Id),
		Ident:      host.Ident,
		Name:       host.Name,
		Cate:       mapping.Cate,
		Tenant:     mapping.Tenant,
		Note:       mapping.Note,
		SourceId:   host.Id,
		SourceType: "host",
		// 构建extend字段
		Extend:     h.buildExtendFromHost(host),
	}

	err = resource.Save()
	if err != nil {
		return nil, fmt.Errorf("failed to save resource: %v", err)
	}

	return resource, nil
}

func (h *HostResourceHandler) UpdateExisting(sourceId int64, tempResource *models.CloudResourceTemp, mapping ImportMapping) error {
	// 获取现有Host记录
	host, err := models.HostGet("id=?", sourceId)
	if err != nil {
		return fmt.Errorf("failed to get existing host: %v", err)
	}
	if host == nil {
		return fmt.Errorf("existing host not found: %d", sourceId)
	}

	// 解析新的云资源数据
	var specInfo map[string]interface{}
	if tempResource.SpecInfo != "" {
		json.Unmarshal([]byte(tempResource.SpecInfo), &specInfo)
	}
	
	var networkInfo map[string]interface{}
	if tempResource.NetworkInfo != "" {
		json.Unmarshal([]byte(tempResource.NetworkInfo), &networkInfo)
	}

	// 更新Host记录
	host.Name = tempResource.Name
	host.CloudStatus = tempResource.Status
	host.CloudRegion = tempResource.Region
	host.CloudZone = tempResource.Zone
	host.InstanceType = h.getStringFromSpec(specInfo, "instance_type")
	host.ImageId = h.getStringFromSpec(specInfo, "image_id")
	host.VpcId = h.getStringFromNetwork(networkInfo, "vpc_id")
	host.SubnetId = h.getStringFromNetwork(networkInfo, "subnet_id")
	host.PublicIP = h.getStringFromNetwork(networkInfo, "public_ip")
	host.PrivateIP = h.getStringFromNetwork(networkInfo, "private_ip")
	host.CloudTags = tempResource.Tags
	host.CloudRawData = tempResource.RawData
	host.LastDiscoveryAt = &time.Time{}
	*host.LastDiscoveryAt = time.Now()

	// 更新CPU、内存、磁盘信息
	host.CPU = h.extractCPU(specInfo)
	host.Mem = h.extractMemory(specInfo)
	host.Disk = h.extractDisk(specInfo)

	// 保存更新
	err = host.UpdateCols("name", "cloud_status", "cloud_region", "cloud_zone", "instance_type",
		"image_id", "vpc_id", "subnet_id", "public_ip", "private_ip", "cloud_tags",
		"cloud_raw_data", "last_discovery_at", "cpu", "mem", "disk")
	if err != nil {
		return fmt.Errorf("failed to update host: %v", err)
	}

	return nil
}

// 辅助方法
func (h *HostResourceHandler) getIPFromNetworkInfo(networkInfo map[string]interface{}) string {
	// 优先使用私网IP
	if privateIP, ok := networkInfo["private_ip"].(string); ok && privateIP != "" {
		return privateIP
	}
	// 备用公网IP
	if publicIP, ok := networkInfo["public_ip"].(string); ok && publicIP != "" {
		return publicIP
	}
	return ""
}

func (h *HostResourceHandler) extractCPU(specInfo map[string]interface{}) string {
	if cpu, ok := specInfo["cpu"]; ok {
		return fmt.Sprintf("%v", cpu)
	}
	return ""
}

func (h *HostResourceHandler) extractMemory(specInfo map[string]interface{}) string {
	if mem, ok := specInfo["memory"]; ok {
		return fmt.Sprintf("%v", mem)
	}
	return ""
}

func (h *HostResourceHandler) extractDisk(specInfo map[string]interface{}) string {
	if disk, ok := specInfo["disk"]; ok {
		return fmt.Sprintf("%v", disk)
	}
	return ""
}

func (h *HostResourceHandler) getStringFromSpec(specInfo map[string]interface{}, key string) string {
	if value, ok := specInfo[key]; ok {
		return fmt.Sprintf("%v", value)
	}
	return ""
}

func (h *HostResourceHandler) getStringFromNetwork(networkInfo map[string]interface{}, key string) string {
	if value, ok := networkInfo[key]; ok {
		return fmt.Sprintf("%v", value)
	}
	return ""
}

func (h *HostResourceHandler) getCloudProvider(tempResource *models.CloudResourceTemp) string {
	// 从discovery记录中获取云厂商信息
	discovery, err := models.CloudResourceDiscoveryGet("id=?", tempResource.DiscoveryId)
	if err != nil || discovery == nil {
		return ""
	}
	
	config, err := models.CloudProviderConfigGet("id=?", discovery.ConfigId)
	if err != nil || config == nil {
		return ""
	}
	
	return config.Provider
}

func (h *HostResourceHandler) buildExtendFromHost(host *models.Host) string {
	extend := map[string]interface{}{
		"cloud_id":       host.CloudId,
		"cloud_provider": host.CloudProvider,
		"cloud_region":   host.CloudRegion,
		"cloud_zone":     host.CloudZone,
		"instance_type":  host.InstanceType,
		"image_id":       host.ImageId,
		"vpc_id":         host.VpcId,
		"subnet_id":      host.SubnetId,
		"public_ip":      host.PublicIP,
		"private_ip":     host.PrivateIP,
		"cloud_status":   host.CloudStatus,
		"cpu":            host.CPU,
		"mem":            host.Mem,
		"disk":           host.Disk,
	}
	
	extendJSON, _ := json.Marshal(extend)
	return string(extendJSON)
}

// DatabaseResourceHandler 数据库资源处理器
type DatabaseResourceHandler struct{}

func (d *DatabaseResourceHandler) GetResourceType() string {
	return "database_instance"
}

func (d *DatabaseResourceHandler) CreateFromTemp(tempResource *models.CloudResourceTemp, mapping ImportMapping) (int64, error) {
	// 解析云资源数据
	var specInfo map[string]interface{}
	if tempResource.SpecInfo != "" {
		json.Unmarshal([]byte(tempResource.SpecInfo), &specInfo)
	}

	var networkInfo map[string]interface{}
	if tempResource.NetworkInfo != "" {
		json.Unmarshal([]byte(tempResource.NetworkInfo), &networkInfo)
	}

	// 创建CloudDatabase记录
	database := &models.CloudDatabase{
		CloudId:         tempResource.CloudId,
		Name:            tempResource.Name,
		Engine:          d.getStringFromSpec(specInfo, "engine"),
		Version:         d.getStringFromSpec(specInfo, "version"),
		InstanceClass:   d.getStringFromSpec(specInfo, "instance_class"),
		StorageSize:     d.getIntFromSpec(specInfo, "storage_size"),
		StorageType:     d.getStringFromSpec(specInfo, "storage_type"),
		ConnectionString: d.getStringFromSpec(specInfo, "connection_string"),
		Port:            d.getIntFromSpec(specInfo, "port"),
		VpcId:           d.getStringFromNetwork(networkInfo, "vpc_id"),
		SubnetId:        d.getStringFromNetwork(networkInfo, "subnet_id"),
		Status:          tempResource.Status,
		Region:          tempResource.Region,
		Zone:            tempResource.Zone,
		CloudProvider:   d.getCloudProvider(tempResource),
		SpecInfo:        tempResource.SpecInfo,
		NetworkInfo:     tempResource.NetworkInfo,
		Tags:            tempResource.Tags,
		RawData:         tempResource.RawData,
		DataSource:      models.DataSourceDiscovery,
	}

	err := database.Save()
	if err != nil {
		return 0, fmt.Errorf("failed to save cloud database: %v", err)
	}

	return database.Id, nil
}

func (d *DatabaseResourceHandler) SyncToResource(sourceId int64, mapping ImportMapping) (*models.Resource, error) {
	// 获取CloudDatabase记录
	database, err := models.CloudDatabaseGet("id=?", sourceId)
	if err != nil {
		return nil, fmt.Errorf("failed to get cloud database: %v", err)
	}
	if database == nil {
		return nil, fmt.Errorf("cloud database not found: %d", sourceId)
	}

	// 创建Resource记录
	resource := &models.Resource{
		UUID:       fmt.Sprintf("cloud-database-%d", database.Id),
		Ident:      database.CloudId,
		Name:       database.Name,
		Cate:       mapping.Cate,
		Tenant:     mapping.Tenant,
		Note:       mapping.Note,
		SourceId:   database.Id,
		SourceType: "cloud_database",
		Extend:     d.buildExtendFromDatabase(database),
	}

	err = resource.Save()
	if err != nil {
		return nil, fmt.Errorf("failed to save resource: %v", err)
	}

	return resource, nil
}

func (d *DatabaseResourceHandler) UpdateExisting(sourceId int64, tempResource *models.CloudResourceTemp, mapping ImportMapping) error {
	// 获取现有CloudDatabase记录
	database, err := models.CloudDatabaseGet("id=?", sourceId)
	if err != nil {
		return fmt.Errorf("failed to get existing cloud database: %v", err)
	}
	if database == nil {
		return fmt.Errorf("existing cloud database not found: %d", sourceId)
	}

	// 解析新的云资源数据
	var specInfo map[string]interface{}
	if tempResource.SpecInfo != "" {
		json.Unmarshal([]byte(tempResource.SpecInfo), &specInfo)
	}

	var networkInfo map[string]interface{}
	if tempResource.NetworkInfo != "" {
		json.Unmarshal([]byte(tempResource.NetworkInfo), &networkInfo)
	}

	// 更新CloudDatabase记录
	database.Name = tempResource.Name
	database.Status = tempResource.Status
	database.Region = tempResource.Region
	database.Zone = tempResource.Zone
	database.Engine = d.getStringFromSpec(specInfo, "engine")
	database.Version = d.getStringFromSpec(specInfo, "version")
	database.InstanceClass = d.getStringFromSpec(specInfo, "instance_class")
	database.StorageSize = d.getIntFromSpec(specInfo, "storage_size")
	database.StorageType = d.getStringFromSpec(specInfo, "storage_type")
	database.ConnectionString = d.getStringFromSpec(specInfo, "connection_string")
	database.Port = d.getIntFromSpec(specInfo, "port")
	database.VpcId = d.getStringFromNetwork(networkInfo, "vpc_id")
	database.SubnetId = d.getStringFromNetwork(networkInfo, "subnet_id")
	database.SpecInfo = tempResource.SpecInfo
	database.NetworkInfo = tempResource.NetworkInfo
	database.Tags = tempResource.Tags
	database.RawData = tempResource.RawData
	database.LastDiscoveryAt = &time.Time{}
	*database.LastDiscoveryAt = time.Now()

	// 保存更新
	err = database.Update("name", "status", "region", "zone", "engine", "version",
		"instance_class", "storage_size", "storage_type", "connection_string",
		"port", "vpc_id", "subnet_id", "spec_info", "network_info", "tags",
		"raw_data", "last_discovery_at")
	if err != nil {
		return fmt.Errorf("failed to update cloud database: %v", err)
	}

	return nil
}

// 数据库处理器的辅助方法
func (d *DatabaseResourceHandler) getStringFromSpec(specInfo map[string]interface{}, key string) string {
	if value, ok := specInfo[key]; ok {
		return fmt.Sprintf("%v", value)
	}
	return ""
}

func (d *DatabaseResourceHandler) getIntFromSpec(specInfo map[string]interface{}, key string) int {
	if value, ok := specInfo[key]; ok {
		if intVal, err := strconv.Atoi(fmt.Sprintf("%v", value)); err == nil {
			return intVal
		}
	}
	return 0
}

func (d *DatabaseResourceHandler) getStringFromNetwork(networkInfo map[string]interface{}, key string) string {
	if value, ok := networkInfo[key]; ok {
		return fmt.Sprintf("%v", value)
	}
	return ""
}

func (d *DatabaseResourceHandler) getCloudProvider(tempResource *models.CloudResourceTemp) string {
	// 从discovery记录中获取云厂商信息
	discovery, err := models.CloudResourceDiscoveryGet("id=?", tempResource.DiscoveryId)
	if err != nil || discovery == nil {
		return ""
	}

	config, err := models.CloudProviderConfigGet("id=?", discovery.ConfigId)
	if err != nil || config == nil {
		return ""
	}

	return config.Provider
}

func (d *DatabaseResourceHandler) buildExtendFromDatabase(database *models.CloudDatabase) string {
	extend := map[string]interface{}{
		"cloud_id":          database.CloudId,
		"cloud_provider":    database.CloudProvider,
		"cloud_region":      database.Region,
		"cloud_zone":        database.Zone,
		"engine":            database.Engine,
		"version":           database.Version,
		"instance_class":    database.InstanceClass,
		"storage_size":      database.StorageSize,
		"storage_type":      database.StorageType,
		"connection_string": database.ConnectionString,
		"port":              database.Port,
		"vpc_id":            database.VpcId,
		"subnet_id":         database.SubnetId,
		"status":            database.Status,
	}

	extendJSON, _ := json.Marshal(extend)
	return string(extendJSON)
}

// CacheResourceHandler 缓存资源处理器
type CacheResourceHandler struct{}

func (c *CacheResourceHandler) GetResourceType() string {
	return "cache_instance"
}

func (c *CacheResourceHandler) CreateFromTemp(tempResource *models.CloudResourceTemp, mapping ImportMapping) (int64, error) {
	// 解析云资源数据
	var specInfo map[string]interface{}
	if tempResource.SpecInfo != "" {
		json.Unmarshal([]byte(tempResource.SpecInfo), &specInfo)
	}

	var networkInfo map[string]interface{}
	if tempResource.NetworkInfo != "" {
		json.Unmarshal([]byte(tempResource.NetworkInfo), &networkInfo)
	}

	// 创建CloudCache记录
	cache := &models.CloudCache{
		CloudId:          tempResource.CloudId,
		Name:             tempResource.Name,
		Engine:           c.getStringFromSpec(specInfo, "engine"),
		Version:          c.getStringFromSpec(specInfo, "version"),
		InstanceClass:    c.getStringFromSpec(specInfo, "instance_class"),
		MemorySize:       c.getIntFromSpec(specInfo, "memory_size"),
		ShardCount:       c.getIntFromSpec(specInfo, "shard_count"),
		ReplicaCount:     c.getIntFromSpec(specInfo, "replica_count"),
		ConnectionString: c.getStringFromSpec(specInfo, "connection_string"),
		Port:             c.getIntFromSpec(specInfo, "port"),
		VpcId:            c.getStringFromNetwork(networkInfo, "vpc_id"),
		SubnetId:         c.getStringFromNetwork(networkInfo, "subnet_id"),
		AuthEnabled:      c.getBoolFromSpec(specInfo, "auth_enabled"),
		SslEnabled:       c.getBoolFromSpec(specInfo, "ssl_enabled"),
		BackupEnabled:    c.getBoolFromSpec(specInfo, "backup_enabled"),
		Status:           tempResource.Status,
		Region:           tempResource.Region,
		Zone:             tempResource.Zone,
		CloudProvider:    c.getCloudProvider(tempResource),
		SpecInfo:         tempResource.SpecInfo,
		NetworkInfo:      tempResource.NetworkInfo,
		Tags:             tempResource.Tags,
		RawData:          tempResource.RawData,
		DataSource:       models.DataSourceDiscovery,
	}

	err := cache.Save()
	if err != nil {
		return 0, fmt.Errorf("failed to save cloud cache: %v", err)
	}

	return cache.Id, nil
}
