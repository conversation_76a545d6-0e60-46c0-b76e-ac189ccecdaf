package cloud

import (
	"encoding/json"
	"fmt"
	"log"
	"strconv"
	"strings"
	"time"

	"github.com/volcengine/volcengine-go-sdk/service/ecs"
	"github.com/volcengine/volcengine-go-sdk/volcengine"
	"github.com/volcengine/volcengine-go-sdk/volcengine/credentials"
	"github.com/volcengine/volcengine-go-sdk/volcengine/session"

	amsConfig "arboris/src/modules/ams/config"
)

// VolcanoProvider 火山云提供商
type VolcanoProvider struct {
	config    ProviderConfig
	ecsClient *ecs.ECS
}

// NewVolcanoProvider 创建火山云提供商实例（从配置文件读取认证信息）
func NewVolcanoProvider() (*VolcanoProvider, error) {
	// 从配置文件读取火山云配置
	cloudConfig, err := amsConfig.GetVolcanoConfig()
	if err != nil {
		return nil, fmt.Errorf("failed to get volcano config: %v", err)
	}

	// 转换为ProviderConfig
	config := ProviderConfig{
		Provider:  "volcano",
		Region:    cloudConfig.Credentials.DefaultRegion,
		AccessKey: cloudConfig.Credentials.AccessKey,
		SecretKey: cloudConfig.Credentials.SecretKey,
		Endpoint:  cloudConfig.API.Endpoint,
	}

	return NewVolcanoProviderWithConfig(config), nil
}

// NewVolcanoProviderWithConfig 使用指定配置创建火山云提供商实例
func NewVolcanoProviderWithConfig(config ProviderConfig) *VolcanoProvider {
	// 创建认证信息
	credential := credentials.NewStaticCredentials(config.AccessKey, config.SecretKey, "")

	// 创建配置
	volcConfig := volcengine.NewConfig().
		WithRegion(config.Region).
		WithCredentials(credential)

	// 如果有自定义端点，设置端点
	if config.Endpoint != "" {
		volcConfig = volcConfig.WithEndpoint(config.Endpoint)
	}

	// 创建会话
	sess, err := session.NewSession(volcConfig)
	if err != nil {
		log.Printf("Failed to create volcano session: %v", err)
		return &VolcanoProvider{
			config: config,
		}
	}

	// 创建ECS客户端
	ecsClient := ecs.New(sess)

	return &VolcanoProvider{
		config:    config,
		ecsClient: ecsClient,
	}
}

// TestConnection 测试连接
func (v *VolcanoProvider) TestConnection() error {
	log.Printf("Testing Volcano Cloud connection for region: %s", v.config.Region)

	if v.ecsClient == nil {
		return fmt.Errorf("ECS client not initialized")
	}

	// 调用DescribeInstances接口测试连接，限制返回1个实例
	input := &ecs.DescribeInstancesInput{
		MaxResults: volcengine.Int32(1),
	}

	_, err := v.ecsClient.DescribeInstances(input)
	if err != nil {
		return fmt.Errorf("failed to connect to Volcano Cloud: %v", err)
	}

	log.Printf("Successfully connected to Volcano Cloud in region: %s", v.config.Region)
	return nil
}

// GetSupportedResourceTypes 获取支持的资源类型
func (v *VolcanoProvider) GetSupportedResourceTypes() []string {
	return []string{"ecs", "rds", "redis", "clb"}
}

// DiscoverResources 发现资源
func (v *VolcanoProvider) DiscoverResources(resourceType string, filters map[string]string) ([]CloudResource, error) {
	switch resourceType {
	case "ecs":
		return v.discoverECSInstances(filters)
	case "rds":
		return v.discoverRDSInstances(filters)
	case "redis":
		return v.discoverRedisInstances(filters)
	case "clb":
		return v.discoverCLBInstances(filters)
	default:
		return nil, fmt.Errorf("unsupported resource type: %s", resourceType)
	}
}

// GetResourceDetail 获取资源详情
func (v *VolcanoProvider) GetResourceDetail(resourceType, resourceId string) (*CloudResource, error) {
	switch resourceType {
	case "ecs":
		return v.getECSInstanceDetail(resourceId)
	default:
		return nil, fmt.Errorf("unsupported resource type: %s", resourceType)
	}
}

// SyncResourceStatus 同步资源状态
func (v *VolcanoProvider) SyncResourceStatus(resources []CloudResource) error {
	for _, resource := range resources {
		switch resource.Type {
		case "ecs":
			if err := v.syncECSInstanceStatus(&resource); err != nil {
				log.Printf("Failed to sync ECS instance %s status: %v", resource.CloudID, err)
			}
		default:
			log.Printf("Unsupported resource type for sync: %s", resource.Type)
		}
	}
	return nil
}

// discoverECSInstances 发现ECS实例
func (v *VolcanoProvider) discoverECSInstances(filters map[string]string) ([]CloudResource, error) {
	log.Printf("Discovering Volcano ECS instances with filters: %+v", filters)

	if v.ecsClient == nil {
		return nil, fmt.Errorf("ECS client not initialized")
	}

	var allResources []CloudResource

	// 构建请求参数
	input := &ecs.DescribeInstancesInput{}

	// 设置分页参数
	maxResults := int32(100) // 默认每页100个
	if maxResultsStr, ok := filters["max_results"]; ok && maxResultsStr != "" {
		if mr, err := strconv.Atoi(maxResultsStr); err == nil && mr > 0 && mr <= 1000 {
			maxResults = int32(mr)
		}
	}
	input.MaxResults = volcengine.Int32(maxResults)

	// 应用可用区过滤器
	if zone, ok := filters["zone"]; ok && zone != "" {
		input.ZoneId = volcengine.String(zone)
	}

	// 应用实例ID过滤器
	if instanceIds, ok := filters["instance_ids"]; ok && instanceIds != "" {
		ids := strings.Split(instanceIds, ",")
		var instanceIdList []*string
		for _, id := range ids {
			if trimmedId := strings.TrimSpace(id); trimmedId != "" {
				instanceIdList = append(instanceIdList, volcengine.String(trimmedId))
			}
		}
		if len(instanceIdList) > 0 {
			input.InstanceIds = instanceIdList
		}
	}

	// 应用名称过滤器（精确匹配）
	if instanceName, ok := filters["instance_name"]; ok && instanceName != "" {
		input.InstanceName = volcengine.String(instanceName)
	}

	// 分页获取所有实例
	var nextToken *string
	for {
		if nextToken != nil {
			input.NextToken = nextToken
		}

		// 调用API
		result, err := v.ecsClient.DescribeInstances(input)
		if err != nil {
			return nil, fmt.Errorf("failed to describe instances: %v", err)
		}

		// 转换为CloudResource
		var pageResources []CloudResource
		for _, instance := range result.Instances {
			resource := v.convertECSInstanceToCloudResource(instance)
			pageResources = append(pageResources, resource)
		}

		// 应用基本的客户端过滤器
		for _, resource := range pageResources {
			shouldInclude := true

			// 应用状态过滤器
			if statusFilter, ok := filters["status"]; ok && statusFilter != "" {
				if !strings.Contains(statusFilter, strings.ToLower(resource.Status)) {
					shouldInclude = false
				}
			}

			// 应用名称模糊匹配过滤器
			if nameLike, ok := filters["name_like"]; ok && nameLike != "" {
				if !strings.Contains(strings.ToLower(resource.Name), strings.ToLower(nameLike)) {
					shouldInclude = false
				}
			}

			if shouldInclude {
				allResources = append(allResources, resource)
			}
		}

		// 检查是否还有更多页
		if result.NextToken == nil || *result.NextToken == "" {
			break
		}
		nextToken = result.NextToken

		// 防止无限循环，最多获取10页
		if len(allResources) >= 1000 {
			log.Printf("Warning: Reached maximum limit of 1000 instances, there might be more")
			break
		}
	}

	// 应用高级客户端过滤器
	filteredResources := v.applyClientSideFilters(allResources, filters)

	// 应用排序
	if sortBy, ok := filters["sort"]; ok && sortBy != "" {
		filteredResources = v.sortResources(filteredResources, sortBy)
	}

	// 检测重复（如果启用）
	if enableDuplicateDetection, ok := filters["enable_duplicate_detection"]; ok && enableDuplicateDetection == "true" {
		err := v.detectDuplicates(filteredResources)
		if err != nil {
			log.Printf("Error detecting duplicates: %v", err)
		}
	}

	log.Printf("Discovered %d ECS instances (filtered from %d)", len(filteredResources), len(allResources))
	return filteredResources, nil
}

// discoverRDSInstances 发现RDS实例
func (v *VolcanoProvider) discoverRDSInstances(filters map[string]string) ([]CloudResource, error) {
	log.Printf("Discovering Volcano RDS instances with filters: %+v", filters)
	
	// TODO: 实现火山云RDS API调用
	var resources []CloudResource
	return resources, nil
}

// discoverRedisInstances 发现Redis实例
func (v *VolcanoProvider) discoverRedisInstances(filters map[string]string) ([]CloudResource, error) {
	log.Printf("Discovering Volcano Redis instances with filters: %+v", filters)
	
	// TODO: 实现火山云Redis API调用
	var resources []CloudResource
	return resources, nil
}

// discoverCLBInstances 发现CLB实例
func (v *VolcanoProvider) discoverCLBInstances(filters map[string]string) ([]CloudResource, error) {
	log.Printf("Discovering Volcano CLB instances with filters: %+v", filters)

	// TODO: 实现火山云CLB API调用
	var resources []CloudResource
	return resources, nil
}

// convertECSInstanceToCloudResource 转换ECS实例为CloudResource
func (v *VolcanoProvider) convertECSInstanceToCloudResource(instance interface{}) CloudResource {
	// 构建原始数据
	rawData := make(map[string]interface{})
	if data, err := json.Marshal(instance); err == nil {
		json.Unmarshal(data, &rawData)
	}

	// 使用map来安全地访问字段
	instanceMap := rawData

	// 安全地获取字符串字段
	getStringField := func(field string) string {
		if val, ok := instanceMap[field]; ok && val != nil {
			if str, ok := val.(string); ok {
				return str
			}
		}
		return ""
	}

	// 安全地获取整数字段
	getIntField := func(field string) int {
		if val, ok := instanceMap[field]; ok && val != nil {
			if num, ok := val.(float64); ok {
				return int(num)
			}
		}
		return 0
	}

	// 获取网络信息 - 改进版本，支持多种网络配置
	publicIP := ""
	privateIP := ""
	var publicIPs []string
	var privateIPs []string

	// 方法1: 从NetworkInterfaces获取
	if networkInterfaces, ok := instanceMap["NetworkInterfaces"].([]interface{}); ok && len(networkInterfaces) > 0 {
		for _, niInterface := range networkInterfaces {
			if ni, ok := niInterface.(map[string]interface{}); ok {
				// 获取公网IP
				if publicIpAddresses, ok := ni["PublicIpAddresses"].([]interface{}); ok {
					for _, pipInterface := range publicIpAddresses {
						if pip, ok := pipInterface.(map[string]interface{}); ok {
							if ip, ok := pip["PublicIpAddress"].(string); ok && ip != "" {
								publicIPs = append(publicIPs, ip)
							}
						}
					}
				}
				// 获取私网IP
				if privateIpAddresses, ok := ni["PrivateIpAddresses"].([]interface{}); ok {
					for _, pipInterface := range privateIpAddresses {
						if pip, ok := pipInterface.(map[string]interface{}); ok {
							if ip, ok := pip["PrivateIpAddress"].(string); ok && ip != "" {
								privateIPs = append(privateIPs, ip)
							}
						}
					}
				}
				// 也尝试直接从网络接口获取IP
				if primaryPrivateIp, ok := ni["PrimaryIpAddress"].(string); ok && primaryPrivateIp != "" {
					privateIPs = append(privateIPs, primaryPrivateIp)
				}
			}
		}
	}

	// 方法2: 直接从实例字段获取（备用方法）
	if len(publicIPs) == 0 {
		if eip := getStringField("EipAddress"); eip != "" {
			publicIPs = append(publicIPs, eip)
		}
		if pip := getStringField("PublicIpAddress"); pip != "" {
			publicIPs = append(publicIPs, pip)
		}
	}

	if len(privateIPs) == 0 {
		if pip := getStringField("PrivateIpAddress"); pip != "" {
			privateIPs = append(privateIPs, pip)
		}
	}

	// 取第一个IP作为主IP
	if len(publicIPs) > 0 {
		publicIP = publicIPs[0]
	}
	if len(privateIPs) > 0 {
		privateIP = privateIPs[0]
	}

	// 获取CPU和内存信息
	cpu := getIntField("Cpus")
	memory := getIntField("MemorySize")

	// 转换状态
	status := strings.ToLower(getStringField("Status"))

	// 构建标签
	tags := make(map[string]string)
	if tagsArray, ok := instanceMap["Tags"].([]interface{}); ok {
		for _, tagInterface := range tagsArray {
			if tag, ok := tagInterface.(map[string]interface{}); ok {
				if key, keyOk := tag["Key"].(string); keyOk {
					if value, valueOk := tag["Value"].(string); valueOk {
						tags[key] = value
					}
				}
			}
		}
	}

	// 构建更详细的规格信息
	specInfo := map[string]interface{}{
		"instance_type": getStringField("InstanceType"),
		"cpu":          cpu,
		"memory":       memory,
		"os_name":      getStringField("OsName"),
		"os_type":      getStringField("OsType"),
		"image_id":     getStringField("ImageId"),
		"hostname":     getStringField("Hostname"),
		"description":  getStringField("Description"),
	}

	// 添加磁盘信息
	if volumes, ok := instanceMap["Volumes"].([]interface{}); ok && len(volumes) > 0 {
		var diskInfo []map[string]interface{}
		for _, volInterface := range volumes {
			if vol, ok := volInterface.(map[string]interface{}); ok {
				disk := map[string]interface{}{
					"volume_id":   vol["VolumeId"],
					"volume_type": vol["VolumeType"],
					"size":        vol["Size"],
					"device":      vol["Device"],
				}
				diskInfo = append(diskInfo, disk)
			}
		}
		specInfo["disks"] = diskInfo
	}

	// 构建更详细的网络信息
	networkInfo := map[string]interface{}{
		"public_ip":     publicIP,
		"private_ip":    privateIP,
		"public_ips":    publicIPs,
		"private_ips":   privateIPs,
		"vpc_id":        getStringField("VpcId"),
		"subnet_id":     getStringField("SubnetId"),
		"security_groups": func() []string {
			var sgIds []string
			if sgs, ok := instanceMap["SecurityGroupIds"].([]interface{}); ok {
				for _, sg := range sgs {
					if sgId, ok := sg.(string); ok {
						sgIds = append(sgIds, sgId)
					}
				}
			}
			return sgIds
		}(),
	}

	resource := CloudResource{
		CloudID:     getStringField("InstanceId"),
		Name:        getStringField("InstanceName"),
		Type:        "ecs",
		Region:      v.config.Region,
		Zone:        getStringField("ZoneId"),
		Status:      status,
		SpecInfo:    specInfo,
		NetworkInfo: networkInfo,
		Tags:        tags,
		RawData:     rawData,
	}

	// 解析创建时间
	if createTimeStr := getStringField("CreatedAt"); createTimeStr != "" {
		if createTime, err := time.Parse(time.RFC3339, createTimeStr); err == nil {
			resource.CreatedTime = createTime
		}
	}

	return resource
}

// buildAdvancedFilters 构建高级过滤器，类似金山云的Filter参数
func (v *VolcanoProvider) buildAdvancedFilters(filters map[string]string) map[string]interface{} {
	advancedFilters := make(map[string]interface{})

	// 项目ID过滤器
	if projectIds, ok := filters["project_ids"]; ok && projectIds != "" {
		ids := strings.Split(projectIds, ",")
		var projectIdList []string
		for _, id := range ids {
			if trimmedId := strings.TrimSpace(id); trimmedId != "" {
				projectIdList = append(projectIdList, trimmedId)
			}
		}
		if len(projectIdList) > 0 {
			advancedFilters["project_ids"] = projectIdList
		}
	}

	// VPC ID过滤器
	if vpcIds, ok := filters["vpc_ids"]; ok && vpcIds != "" {
		ids := strings.Split(vpcIds, ",")
		var vpcIdList []string
		for _, id := range ids {
			if trimmedId := strings.TrimSpace(id); trimmedId != "" {
				vpcIdList = append(vpcIdList, trimmedId)
			}
		}
		if len(vpcIdList) > 0 {
			advancedFilters["vpc_ids"] = vpcIdList
		}
	}

	// 子网ID过滤器
	if subnetIds, ok := filters["subnet_ids"]; ok && subnetIds != "" {
		ids := strings.Split(subnetIds, ",")
		var subnetIdList []string
		for _, id := range ids {
			if trimmedId := strings.TrimSpace(id); trimmedId != "" {
				subnetIdList = append(subnetIdList, trimmedId)
			}
		}
		if len(subnetIdList) > 0 {
			advancedFilters["subnet_ids"] = subnetIdList
		}
	}

	// 实例类型过滤器
	if instanceTypes, ok := filters["instance_types"]; ok && instanceTypes != "" {
		types := strings.Split(instanceTypes, ",")
		var typeList []string
		for _, t := range types {
			if trimmedType := strings.TrimSpace(t); trimmedType != "" {
				typeList = append(typeList, trimmedType)
			}
		}
		if len(typeList) > 0 {
			advancedFilters["instance_types"] = typeList
		}
	}

	// 标签过滤器
	if tagFilters, ok := filters["tags"]; ok && tagFilters != "" {
		// 格式: key1:value1,key2:value2
		tagPairs := strings.Split(tagFilters, ",")
		tagMap := make(map[string]string)
		for _, pair := range tagPairs {
			if kv := strings.Split(pair, ":"); len(kv) == 2 {
				key := strings.TrimSpace(kv[0])
				value := strings.TrimSpace(kv[1])
				if key != "" && value != "" {
					tagMap[key] = value
				}
			}
		}
		if len(tagMap) > 0 {
			advancedFilters["tags"] = tagMap
		}
	}

	return advancedFilters
}

// applyClientSideFilters 应用客户端过滤器（API不支持的过滤器）
func (v *VolcanoProvider) applyClientSideFilters(resources []CloudResource, filters map[string]string) []CloudResource {
	var filteredResources []CloudResource

	for _, resource := range resources {
		shouldInclude := true

		// 高级过滤器
		advancedFilters := v.buildAdvancedFilters(filters)

		// 检查VPC ID
		if vpcIds, ok := advancedFilters["vpc_ids"].([]string); ok {
			if vpcId, exists := resource.NetworkInfo["vpc_id"].(string); exists {
				found := false
				for _, id := range vpcIds {
					if id == vpcId {
						found = true
						break
					}
				}
				if !found {
					shouldInclude = false
				}
			} else {
				shouldInclude = false
			}
		}

		// 检查实例类型
		if instanceTypes, ok := advancedFilters["instance_types"].([]string); ok {
			if instanceType, exists := resource.SpecInfo["instance_type"].(string); exists {
				found := false
				for _, t := range instanceTypes {
					if t == instanceType {
						found = true
						break
					}
				}
				if !found {
					shouldInclude = false
				}
			} else {
				shouldInclude = false
			}
		}

		// 检查标签
		if tagFilters, ok := advancedFilters["tags"].(map[string]string); ok {
			for key, value := range tagFilters {
				if resourceValue, exists := resource.Tags[key]; !exists || resourceValue != value {
					shouldInclude = false
					break
				}
			}
		}

		if shouldInclude {
			filteredResources = append(filteredResources, resource)
		}
	}

	return filteredResources
}

// sortResources 对资源进行排序
func (v *VolcanoProvider) sortResources(resources []CloudResource, sortBy string) []CloudResource {
	if sortBy == "" {
		return resources
	}

	// 解析排序参数，格式: field:asc 或 field:desc
	parts := strings.Split(sortBy, ":")
	if len(parts) != 2 {
		log.Printf("Invalid sort format: %s, expected format: field:asc or field:desc", sortBy)
		return resources
	}

	field := strings.TrimSpace(parts[0])
	order := strings.TrimSpace(strings.ToLower(parts[1]))

	if order != "asc" && order != "desc" {
		log.Printf("Invalid sort order: %s, must be 'asc' or 'desc'", order)
		return resources
	}

	// 创建副本以避免修改原始切片
	sortedResources := make([]CloudResource, len(resources))
	copy(sortedResources, resources)

	// 根据字段排序
	switch field {
	case "name", "instance_name":
		if order == "asc" {
			for i := 0; i < len(sortedResources)-1; i++ {
				for j := i + 1; j < len(sortedResources); j++ {
					if sortedResources[i].Name > sortedResources[j].Name {
						sortedResources[i], sortedResources[j] = sortedResources[j], sortedResources[i]
					}
				}
			}
		} else {
			for i := 0; i < len(sortedResources)-1; i++ {
				for j := i + 1; j < len(sortedResources); j++ {
					if sortedResources[i].Name < sortedResources[j].Name {
						sortedResources[i], sortedResources[j] = sortedResources[j], sortedResources[i]
					}
				}
			}
		}
	case "created_time", "create_time":
		if order == "asc" {
			for i := 0; i < len(sortedResources)-1; i++ {
				for j := i + 1; j < len(sortedResources); j++ {
					if sortedResources[i].CreatedTime.After(sortedResources[j].CreatedTime) {
						sortedResources[i], sortedResources[j] = sortedResources[j], sortedResources[i]
					}
				}
			}
		} else {
			for i := 0; i < len(sortedResources)-1; i++ {
				for j := i + 1; j < len(sortedResources); j++ {
					if sortedResources[i].CreatedTime.Before(sortedResources[j].CreatedTime) {
						sortedResources[i], sortedResources[j] = sortedResources[j], sortedResources[i]
					}
				}
			}
		}
	case "status":
		if order == "asc" {
			for i := 0; i < len(sortedResources)-1; i++ {
				for j := i + 1; j < len(sortedResources); j++ {
					if sortedResources[i].Status > sortedResources[j].Status {
						sortedResources[i], sortedResources[j] = sortedResources[j], sortedResources[i]
					}
				}
			}
		} else {
			for i := 0; i < len(sortedResources)-1; i++ {
				for j := i + 1; j < len(sortedResources); j++ {
					if sortedResources[i].Status < sortedResources[j].Status {
						sortedResources[i], sortedResources[j] = sortedResources[j], sortedResources[i]
					}
				}
			}
		}
	case "zone":
		if order == "asc" {
			for i := 0; i < len(sortedResources)-1; i++ {
				for j := i + 1; j < len(sortedResources); j++ {
					if sortedResources[i].Zone > sortedResources[j].Zone {
						sortedResources[i], sortedResources[j] = sortedResources[j], sortedResources[i]
					}
				}
			}
		} else {
			for i := 0; i < len(sortedResources)-1; i++ {
				for j := i + 1; j < len(sortedResources); j++ {
					if sortedResources[i].Zone < sortedResources[j].Zone {
						sortedResources[i], sortedResources[j] = sortedResources[j], sortedResources[i]
					}
				}
			}
		}
	default:
		log.Printf("Unsupported sort field: %s", field)
	}

	return sortedResources
}

// detectDuplicates 检测重复设备
func (v *VolcanoProvider) detectDuplicates(resources []CloudResource) error {
	// 这里可以集成重复检测逻辑
	// 由于需要导入duplicate包，这里先预留接口
	log.Printf("Duplicate detection for %d resources (feature placeholder)", len(resources))

	// TODO: 集成重复检测
	// detector := duplicate.NewDuplicateDetector(config)
	// return detector.DetectResourceDuplicates(resources)

	return nil
}

// getECSInstanceDetail 获取ECS实例详情
func (v *VolcanoProvider) getECSInstanceDetail(instanceId string) (*CloudResource, error) {
	if v.ecsClient == nil {
		return nil, fmt.Errorf("ECS client not initialized")
	}

	input := &ecs.DescribeInstancesInput{
		InstanceIds: []*string{volcengine.String(instanceId)},
	}

	result, err := v.ecsClient.DescribeInstances(input)
	if err != nil {
		return nil, fmt.Errorf("failed to describe instance: %v", err)
	}

	if len(result.Instances) == 0 {
		return nil, fmt.Errorf("instance not found: %s", instanceId)
	}

	resource := v.convertECSInstanceToCloudResource(result.Instances[0])
	return &resource, nil
}

// syncECSInstanceStatus 同步ECS实例状态
func (v *VolcanoProvider) syncECSInstanceStatus(resource *CloudResource) error {
	detail, err := v.getECSInstanceDetail(resource.CloudID)
	if err != nil {
		return err
	}

	// 更新状态
	resource.Status = detail.Status
	resource.UpdatedTime = time.Now()

	return nil
}
