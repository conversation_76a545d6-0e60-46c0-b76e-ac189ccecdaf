package cloud

import (
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"arboris/src/models"
)

// CacheResourceHandler的其余方法
func (c *CacheResourceHandler) SyncToResource(sourceId int64, mapping ImportMapping) (*models.Resource, error) {
	// 获取CloudCache记录
	cache, err := models.CloudCacheGet("id=?", sourceId)
	if err != nil {
		return nil, fmt.Errorf("failed to get cloud cache: %v", err)
	}
	if cache == nil {
		return nil, fmt.Errorf("cloud cache not found: %d", sourceId)
	}

	// 创建Resource记录
	resource := &models.Resource{
		UUID:       fmt.Sprintf("cloud-cache-%d", cache.Id),
		Ident:      cache.CloudId,
		Name:       cache.Name,
		Cate:       mapping.Cate,
		Tenant:     mapping.Tenant,
		Note:       mapping.Note,
		SourceId:   cache.Id,
		SourceType: "cloud_cache",
		Extend:     c.buildExtendFromCache(cache),
	}

	err = resource.Save()
	if err != nil {
		return nil, fmt.Errorf("failed to save resource: %v", err)
	}

	return resource, nil
}

func (c *CacheResourceHandler) UpdateExisting(sourceId int64, tempResource *models.CloudResourceTemp, mapping ImportMapping) error {
	// 获取现有CloudCache记录
	cache, err := models.CloudCacheGet("id=?", sourceId)
	if err != nil {
		return fmt.Errorf("failed to get existing cloud cache: %v", err)
	}
	if cache == nil {
		return fmt.Errorf("existing cloud cache not found: %d", sourceId)
	}

	// 解析新的云资源数据
	var specInfo map[string]interface{}
	if tempResource.SpecInfo != "" {
		json.Unmarshal([]byte(tempResource.SpecInfo), &specInfo)
	}
	
	var networkInfo map[string]interface{}
	if tempResource.NetworkInfo != "" {
		json.Unmarshal([]byte(tempResource.NetworkInfo), &networkInfo)
	}

	// 更新CloudCache记录
	cache.Name = tempResource.Name
	cache.Status = tempResource.Status
	cache.Region = tempResource.Region
	cache.Zone = tempResource.Zone
	cache.Engine = c.getStringFromSpec(specInfo, "engine")
	cache.Version = c.getStringFromSpec(specInfo, "version")
	cache.InstanceClass = c.getStringFromSpec(specInfo, "instance_class")
	cache.MemorySize = c.getIntFromSpec(specInfo, "memory_size")
	cache.ShardCount = c.getIntFromSpec(specInfo, "shard_count")
	cache.ReplicaCount = c.getIntFromSpec(specInfo, "replica_count")
	cache.ConnectionString = c.getStringFromSpec(specInfo, "connection_string")
	cache.Port = c.getIntFromSpec(specInfo, "port")
	cache.VpcId = c.getStringFromNetwork(networkInfo, "vpc_id")
	cache.SubnetId = c.getStringFromNetwork(networkInfo, "subnet_id")
	cache.AuthEnabled = c.getBoolFromSpec(specInfo, "auth_enabled")
	cache.SslEnabled = c.getBoolFromSpec(specInfo, "ssl_enabled")
	cache.BackupEnabled = c.getBoolFromSpec(specInfo, "backup_enabled")
	cache.SpecInfo = tempResource.SpecInfo
	cache.NetworkInfo = tempResource.NetworkInfo
	cache.Tags = tempResource.Tags
	cache.RawData = tempResource.RawData
	cache.LastDiscoveryAt = &time.Time{}
	*cache.LastDiscoveryAt = time.Now()

	// 保存更新
	err = cache.Update("name", "status", "region", "zone", "engine", "version", 
		"instance_class", "memory_size", "shard_count", "replica_count", 
		"connection_string", "port", "vpc_id", "subnet_id", "auth_enabled", 
		"ssl_enabled", "backup_enabled", "spec_info", "network_info", "tags", 
		"raw_data", "last_discovery_at")
	if err != nil {
		return fmt.Errorf("failed to update cloud cache: %v", err)
	}

	return nil
}

// 缓存处理器的辅助方法
func (c *CacheResourceHandler) getStringFromSpec(specInfo map[string]interface{}, key string) string {
	if value, ok := specInfo[key]; ok {
		return fmt.Sprintf("%v", value)
	}
	return ""
}

func (c *CacheResourceHandler) getIntFromSpec(specInfo map[string]interface{}, key string) int {
	if value, ok := specInfo[key]; ok {
		if intVal, err := strconv.Atoi(fmt.Sprintf("%v", value)); err == nil {
			return intVal
		}
	}
	return 0
}

func (c *CacheResourceHandler) getBoolFromSpec(specInfo map[string]interface{}, key string) bool {
	if value, ok := specInfo[key]; ok {
		if boolVal, ok := value.(bool); ok {
			return boolVal
		}
		// 尝试从字符串转换
		if strVal, ok := value.(string); ok {
			return strVal == "true" || strVal == "1" || strVal == "yes"
		}
	}
	return false
}

func (c *CacheResourceHandler) getStringFromNetwork(networkInfo map[string]interface{}, key string) string {
	if value, ok := networkInfo[key]; ok {
		return fmt.Sprintf("%v", value)
	}
	return ""
}

func (c *CacheResourceHandler) getCloudProvider(tempResource *models.CloudResourceTemp) string {
	// 从discovery记录中获取云厂商信息
	discovery, err := models.CloudResourceDiscoveryGet("id=?", tempResource.DiscoveryId)
	if err != nil || discovery == nil {
		return ""
	}
	
	config, err := models.CloudProviderConfigGet("id=?", discovery.ConfigId)
	if err != nil || config == nil {
		return ""
	}
	
	return config.Provider
}

func (c *CacheResourceHandler) buildExtendFromCache(cache *models.CloudCache) string {
	extend := map[string]interface{}{
		"cloud_id":          cache.CloudId,
		"cloud_provider":    cache.CloudProvider,
		"cloud_region":      cache.Region,
		"cloud_zone":        cache.Zone,
		"engine":            cache.Engine,
		"version":           cache.Version,
		"instance_class":    cache.InstanceClass,
		"memory_size":       cache.MemorySize,
		"shard_count":       cache.ShardCount,
		"replica_count":     cache.ReplicaCount,
		"connection_string": cache.ConnectionString,
		"port":              cache.Port,
		"vpc_id":            cache.VpcId,
		"subnet_id":         cache.SubnetId,
		"auth_enabled":      cache.AuthEnabled,
		"ssl_enabled":       cache.SslEnabled,
		"backup_enabled":    cache.BackupEnabled,
		"status":            cache.Status,
	}
	
	extendJSON, _ := json.Marshal(extend)
	return string(extendJSON)
}
