package cloud

import (
	"encoding/json"
	"fmt"
	"log"
	"strconv"
	"strings"
	"time"

	epc "github.com/kingsoftcloud/sdk-go/v2/ksyun/client/epc/v20151101"
	kcs "github.com/kingsoftcloud/sdk-go/v2/ksyun/client/kcs/v20160701"
	kec "github.com/kingsoftcloud/sdk-go/v2/ksyun/client/kec/v20160304"
	krds "github.com/kingsoftcloud/sdk-go/v2/ksyun/client/krds/v20160701"
	slb "github.com/kingsoftcloud/sdk-go/v2/ksyun/client/slb/v20160304"
	"github.com/kingsoftcloud/sdk-go/v2/ksyun/common"
	"github.com/kingsoftcloud/sdk-go/v2/ksyun/common/profile"

	amsConfig "arboris/src/modules/ams/config"
)

// KingsoftProvider 金山云提供商
type KingsoftProvider struct {
	config     ProviderConfig
	kecClient  *kec.Client
	epcClient  *epc.Client
	krdsClient *krds.Client
	kcsClient  *kcs.Client
	slbClient  *slb.Client
}

// NewKingsoftProvider 创建金山云提供商实例（从配置文件读取认证信息）
func NewKingsoftProvider() (*KingsoftProvider, error) {
	// 从配置文件读取金山云配置
	cloudConfig, err := amsConfig.GetKingsoftConfig()
	if err != nil {
		return nil, fmt.Errorf("failed to get kingsoft config: %v", err)
	}

	// 转换为ProviderConfig
	config := ProviderConfig{
		Provider:  "kingsoft",
		Region:    cloudConfig.Credentials.DefaultRegion,
		AccessKey: cloudConfig.Credentials.AccessKey,
		SecretKey: cloudConfig.Credentials.SecretKey,
		Endpoint:  cloudConfig.API.Endpoint,
	}

	return NewKingsoftProviderWithConfig(config), nil
}

// NewKingsoftProviderWithConfig 使用指定配置创建金山云提供商实例
func NewKingsoftProviderWithConfig(config ProviderConfig) *KingsoftProvider {
	// 创建认证信息
	credential := common.NewCredential(config.AccessKey, config.SecretKey)

	// 创建客户端配置
	cpf := profile.NewClientProfile()
	cpf.HttpProfile.ReqMethod = "POST"
	cpf.HttpProfile.ReqTimeout = 60

	// 设置端点
	if config.Endpoint != "" {
		cpf.HttpProfile.Endpoint = config.Endpoint
	} else {
		cpf.HttpProfile.Endpoint = "kec.api.ksyun.com"
	}

	// 创建各服务客户端
	kecClient, _ := kec.NewClient(credential, config.Region, cpf)

	// 为其他服务设置不同的端点
	cpfKrds := profile.NewClientProfile()
	cpfKrds.HttpProfile.ReqMethod = "POST"
	cpfKrds.HttpProfile.ReqTimeout = 60
	cpfKrds.HttpProfile.Endpoint = "krds.api.ksyun.com"
	krdsClient, _ := krds.NewClient(credential, config.Region, cpfKrds)

	cpfKcs := profile.NewClientProfile()
	cpfKcs.HttpProfile.ReqMethod = "POST"
	cpfKcs.HttpProfile.ReqTimeout = 60
	cpfKcs.HttpProfile.Endpoint = "kcs.api.ksyun.com"
	kcsClient, _ := kcs.NewClient(credential, config.Region, cpfKcs)

	cpfEpc := profile.NewClientProfile()
	cpfEpc.HttpProfile.ReqMethod = "POST"
	cpfEpc.HttpProfile.ReqTimeout = 60
	cpfEpc.HttpProfile.Endpoint = "epc.api.ksyun.com"
	epcClient, _ := epc.NewClient(credential, config.Region, cpfEpc)

	cpfSlb := profile.NewClientProfile()
	cpfSlb.HttpProfile.ReqMethod = "POST"
	cpfSlb.HttpProfile.ReqTimeout = 60
	cpfSlb.HttpProfile.Endpoint = "slb.api.ksyun.com"
	slbClient, _ := slb.NewClient(credential, config.Region, cpfSlb)

	return &KingsoftProvider{
		config:     config,
		kecClient:  kecClient,
		epcClient:  epcClient,
		krdsClient: krdsClient,
		kcsClient:  kcsClient,
		slbClient:  slbClient,
	}
}

// TestConnection 测试连接
func (k *KingsoftProvider) TestConnection() error {
	log.Printf("Testing Kingsoft Cloud connection for region: %s", k.config.Region)

	// 调用DescribeInstances接口测试连接
	request := kec.NewDescribeInstancesRequest()

	responseString := k.kecClient.DescribeInstances(request)

	var respStruct kec.DescribeInstancesResponse
	err := respStruct.FromJsonString(responseString)
	if err != nil {
		return fmt.Errorf("failed to connect to Kingsoft Cloud: %v", err)
	}

	log.Printf("Successfully connected to Kingsoft Cloud in region: %s", k.config.Region)
	return nil
}

// GetSupportedResourceTypes 获取支持的资源类型
func (k *KingsoftProvider) GetSupportedResourceTypes() []string {
	return []string{"ecs", "epc", "rds", "redis", "slb"}
}

// DiscoverResources 发现资源
func (k *KingsoftProvider) DiscoverResources(resourceType string, filters map[string]string) ([]CloudResource, error) {
	switch resourceType {
	case "ecs":
		return k.discoverECSInstances(filters)
	case "epc":
		return k.discoverEPCInstances(filters)
	case "rds":
		return k.discoverRDSInstances(filters)
	case "redis":
		return k.discoverRedisInstances(filters)
	case "slb":
		return k.discoverSLBInstances(filters)
	default:
		return nil, fmt.Errorf("unsupported resource type: %s", resourceType)
	}
}

// GetResourceDetail 获取资源详情
func (k *KingsoftProvider) GetResourceDetail(resourceType, resourceId string) (*CloudResource, error) {
	switch resourceType {
	case "ecs":
		return k.getECSInstanceDetail(resourceId)
	case "epc":
		return k.getEPCInstanceDetail(resourceId)
	case "rds":
		return k.getRDSInstanceDetail(resourceId)
	case "redis":
		return k.getRedisInstanceDetail(resourceId)
	case "slb":
		return k.getSLBInstanceDetail(resourceId)
	default:
		return nil, fmt.Errorf("unsupported resource type: %s", resourceType)
	}
}

// SyncResourceStatus 同步资源状态
func (k *KingsoftProvider) SyncResourceStatus(resources []CloudResource) error {
	for _, resource := range resources {
		switch resource.Type {
		case "ecs":
			if err := k.syncECSInstanceStatus(&resource); err != nil {
				log.Printf("Failed to sync ECS instance %s status: %v", resource.CloudID, err)
			}
		case "epc":
			if err := k.syncEPCInstanceStatus(&resource); err != nil {
				log.Printf("Failed to sync EPC instance %s status: %v", resource.CloudID, err)
			}
		case "rds":
			if err := k.syncRDSInstanceStatus(&resource); err != nil {
				log.Printf("Failed to sync RDS instance %s status: %v", resource.CloudID, err)
			}
		case "redis":
			if err := k.syncRedisInstanceStatus(&resource); err != nil {
				log.Printf("Failed to sync Redis instance %s status: %v", resource.CloudID, err)
			}
		case "slb":
			if err := k.syncSLBInstanceStatus(&resource); err != nil {
				log.Printf("Failed to sync SLB instance %s status: %v", resource.CloudID, err)
			}
		}
	}
	return nil
}

// discoverECSInstances 发现ECS实例
func (k *KingsoftProvider) discoverECSInstances(filters map[string]string) ([]CloudResource, error) {
	log.Printf("Discovering Kingsoft ECS instances with filters: %+v", filters)

	var resources []CloudResource

	// 构建请求参数
	request := kec.NewDescribeInstancesRequest()

	// 调用API
	responseString := k.kecClient.DescribeInstances(request)

	var respStruct kec.DescribeInstancesResponse
	err := respStruct.FromJsonString(responseString)
	if err != nil {
		return nil, fmt.Errorf("failed to describe instances: %v", err)
	}

	// 转换为CloudResource并应用过滤器
	for _, instance := range respStruct.InstancesSet {
		// 应用可用区过滤器
		if zone, ok := filters["zone"]; ok && zone != "" {
			if instance.AvailabilityZone == nil || *instance.AvailabilityZone != zone {
				continue
			}
		}

		// 应用状态过滤器
		if statusFilter, ok := filters["status"]; ok && statusFilter != "" {
			if instance.InstanceState.Name == nil {
				continue
			}
			if !strings.Contains(statusFilter, strings.ToLower(*instance.InstanceState.Name)) {
				continue
			}
		}

		// 应用名称过滤器
		if nameLike, ok := filters["name_like"]; ok && nameLike != "" {
			if instance.InstanceName == nil || !strings.Contains(strings.ToLower(*instance.InstanceName), strings.ToLower(nameLike)) {
				continue
			}
		}

		resource := k.convertECSInstanceToCloudResource(&instance)
		resources = append(resources, resource)
	}

	log.Printf("Discovered %d ECS instances", len(resources))
	return resources, nil
}

// discoverRDSInstances 发现RDS实例
func (k *KingsoftProvider) discoverRDSInstances(filters map[string]string) ([]CloudResource, error) {
	log.Printf("Discovering Kingsoft RDS instances with filters: %+v", filters)

	var resources []CloudResource

	// TODO: 实现金山云RDS API调用
	// 暂时返回空结果，避免编译错误
	log.Printf("RDS discovery not yet implemented")
	return resources, nil
}

// discoverRedisInstances 发现Redis实例
func (k *KingsoftProvider) discoverRedisInstances(filters map[string]string) ([]CloudResource, error) {
	log.Printf("Discovering Kingsoft Redis instances with filters: %+v", filters)

	var resources []CloudResource

	// TODO: 实现金山云Redis API调用
	// 暂时返回空结果，避免编译错误
	log.Printf("Redis discovery not yet implemented")
	return resources, nil
}

// discoverSLBInstances 发现SLB实例
func (k *KingsoftProvider) discoverSLBInstances(filters map[string]string) ([]CloudResource, error) {
	log.Printf("Discovering Kingsoft SLB instances with filters: %+v", filters)

	var resources []CloudResource

	// TODO: 实现金山云SLB API调用
	// 暂时返回空结果，避免编译错误
	log.Printf("SLB discovery not yet implemented")
	return resources, nil
}

// convertECSInstanceToCloudResource 转换ECS实例为CloudResource
func (k *KingsoftProvider) convertECSInstanceToCloudResource(instance interface{}) CloudResource {
	// 构建原始数据
	rawData := make(map[string]interface{})
	if data, err := json.Marshal(instance); err == nil {
		json.Unmarshal(data, &rawData)
	}

	// 使用map来安全地访问字段
	instanceMap := rawData

	// 安全地获取字符串字段
	getStringField := func(field string) string {
		if val, ok := instanceMap[field]; ok && val != nil {
			if str, ok := val.(string); ok {
				return str
			}
		}
		return ""
	}

	// 安全地获取整数字段
	_ = func(field string) int {
		if val, ok := instanceMap[field]; ok && val != nil {
			if num, ok := val.(float64); ok {
				return int(num)
			}
		}
		return 0
	}

	// 获取实例状态
	status := "unknown"
	if instanceState, ok := instanceMap["InstanceState"].(map[string]interface{}); ok {
		if name, ok := instanceState["Name"].(string); ok {
			status = strings.ToLower(name)
		}
	}

	// 获取网络信息
	var publicIP, privateIP string
	if networkInterfaces, ok := instanceMap["NetworkInterfaceSet"].([]interface{}); ok && len(networkInterfaces) > 0 {
		if firstInterface, ok := networkInterfaces[0].(map[string]interface{}); ok {
			if ip, ok := firstInterface["PrivateIpAddress"].(string); ok {
				privateIP = ip
			}
		}
	}

	// 获取规格信息
	var cpu, memory int
	if instanceConfigure, ok := instanceMap["InstanceConfigure"].(map[string]interface{}); ok {
		if vcpu, ok := instanceConfigure["VCPU"].(float64); ok {
			cpu = int(vcpu)
		}
		if memoryGb, ok := instanceConfigure["MemoryGb"].(float64); ok {
			memory = int(memoryGb)
		}
	}

	resource := CloudResource{
		CloudID: getStringField("InstanceId"),
		Name:    getStringField("InstanceName"),
		Type:    "ecs",
		Region:  k.config.Region,
		Zone:    getStringField("AvailabilityZone"),
		Status:  status,
		SpecInfo: map[string]interface{}{
			"instance_type": getStringField("InstanceType"),
			"cpu":           cpu,
			"memory":        memory,
		},
		NetworkInfo: map[string]interface{}{
			"public_ip":  publicIP,
			"private_ip": privateIP,
		},
		Tags:    make(map[string]string), // 暂时为空，可以后续添加标签解析
		RawData: rawData,
	}

	// 解析创建时间
	if createTimeStr := getStringField("CreationDate"); createTimeStr != "" {
		if createTime, err := time.Parse("2006-01-02T15:04:05Z", createTimeStr); err == nil {
			resource.CreatedTime = createTime
		}
	}

	return resource
}

// getECSInstanceDetail 获取ECS实例详情
func (k *KingsoftProvider) getECSInstanceDetail(instanceId string) (*CloudResource, error) {
	request := kec.NewDescribeInstancesRequest()
	request.InstanceId = []*string{&instanceId}

	responseString := k.kecClient.DescribeInstances(request)

	var respStruct kec.DescribeInstancesResponse
	err := respStruct.FromJsonString(responseString)
	if err != nil {
		return nil, fmt.Errorf("failed to describe instance: %v", err)
	}

	if len(respStruct.InstancesSet) == 0 {
		return nil, fmt.Errorf("instance not found: %s", instanceId)
	}

	resource := k.convertECSInstanceToCloudResource(respStruct.InstancesSet[0])
	return &resource, nil
}

// getRDSInstanceDetail 获取RDS实例详情
func (k *KingsoftProvider) getRDSInstanceDetail(instanceId string) (*CloudResource, error) {
	// TODO: 实现RDS实例详情获取
	return nil, fmt.Errorf("RDS instance detail not implemented")
}

// getRedisInstanceDetail 获取Redis实例详情
func (k *KingsoftProvider) getRedisInstanceDetail(instanceId string) (*CloudResource, error) {
	// TODO: 实现Redis实例详情获取
	return nil, fmt.Errorf("Redis instance detail not implemented")
}

// getSLBInstanceDetail 获取SLB实例详情
func (k *KingsoftProvider) getSLBInstanceDetail(instanceId string) (*CloudResource, error) {
	// TODO: 实现SLB实例详情获取
	return nil, fmt.Errorf("SLB instance detail not implemented")
}

// syncECSInstanceStatus 同步ECS实例状态
func (k *KingsoftProvider) syncECSInstanceStatus(resource *CloudResource) error {
	detail, err := k.getECSInstanceDetail(resource.CloudID)
	if err != nil {
		return err
	}

	// 更新状态
	resource.Status = detail.Status
	resource.UpdatedTime = time.Now()

	return nil
}

// syncRDSInstanceStatus 同步RDS实例状态
func (k *KingsoftProvider) syncRDSInstanceStatus(resource *CloudResource) error {
	// TODO: 实现RDS实例状态同步
	return nil
}

// syncRedisInstanceStatus 同步Redis实例状态
func (k *KingsoftProvider) syncRedisInstanceStatus(resource *CloudResource) error {
	// TODO: 实现Redis实例状态同步
	return nil
}

// syncSLBInstanceStatus 同步SLB实例状态
func (k *KingsoftProvider) syncSLBInstanceStatus(resource *CloudResource) error {
	// TODO: 实现SLB实例状态同步
	return nil
}

// discoverEPCInstances 发现EPC裸金属服务器实例
func (k *KingsoftProvider) discoverEPCInstances(filters map[string]string) ([]CloudResource, error) {
	log.Printf("Discovering Kingsoft EPC instances with filters: %+v", filters)

	var resources []CloudResource

	// 构建请求参数
	request := epc.DescribeEpcsRequest{}

	// 应用过滤器
	if projectIds, ok := filters["project_ids"]; ok && projectIds != "" {
		ids := strings.Split(projectIds, ",")
		var projectIdList []*string
		for _, id := range ids {
			if trimmedId := strings.TrimSpace(id); trimmedId != "" {
				projectIdList = append(projectIdList, &trimmedId)
			}
		}
		if len(projectIdList) > 0 {
			request.ProjectId = projectIdList
		}
	}

	if hostIds, ok := filters["host_ids"]; ok && hostIds != "" {
		ids := strings.Split(hostIds, ",")
		var hostIdList []*string
		for _, id := range ids {
			if trimmedId := strings.TrimSpace(id); trimmedId != "" {
				hostIdList = append(hostIdList, &trimmedId)
			}
		}
		if len(hostIdList) > 0 {
			request.HostId = hostIdList
		}
	}

	// 设置分页参数
	maxResults := 100
	if maxResultsStr, ok := filters["max_results"]; ok && maxResultsStr != "" {
		if mr, err := strconv.Atoi(maxResultsStr); err == nil && mr > 0 && mr <= 1000 {
			maxResults = mr
		}
	}
	request.MaxResults = &maxResults

	// 分页获取所有实例
	nextToken := ""
	for {
		if nextToken != "" {
			request.NextToken = &nextToken
		}

		// 调用API
		responseStr := k.epcClient.DescribeEpcs(&request)

		// 解析响应
		var response epc.DescribeEpcsResponse
		if err := json.Unmarshal([]byte(responseStr), &response); err != nil {
			return nil, fmt.Errorf("failed to parse EPC response: %v", err)
		}

		// 转换为CloudResource
		for _, host := range response.HostSet {
			// 应用客户端过滤器
			if statusFilter, ok := filters["status"]; ok && statusFilter != "" {
				// EPC没有明确的状态字段，可以根据其他字段判断状态
				// 这里暂时跳过状态过滤，或者可以根据业务需求自定义状态逻辑
				log.Printf("Status filter not supported for EPC instances: %s", statusFilter)
			}

			if nameLike, ok := filters["name_like"]; ok && nameLike != "" {
				if host.HostName == nil {
					continue
				}
				if !strings.Contains(strings.ToLower(*host.HostName), strings.ToLower(nameLike)) {
					continue
				}
			}

			// 应用可用区过滤器
			if zone, ok := filters["zone"]; ok && zone != "" {
				if host.AvailabilityZone == nil || *host.AvailabilityZone != zone {
					continue
				}
			}

			resource := k.convertEPCHostToCloudResource(host)
			resources = append(resources, resource)
		}

		// 检查是否还有更多页
		if response.NextToken == nil || *response.NextToken == "" {
			break
		}
		nextToken = *response.NextToken

		// 防止无限循环
		if len(resources) >= 10000 {
			log.Printf("Warning: Reached maximum limit of 10000 EPC instances")
			break
		}
	}

	log.Printf("Discovered %d EPC instances", len(resources))
	return resources, nil
}

// convertEPCHostToCloudResource 转换EPC主机为CloudResource
func (k *KingsoftProvider) convertEPCHostToCloudResource(host interface{}) CloudResource {
	// 构建原始数据
	rawData := make(map[string]interface{})
	if data, err := json.Marshal(host); err == nil {
		json.Unmarshal(data, &rawData)
	}

	// 安全地获取字符串字段
	getStringField := func(key string) string {
		if val, ok := rawData[key].(string); ok {
			return val
		}
		return ""
	}

	// 安全地获取整数字段（从字符串转换）
	getIntFromString := func(key string) int {
		if val, ok := rawData[key].(string); ok {
			if intVal, err := strconv.Atoi(val); err == nil {
				return intVal
			}
		}
		return 0
	}

	// 获取网络信息
	var publicIPs []string
	var privateIPs []string

	if networkInterfaces, ok := rawData["NetworkInterfaceAttributeSet"].([]interface{}); ok {
		for _, niInterface := range networkInterfaces {
			if ni, ok := niInterface.(map[string]interface{}); ok {
				if privateIP, ok := ni["PrivateIpAddress"].(string); ok && privateIP != "" {
					privateIPs = append(privateIPs, privateIP)
				}
				// EPC可能有公网IP，从其他字段获取
			}
		}
	}

	// 主要IP地址
	primaryPrivateIP := ""
	if len(privateIPs) > 0 {
		primaryPrivateIP = privateIPs[0]
	}

	primaryPublicIP := ""
	if len(publicIPs) > 0 {
		primaryPublicIP = publicIPs[0]
	}

	// 构建规格信息
	specInfo := map[string]interface{}{
		"host_type":    getStringField("HostType"),
		"product_type": getStringField("ProductType"),
		"cpu":          getIntFromString("Cpu"),
		"memory":       getIntFromString("Memory"),
		"disk":         getStringField("Disk"),
		"raid":         getStringField("Raid"),
		"os_name":      getStringField("OsName"),
		"sn":           getStringField("Sn"),
	}

	// 构建网络信息
	networkInfo := map[string]interface{}{
		"private_ip":  primaryPrivateIP,
		"public_ip":   primaryPublicIP,
		"private_ips": privateIPs,
		"public_ips":  publicIPs,
		"vpc_id":      "", // EPC可能没有VPC概念
		"subnet_id":   getStringField("SubnetId"),
	}

	// 构建标签
	tags := make(map[string]string)
	// EPC可能没有标签字段，或者在其他地方

	// EPC没有明确的状态字段，设置默认状态
	status := "active" // 默认状态，可以根据业务需求调整
	if hostStatus := getStringField("HostStatus"); hostStatus != "" {
		status = strings.ToLower(hostStatus)
	}

	resource := CloudResource{
		CloudID:     getStringField("HostId"),
		Name:        getStringField("HostName"),
		Type:        "epc",
		Region:      k.config.Region,
		Zone:        getStringField("AvailabilityZone"),
		Status:      status,
		SpecInfo:    specInfo,
		NetworkInfo: networkInfo,
		Tags:        tags,
		RawData:     rawData,
	}

	// 解析创建时间
	if createTimeStr := getStringField("CreateTime"); createTimeStr != "" {
		if createTime, err := time.Parse("2006-01-02 15:04:05", createTimeStr); err == nil {
			resource.CreatedTime = createTime
		}
	}

	return resource
}

// getEPCInstanceDetail 获取EPC实例详情
func (k *KingsoftProvider) getEPCInstanceDetail(hostId string) (*CloudResource, error) {
	request := epc.DescribeEpcsRequest{
		HostId: []*string{&hostId},
	}

	responseStr := k.epcClient.DescribeEpcs(&request)

	// 解析响应
	var response epc.DescribeEpcsResponse
	if err := json.Unmarshal([]byte(responseStr), &response); err != nil {
		return nil, fmt.Errorf("failed to parse EPC response: %v", err)
	}

	if len(response.HostSet) == 0 {
		return nil, fmt.Errorf("EPC instance not found: %s", hostId)
	}

	resource := k.convertEPCHostToCloudResource(response.HostSet[0])
	return &resource, nil
}

// syncEPCInstanceStatus 同步EPC实例状态
func (k *KingsoftProvider) syncEPCInstanceStatus(resource *CloudResource) error {
	detail, err := k.getEPCInstanceDetail(resource.CloudID)
	if err != nil {
		return err
	}

	// 更新状态
	resource.Status = detail.Status
	resource.UpdatedTime = time.Now()

	return nil
}
