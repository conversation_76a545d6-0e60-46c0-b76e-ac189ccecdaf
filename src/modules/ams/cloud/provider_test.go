package cloud

import (
	"testing"
)

// TestGetSupportedProviders 测试获取支持的云厂商列表
func TestGetSupportedProviders(t *testing.T) {
	providers := GetSupportedProviders()
	
	if len(providers) == 0 {
		t.<PERSON><PERSON><PERSON>("Expected at least one supported provider")
	}
	
	expectedProviders := map[string]bool{
		"kingsoft": false,
		"volcano":  false,
		"aliyun":   false,
		"tencent":  false,
		"ctyun":    false,
	}
	
	for _, provider := range providers {
		if _, exists := expectedProviders[provider]; exists {
			expectedProviders[provider] = true
		}
	}
	
	for provider, found := range expectedProviders {
		if !found {
			t.<PERSON>rrorf("Expected provider %s not found", provider)
		}
	}
}

// TestGetSupportedResourceTypes 测试获取支持的资源类型
func TestGetSupportedResourceTypes(t *testing.T) {
	// 测试金山云
	kingsoftTypes := GetSupportedResourceTypes("kingsoft")
	if len(kingsoftTypes) == 0 {
		t.<PERSON><PERSON>r("Expected at least one resource type for kingsoft")
	}
	
	expectedKingsoftTypes := []string{"ecs", "rds", "redis", "slb"}
	for _, expectedType := range expectedKingsoftTypes {
		found := false
		for _, actualType := range kingsoftTypes {
			if actualType == expectedType {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("Expected resource type %s not found for kingsoft", expectedType)
		}
	}
	
	// 测试火山云
	volcanoTypes := GetSupportedResourceTypes("volcano")
	if len(volcanoTypes) == 0 {
		t.Error("Expected at least one resource type for volcano")
	}
	
	expectedVolcanoTypes := []string{"ecs", "rds", "redis", "clb"}
	for _, expectedType := range expectedVolcanoTypes {
		found := false
		for _, actualType := range volcanoTypes {
			if actualType == expectedType {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("Expected resource type %s not found for volcano", expectedType)
		}
	}
	
	// 测试不支持的云厂商
	unsupportedTypes := GetSupportedResourceTypes("unsupported")
	if len(unsupportedTypes) != 0 {
		t.Error("Expected no resource types for unsupported provider")
	}
}

// TestGetResourceTypeName 测试获取资源类型中文名
func TestGetResourceTypeName(t *testing.T) {
	testCases := []struct {
		provider     string
		resourceType string
		expected     string
	}{
		{"kingsoft", "ecs", "云服务器"},
		{"kingsoft", "rds", "云数据库"},
		{"kingsoft", "redis", "云缓存"},
		{"kingsoft", "slb", "负载均衡"},
		{"volcano", "ecs", "云服务器"},
		{"volcano", "rds", "云数据库"},
		{"volcano", "redis", "云缓存"},
		{"volcano", "clb", "负载均衡"},
		{"unsupported", "ecs", "ecs"}, // 不支持的云厂商应该返回原始类型
		{"kingsoft", "unsupported", "unsupported"}, // 不支持的资源类型应该返回原始类型
	}
	
	for _, tc := range testCases {
		actual := GetResourceTypeName(tc.provider, tc.resourceType)
		if actual != tc.expected {
			t.Errorf("GetResourceTypeName(%s, %s) = %s, expected %s", 
				tc.provider, tc.resourceType, actual, tc.expected)
		}
	}
}

// TestNewCloudProvider 测试创建云厂商实例
func TestNewCloudProvider(t *testing.T) {
	// 测试金山云
	kingsoftConfig := ProviderConfig{
		Provider:  "kingsoft",
		Region:    "cn-beijing-6",
		AccessKey: "test_access_key",
		SecretKey: "test_secret_key",
		Endpoint:  "",
	}
	
	kingsoftProvider, err := NewCloudProvider(kingsoftConfig)
	if err != nil {
		t.Errorf("Failed to create kingsoft provider: %v", err)
	}
	if kingsoftProvider == nil {
		t.Error("Expected non-nil kingsoft provider")
	}
	
	// 测试火山云
	volcanoConfig := ProviderConfig{
		Provider:  "volcano",
		Region:    "cn-beijing",
		AccessKey: "test_access_key",
		SecretKey: "test_secret_key",
		Endpoint:  "",
	}
	
	volcanoProvider, err := NewCloudProvider(volcanoConfig)
	if err != nil {
		t.Errorf("Failed to create volcano provider: %v", err)
	}
	if volcanoProvider == nil {
		t.Error("Expected non-nil volcano provider")
	}
	
	// 测试不支持的云厂商
	unsupportedConfig := ProviderConfig{
		Provider:  "unsupported",
		Region:    "test-region",
		AccessKey: "test_access_key",
		SecretKey: "test_secret_key",
		Endpoint:  "",
	}
	
	_, err = NewCloudProvider(unsupportedConfig)
	if err == nil {
		t.Error("Expected error for unsupported provider")
	}
}

// TestDiscoveryFilters 测试发现过滤器
func TestDiscoveryFilters(t *testing.T) {
	filters := DiscoveryFilters{
		Zone:     "cn-beijing-6a",
		Status:   []string{"running", "stopped"},
		Tags:     map[string]string{"env": "prod", "team": "backend"},
		NameLike: "web",
	}
	
	filterMap := filters.ToMap()
	
	// 检查zone过滤
	if filterMap["zone"] != "cn-beijing-6a" {
		t.Errorf("Expected zone filter 'cn-beijing-6a', got '%s'", filterMap["zone"])
	}
	
	// 检查status过滤
	expectedStatus := "running,stopped"
	if filterMap["status"] != expectedStatus {
		t.Errorf("Expected status filter '%s', got '%s'", expectedStatus, filterMap["status"])
	}
	
	// 检查name_like过滤
	if filterMap["name_like"] != "web" {
		t.Errorf("Expected name_like filter 'web', got '%s'", filterMap["name_like"])
	}
	
	// 检查标签过滤
	if filterMap["tag:env"] != "prod" {
		t.Errorf("Expected tag:env filter 'prod', got '%s'", filterMap["tag:env"])
	}
	if filterMap["tag:team"] != "backend" {
		t.Errorf("Expected tag:team filter 'backend', got '%s'", filterMap["tag:team"])
	}
}

// TestKingsoftProvider 测试金山云提供商
func TestKingsoftProvider(t *testing.T) {
	config := ProviderConfig{
		Provider:  "kingsoft",
		Region:    "cn-beijing-6",
		AccessKey: "test_access_key",
		SecretKey: "test_secret_key",
		Endpoint:  "",
	}
	
	provider := NewKingsoftProvider(config)
	
	// 测试获取支持的资源类型
	resourceTypes := provider.GetSupportedResourceTypes()
	expectedTypes := []string{"ecs", "rds", "redis", "slb"}
	
	if len(resourceTypes) != len(expectedTypes) {
		t.Errorf("Expected %d resource types, got %d", len(expectedTypes), len(resourceTypes))
	}
	
	for _, expectedType := range expectedTypes {
		found := false
		for _, actualType := range resourceTypes {
			if actualType == expectedType {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("Expected resource type %s not found", expectedType)
		}
	}
	
	// 测试连接（这里只是测试方法调用，不测试实际连接）
	err := provider.TestConnection()
	if err != nil {
		t.Logf("Connection test returned error (expected in test environment): %v", err)
	}
}

// TestVolcanoProvider 测试火山云提供商
func TestVolcanoProvider(t *testing.T) {
	config := ProviderConfig{
		Provider:  "volcano",
		Region:    "cn-beijing",
		AccessKey: "test_access_key",
		SecretKey: "test_secret_key",
		Endpoint:  "",
	}
	
	provider := NewVolcanoProvider(config)
	
	// 测试获取支持的资源类型
	resourceTypes := provider.GetSupportedResourceTypes()
	expectedTypes := []string{"ecs", "rds", "redis", "clb"}
	
	if len(resourceTypes) != len(expectedTypes) {
		t.Errorf("Expected %d resource types, got %d", len(expectedTypes), len(resourceTypes))
	}
	
	for _, expectedType := range expectedTypes {
		found := false
		for _, actualType := range resourceTypes {
			if actualType == expectedType {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("Expected resource type %s not found", expectedType)
		}
	}
	
	// 测试连接（这里只是测试方法调用，不测试实际连接）
	err := provider.TestConnection()
	if err != nil {
		t.Logf("Connection test returned error (expected in test environment): %v", err)
	}
}

// TestAliyunProvider 测试阿里云提供商
func TestAliyunProvider(t *testing.T) {
	config := ProviderConfig{
		Provider:  "aliyun",
		Region:    "cn-hangzhou",
		AccessKey: "test_access_key",
		SecretKey: "test_secret_key",
		Endpoint:  "",
	}

	provider := NewAliyunProvider(config)

	// 测试获取支持的资源类型
	resourceTypes := provider.GetSupportedResourceTypes()
	expectedTypes := []string{"ecs", "rds", "redis", "slb", "vpc"}

	if len(resourceTypes) != len(expectedTypes) {
		t.Errorf("Expected %d resource types, got %d", len(expectedTypes), len(resourceTypes))
	}

	for _, expectedType := range expectedTypes {
		found := false
		for _, actualType := range resourceTypes {
			if actualType == expectedType {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("Expected resource type %s not found", expectedType)
		}
	}

	// 测试连接
	err := provider.TestConnection()
	if err != nil {
		t.Logf("Connection test returned error (expected in test environment): %v", err)
	}
}

// TestTencentProvider 测试腾讯云提供商
func TestTencentProvider(t *testing.T) {
	config := ProviderConfig{
		Provider:  "tencent",
		Region:    "ap-beijing",
		AccessKey: "test_access_key",
		SecretKey: "test_secret_key",
		Endpoint:  "",
	}

	provider := NewTencentProvider(config)

	// 测试获取支持的资源类型
	resourceTypes := provider.GetSupportedResourceTypes()
	expectedTypes := []string{"cvm", "cdb", "redis", "clb", "vpc"}

	if len(resourceTypes) != len(expectedTypes) {
		t.Errorf("Expected %d resource types, got %d", len(expectedTypes), len(resourceTypes))
	}

	for _, expectedType := range expectedTypes {
		found := false
		for _, actualType := range resourceTypes {
			if actualType == expectedType {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("Expected resource type %s not found", expectedType)
		}
	}

	// 测试连接
	err := provider.TestConnection()
	if err != nil {
		t.Logf("Connection test returned error (expected in test environment): %v", err)
	}
}

// TestCtyunProvider 测试天翼云提供商
func TestCtyunProvider(t *testing.T) {
	config := ProviderConfig{
		Provider:  "ctyun",
		Region:    "cn-bj",
		AccessKey: "test_access_key",
		SecretKey: "test_secret_key",
		Endpoint:  "",
	}

	provider := NewCtyunProvider(config)

	// 测试获取支持的资源类型
	resourceTypes := provider.GetSupportedResourceTypes()
	expectedTypes := []string{"ecs", "rds", "redis", "elb", "vpc"}

	if len(resourceTypes) != len(expectedTypes) {
		t.Errorf("Expected %d resource types, got %d", len(expectedTypes), len(resourceTypes))
	}

	for _, expectedType := range expectedTypes {
		found := false
		for _, actualType := range resourceTypes {
			if actualType == expectedType {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("Expected resource type %s not found", expectedType)
		}
	}

	// 测试连接
	err := provider.TestConnection()
	if err != nil {
		t.Logf("Connection test returned error (expected in test environment): %v", err)
	}
}
