package cloud

import (
	"bytes"
	"crypto/hmac"
	"crypto/sha1"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"sort"
	"strings"
	"time"
)

// HTTPClient 通用HTTP客户端
type HTTPClient struct {
	client    *http.Client
	baseURL   string
	accessKey string
	secretKey string
	region    string
	provider  string
}

// NewHTTPClient 创建HTTP客户端
func NewHTTPClient(provider, baseURL, accessKey, secretKey, region string) *HTTPClient {
	return &HTTPClient{
		client:    &http.Client{Timeout: 30 * time.Second},
		baseURL:   baseURL,
		accessKey: accessKey,
		secretKey: secretKey,
		region:    region,
		provider:  provider,
	}
}

// APIResponse 通用API响应结构
type APIResponse struct {
	Code      interface{} `json:"code"`
	Message   string      `json:"message"`
	Data      interface{} `json:"data"`
	RequestId string      `json:"requestId"`
	Error     interface{} `json:"error"`
}

// MakeRequest 发起HTTP请求
func (c *HTTPClient) MakeRequest(method, path string, params map[string]string, body interface{}) (*APIResponse, error) {
	// 构建URL
	apiURL := c.baseURL + path
	if params != nil && len(params) > 0 {
		values := url.Values{}
		for k, v := range params {
			values.Add(k, v)
		}
		if method == "GET" {
			apiURL += "?" + values.Encode()
		}
	}
	
	// 构建请求体
	var reqBody io.Reader
	if body != nil {
		bodyBytes, err := json.Marshal(body)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal request body: %v", err)
		}
		reqBody = bytes.NewReader(bodyBytes)
	}
	
	// 创建请求
	req, err := http.NewRequest(method, apiURL, reqBody)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %v", err)
	}
	
	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("User-Agent", "arboris-cloud-client/1.0")
	
	// 添加认证签名
	if err := c.signRequest(req, params); err != nil {
		return nil, fmt.Errorf("failed to sign request: %v", err)
	}
	
	// 发送请求
	resp, err := c.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %v", err)
	}
	defer resp.Body.Close()
	
	// 读取响应
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %v", err)
	}
	
	// 解析响应
	var apiResp APIResponse
	if err := json.Unmarshal(respBody, &apiResp); err != nil {
		return nil, fmt.Errorf("failed to parse response: %v", err)
	}
	
	// 检查错误
	if err := c.checkError(&apiResp); err != nil {
		return nil, err
	}
	
	return &apiResp, nil
}

// signRequest 为请求添加签名
func (c *HTTPClient) signRequest(req *http.Request, params map[string]string) error {
	switch c.provider {
	case "aliyun":
		return c.signAliyunRequest(req, params)
	case "tencent":
		return c.signTencentRequest(req, params)
	case "kingsoft":
		return c.signKingsoftRequest(req, params)
	case "volcano":
		return c.signVolcanoRequest(req, params)
	case "ctyun":
		return c.signCtyunRequest(req, params)
	default:
		return fmt.Errorf("unsupported provider: %s", c.provider)
	}
}

// signAliyunRequest 阿里云签名
func (c *HTTPClient) signAliyunRequest(req *http.Request, params map[string]string) error {
	// 阿里云签名实现
	timestamp := time.Now().UTC().Format("2006-01-02T15:04:05Z")
	nonce := fmt.Sprintf("%d", time.Now().UnixNano())
	
	// 添加公共参数
	if params == nil {
		params = make(map[string]string)
	}
	params["AccessKeyId"] = c.accessKey
	params["SignatureMethod"] = "HMAC-SHA1"
	params["SignatureVersion"] = "1.0"
	params["Timestamp"] = timestamp
	params["SignatureNonce"] = nonce
	params["Format"] = "JSON"
	
	// 计算签名
	signature := c.calculateAliyunSignature(params)
	params["Signature"] = signature
	
	// 更新URL
	values := url.Values{}
	for k, v := range params {
		values.Add(k, v)
	}
	req.URL.RawQuery = values.Encode()
	
	return nil
}

// signTencentRequest 腾讯云签名
func (c *HTTPClient) signTencentRequest(req *http.Request, params map[string]string) error {
	// 腾讯云签名实现
	timestamp := fmt.Sprintf("%d", time.Now().Unix())
	
	req.Header.Set("X-TC-Action", params["Action"])
	req.Header.Set("X-TC-Version", params["Version"])
	req.Header.Set("X-TC-Region", c.region)
	req.Header.Set("X-TC-Timestamp", timestamp)
	
	// 计算签名
	signature := c.calculateTencentSignature(req, timestamp)
	authorization := fmt.Sprintf("TC3-HMAC-SHA256 Credential=%s/%s/tc3_request, SignedHeaders=content-type;host;x-tc-action, Signature=%s",
		c.accessKey, time.Now().UTC().Format("2006-01-02"), signature)
	req.Header.Set("Authorization", authorization)
	
	return nil
}

// signKingsoftRequest 金山云签名
func (c *HTTPClient) signKingsoftRequest(req *http.Request, params map[string]string) error {
	// 金山云签名实现
	timestamp := time.Now().UTC().Format("2006-01-02T15:04:05Z")
	
	if params == nil {
		params = make(map[string]string)
	}
	params["AccessKeyId"] = c.accessKey
	params["SignatureMethod"] = "HmacSHA1"
	params["SignatureVersion"] = "1.0"
	params["Timestamp"] = timestamp
	
	// 计算签名
	signature := c.calculateKingsoftSignature(params)
	params["Signature"] = signature
	
	// 更新URL
	values := url.Values{}
	for k, v := range params {
		values.Add(k, v)
	}
	req.URL.RawQuery = values.Encode()
	
	return nil
}

// signVolcanoRequest 火山云签名
func (c *HTTPClient) signVolcanoRequest(req *http.Request, params map[string]string) error {
	// 火山云签名实现
	timestamp := fmt.Sprintf("%d", time.Now().Unix())
	
	req.Header.Set("X-Date", timestamp)
	req.Header.Set("Authorization", fmt.Sprintf("HMAC-SHA256 AccessKey=%s", c.accessKey))
	
	// 简化的签名实现
	signString := fmt.Sprintf("%s\n%s\n%s", req.Method, req.URL.Path, timestamp)
	signature := c.calculateHMACSHA256(signString, c.secretKey)
	req.Header.Set("X-Signature", signature)
	
	return nil
}

// signCtyunRequest 天翼云签名
func (c *HTTPClient) signCtyunRequest(req *http.Request, params map[string]string) error {
	// 天翼云签名实现
	timestamp := fmt.Sprintf("%d", time.Now().Unix())
	
	req.Header.Set("X-Ctyun-Date", timestamp)
	
	// 构建签名字符串
	signString := c.buildCtyunSignString(req, timestamp)
	signature := c.calculateHMACSHA256(signString, c.secretKey)
	
	req.Header.Set("Authorization", fmt.Sprintf("CTYUN-HMAC-SHA256 AccessKeyId=%s,Signature=%s", c.accessKey, signature))
	
	return nil
}

// calculateAliyunSignature 计算阿里云签名
func (c *HTTPClient) calculateAliyunSignature(params map[string]string) string {
	// 排序参数
	var keys []string
	for key := range params {
		if key != "Signature" {
			keys = append(keys, key)
		}
	}
	sort.Strings(keys)
	
	// 构建查询字符串
	var queryParts []string
	for _, key := range keys {
		queryParts = append(queryParts, fmt.Sprintf("%s=%s", url.QueryEscape(key), url.QueryEscape(params[key])))
	}
	queryString := strings.Join(queryParts, "&")
	
	// 构建签名字符串
	signString := "GET&%2F&" + url.QueryEscape(queryString)
	
	// 计算HMAC-SHA1签名
	h := hmac.New(sha1.New, []byte(c.secretKey+"&"))
	h.Write([]byte(signString))
	signature := base64.StdEncoding.EncodeToString(h.Sum(nil))
	
	return signature
}

// calculateTencentSignature 计算腾讯云签名
func (c *HTTPClient) calculateTencentSignature(req *http.Request, timestamp string) string {
	// 简化的腾讯云签名实现
	signString := fmt.Sprintf("%s\n%s\n%s", req.Method, req.URL.Path, timestamp)
	return c.calculateHMACSHA256(signString, c.secretKey)
}

// calculateKingsoftSignature 计算金山云签名
func (c *HTTPClient) calculateKingsoftSignature(params map[string]string) string {
	// 排序参数
	var keys []string
	for key := range params {
		if key != "Signature" {
			keys = append(keys, key)
		}
	}
	sort.Strings(keys)
	
	// 构建查询字符串
	var queryParts []string
	for _, key := range keys {
		queryParts = append(queryParts, fmt.Sprintf("%s=%s", url.QueryEscape(key), url.QueryEscape(params[key])))
	}
	queryString := strings.Join(queryParts, "&")
	
	// 构建签名字符串
	signString := "GET&%2F&" + url.QueryEscape(queryString)
	
	// 计算HMAC-SHA1签名
	h := hmac.New(sha1.New, []byte(c.secretKey+"&"))
	h.Write([]byte(signString))
	signature := base64.StdEncoding.EncodeToString(h.Sum(nil))
	
	return signature
}

// calculateHMACSHA256 计算HMAC-SHA256签名
func (c *HTTPClient) calculateHMACSHA256(data, key string) string {
	h := hmac.New(sha256.New, []byte(key))
	h.Write([]byte(data))
	return hex.EncodeToString(h.Sum(nil))
}

// buildCtyunSignString 构建天翼云签名字符串
func (c *HTTPClient) buildCtyunSignString(req *http.Request, timestamp string) string {
	method := req.Method
	path := req.URL.Path
	if req.URL.RawQuery != "" {
		path += "?" + req.URL.RawQuery
	}
	
	return fmt.Sprintf("%s\n%s\n%s", method, path, timestamp)
}

// checkError 检查API错误
func (c *HTTPClient) checkError(resp *APIResponse) error {
	switch c.provider {
	case "aliyun", "kingsoft":
		if resp.Error != nil {
			return fmt.Errorf("API error: %v", resp.Error)
		}
	case "tencent":
		if code, ok := resp.Code.(string); ok && code != "" {
			return fmt.Errorf("API error: %s - %s", code, resp.Message)
		}
	case "volcano", "ctyun":
		if code, ok := resp.Code.(float64); ok && code != 0 {
			return fmt.Errorf("API error: %s", resp.Message)
		}
	}
	return nil
}
