package jumpserver

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/toolkits/pkg/logger"
)

// Client JumpServer API客户端
type Client struct {
	baseURL      string
	username     string
	password     string
	token        string
	organization string
	timeout      time.Duration
	httpClient   *http.Client
}

// NewClient 创建新的JumpServer客户端
func NewClient(baseURL, username, password, token, organization string, timeout time.Duration) *Client {
	return &Client{
		baseURL:      baseURL,
		username:     username,
		password:     password,
		token:        token,
		organization: organization,
		timeout:      timeout,
		httpClient: &http.Client{
			Timeout: timeout,
		},
	}
}

// TestConnection 测试连接
func (c *Client) TestConnection() error {
	req, err := http.NewRequest("GET", c.baseURL+"/api/v1/users/profile/", nil)
	if err != nil {
		return err
	}

	c.setHeaders(req)

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("connection failed: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("authentication failed: status=%d", resp.StatusCode)
	}

	logger.Info("JumpServer connection test successful")
	return nil
}

// GetNodeByValue 根据value获取节点
func (c *Client) GetNodeByValue(value string) (*Node, error) {
	url := fmt.Sprintf("%s/api/v1/assets/nodes/?value=%s", c.baseURL, url.QueryEscape(value))

	// 根据AMS分支的实现，节点查询可能直接返回数组格式
	var nodes []Node
	if err := c.doRequest("GET", url, nil, &nodes); err != nil {
		// 如果直接解析失败，尝试APIResponse格式
		var response APIResponse
		if err2 := c.doRequest("GET", url, nil, &response); err2 != nil {
			return nil, err
		}

		results, ok := response.Results.([]interface{})
		if !ok || len(results) == 0 {
			return nil, nil
		}

		// 转换为Node结构
		nodeData, _ := json.Marshal(results[0])
		var node Node
		if err := json.Unmarshal(nodeData, &node); err != nil {
			return nil, fmt.Errorf("failed to parse node data: %v", err)
		}
		return &node, nil
	}

	if len(nodes) == 0 {
		return nil, nil
	}

	return &nodes[0], nil
}

// CreateNode 创建节点
func (c *Client) CreateNode(node *Node) (*Node, error) {
	var url string
	var req map[string]interface{}

	if node.Parent == "" {
		// 创建根级节点
		url = fmt.Sprintf("%s/api/v1/assets/nodes/", c.baseURL)
		req = map[string]interface{}{
			"key":   node.Key,
			"value": node.Value,
		}
	} else {
		// 创建子节点，使用 children API
		url = fmt.Sprintf("%s/api/v1/assets/nodes/%s/children/", c.baseURL, node.Parent)
		req = map[string]interface{}{
			"value": node.Value,
		}
	}

	logger.Infof("Creating node with URL: %s, Request: %+v", url, req)

	var createdNode Node
	if err := c.doRequest("POST", url, req, &createdNode); err != nil {
		logger.Infof("Failed to create node: URL=%s, Request=%+v, Error=%v", url, req, err)
		return nil, err
	}

	logger.Infof("Successfully created node: %+v", createdNode)
	return &createdNode, nil
}

// GetNodeByParentAndValue 根据父节点ID和节点名称获取节点
// 这是正确的方式来检查同名节点的唯一性
func (c *Client) GetNodeByParentAndValue(parentID, value string) (*Node, error) {
	logger.Infof("GetNodeByParentAndValue called: parentID=%s, value=%s", parentID, value)

	var apiURL string
	if parentID == "" {
		// 获取所有根级节点，然后在客户端过滤
		apiURL = fmt.Sprintf("%s/api/v1/assets/nodes/", c.baseURL)
	} else {
		// 获取指定父节点下的所有子节点，然后在客户端过滤
		apiURL = fmt.Sprintf("%s/api/v1/assets/nodes/%s/children/", c.baseURL, parentID)
	}

	logger.Infof("API URL: %s", apiURL)

	// 尝试直接解析为数组格式
	var nodes []Node
	if err := c.doRequest("GET", apiURL, nil, &nodes); err != nil {
		// 如果直接解析失败，尝试APIResponse格式
		var response APIResponse
		if err2 := c.doRequest("GET", apiURL, nil, &response); err2 != nil {
			return nil, err2
		}

		results, ok := response.Results.([]interface{})
		if !ok || len(results) == 0 {
			return nil, nil // 节点不存在
		}

		// 转换为Node结构数组
		nodes = make([]Node, len(results))
		for i, result := range results {
			nodeData, _ := json.Marshal(result)
			if err := json.Unmarshal(nodeData, &nodes[i]); err != nil {
				return nil, fmt.Errorf("failed to parse node data at index %d: %v", i, err)
			}
		}
	}

	logger.Infof("Retrieved %d nodes from API", len(nodes))

	// 在客户端过滤匹配的节点
	for i, node := range nodes {
		logger.Infof("Node %d: ID=%s, Value=%s, Key=%s", i+1, node.ID, node.Value, node.Key)
		if node.Value == value {
			logger.Infof("Found matching node: ID=%s, Value=%s", node.ID, node.Value)
			return &node, nil
		}
	}

	logger.Infof("No matching node found for value: %s", value)
	return nil, nil // 节点不存在
}

// GetNodeChildren 获取指定父节点下的所有子节点
func (c *Client) GetNodeChildren(parentID string) ([]Node, error) {
	var apiURL string
	if parentID == "" {
		// 获取根级节点
		apiURL = fmt.Sprintf("%s/api/v1/assets/nodes/", c.baseURL)
	} else {
		// 获取指定父节点下的子节点
		apiURL = fmt.Sprintf("%s/api/v1/assets/nodes/%s/children/", c.baseURL, parentID)
	}

	// 尝试直接解析为数组格式
	var nodes []Node
	if err := c.doRequest("GET", apiURL, nil, &nodes); err != nil {
		// 如果直接解析失败，尝试APIResponse格式
		var response APIResponse
		if err2 := c.doRequest("GET", apiURL, nil, &response); err2 != nil {
			return nil, err2
		}

		results, ok := response.Results.([]interface{})
		if !ok {
			return []Node{}, nil // 返回空数组而不是nil
		}

		// 转换为Node结构数组
		nodes = make([]Node, len(results))
		for i, result := range results {
			nodeData, _ := json.Marshal(result)
			if err := json.Unmarshal(nodeData, &nodes[i]); err != nil {
				return nil, fmt.Errorf("failed to parse node data at index %d: %v", i, err)
			}
		}
	}

	return nodes, nil
}

// CreateNodeHierarchy 创建节点层级结构
func (c *Client) CreateNodeHierarchy(fullPath string) (*Node, error) {
	// 分解路径，逐级创建节点
	parts := strings.Split(strings.Trim(fullPath, "/"), "/")
	if len(parts) == 0 || (len(parts) == 1 && parts[0] == "") {
		return nil, fmt.Errorf("invalid path: %s", fullPath)
	}

	var lastCreatedNode *Node
	var parentNodeID string

	for _, part := range parts {
		if part == "" {
			continue
		}

		// 检查当前节点是否已存在（通过父节点ID和节点名称）
		if existingNode, err := c.GetNodeByParentAndValue(parentNodeID, part); err == nil && existingNode != nil {
			lastCreatedNode = existingNode
			parentNodeID = existingNode.ID
			continue
		}

		// 创建新节点
		node := &Node{
			Key:    part,
			Value:  strings.Title(part), // 首字母大写作为显示名
			Parent: parentNodeID,        // 使用父节点ID
		}

		createdNode, err := c.CreateNode(node)
		if err != nil {
			return nil, fmt.Errorf("failed to create node %s: %v", part, err)
		}

		lastCreatedNode = createdNode
		parentNodeID = createdNode.ID // 更新父节点ID为当前创建的节点ID
	}

	return lastCreatedNode, nil
}

// UpdateNode 更新节点
func (c *Client) UpdateNode(nodeID string, node *Node) (*Node, error) {
	url := fmt.Sprintf("%s/api/v1/assets/nodes/%s/", c.baseURL, nodeID)

	// 创建更新请求对象，不包含ID字段
	updateReq := map[string]interface{}{
		"key":       node.Key,
		"value":     node.Value,
		"parent":    node.Parent,
		"full_path": node.FullPath,
		"meta":      node.Meta,
	}

	logger.Infof("Updating node with ID %s: value=%s, key=%s", nodeID, node.Value, node.Key)

	var updatedNode Node
	if err := c.doRequest("PUT", url, updateReq, &updatedNode); err != nil {
		logger.Errorf("Failed to update node %s: %v", nodeID, err)
		return nil, err
	}

	logger.Infof("Successfully updated node: id=%s, value=%s", updatedNode.ID, updatedNode.Value)
	return &updatedNode, nil
}

// DeleteNode 删除节点
func (c *Client) DeleteNode(nodeID string) error {
	url := fmt.Sprintf("%s/api/v1/assets/nodes/%s/", c.baseURL, nodeID)

	return c.doRequest("DELETE", url, nil, nil)
}

// GetAssetByIP 根据IP地址精确获取资产
func (c *Client) GetAssetByIP(ip string) (*Asset, error) {
	// 使用hosts端点进行精确查询，通过address参数，添加display和draw参数
	url := fmt.Sprintf("%s/api/v1/assets/hosts/?address=%s&offset=0&limit=15&display=1&draw=1", c.baseURL, url.QueryEscape(ip))
	logger.Infof("Getting asset by IP: ip=%s, url=%s", ip, url)

	// JumpServer hosts API返回的是分页格式，使用更宽松的结构体
	var response struct {
		Count    int                      `json:"count"`
		Next     *string                  `json:"next"`
		Previous *string                  `json:"previous"`
		Results  []map[string]interface{} `json:"results"`
	}

	err := c.doRequest("GET", url, nil, &response)
	if err != nil {
		logger.Errorf("Failed to get asset by IP %s: %v", ip, err)
		return nil, fmt.Errorf("failed to get asset by IP: %v", err)
	}

	if response.Count == 0 || len(response.Results) == 0 {
		return nil, nil
	}

	// 手动解析第一个资产
	rawAsset := response.Results[0]

	// 创建Asset对象并手动映射字段
	asset := &Asset{}

	if id, ok := rawAsset["id"].(string); ok {
		asset.ID = id
	}
	if name, ok := rawAsset["name"].(string); ok {
		asset.Name = name
		asset.Hostname = name // 同时设置hostname字段
	}
	if address, ok := rawAsset["address"].(string); ok {
		asset.Address = address
		asset.IP = address // 同时设置ip字段
	}
	if comment, ok := rawAsset["comment"].(string); ok {
		asset.Comment = comment
	}
	if isActive, ok := rawAsset["is_active"].(bool); ok {
		asset.IsActive = isActive
	}

	// 处理nodes字段（可能是复杂结构）
	if nodes, ok := rawAsset["nodes"].([]interface{}); ok {
		asset.Nodes = make([]string, 0, len(nodes))
		for _, node := range nodes {
			if nodeMap, ok := node.(map[string]interface{}); ok {
				if nodeID, ok := nodeMap["id"].(string); ok {
					asset.Nodes = append(asset.Nodes, nodeID)
				}
			}
		}
	}

	return asset, nil
}

// GetAssetBySearch 根据搜索条件获取资产（用于名称等模糊搜索）
func (c *Client) GetAssetBySearch(searchTerm string) (*Asset, error) {
	// 使用assets端点进行搜索，这是JumpServer的标准资产搜索接口
	url := fmt.Sprintf("%s/api/v1/assets/assets/?search=%s&limit=15&offset=0", c.baseURL, url.QueryEscape(searchTerm))
	logger.Infof("Searching for asset: search=%s, url=%s", searchTerm, url)

	// JumpServer资产搜索API返回的是分页格式
	var response struct {
		Count    int     `json:"count"`
		Next     *string `json:"next"`
		Previous *string `json:"previous"`
		Results  []Asset `json:"results"`
	}

	err := c.doRequest("GET", url, nil, &response)
	if err != nil {
		return nil, fmt.Errorf("failed to search asset: %v", err)
	}

	if response.Count == 0 || len(response.Results) == 0 {
		return nil, nil
	}

	// 返回第一个匹配的资产
	asset := &response.Results[0]

	// 确保字段映射正确
	if asset.Name == "" && asset.Hostname != "" {
		asset.Name = asset.Hostname
	}
	if asset.Address == "" && asset.IP != "" {
		asset.Address = asset.IP
	}

	return asset, nil
}

// GetAssetByName 根据名称获取资产（使用搜索接口）
func (c *Client) GetAssetByName(name string) (*Asset, error) {
	return c.GetAssetBySearch(name)
}

// CreateAsset 创建资产
func (c *Client) CreateAsset(asset *Asset) (*Asset, error) {
	url := fmt.Sprintf("%s/api/v1/assets/hosts/", c.baseURL)

	// 根据AMS分支的实现，构建主机创建请求
	hostReq := map[string]interface{}{
		"name":     asset.Hostname,
		"address":  asset.IP,
		"platform": 1, // 平台 ID，1通常表示Linux
		"comment":  asset.Comment,
		"nodes":    asset.Nodes, // 添加节点信息
	}

	var createdHost map[string]interface{}
	if err := c.doRequest("POST", url, hostReq, &createdHost); err != nil {
		return nil, err
	}

	// 转换响应为Asset结构
	createdAsset := &Asset{
		IsActive: true,
	}

	// 安全解析各个字段
	if id, ok := createdHost["id"].(string); ok {
		createdAsset.ID = id
	}
	if hostname, ok := createdHost["name"].(string); ok {
		createdAsset.Hostname = hostname
	}
	if ip, ok := createdHost["address"].(string); ok {
		createdAsset.IP = ip
	}
	if comment, ok := createdHost["comment"].(string); ok {
		createdAsset.Comment = comment
	}

	// 处理节点信息
	if nodes, ok := createdHost["nodes"].([]interface{}); ok {
		for _, n := range nodes {
			if node, ok := n.(map[string]interface{}); ok {
				if nodeID, ok := node["id"].(string); ok {
					createdAsset.Nodes = append(createdAsset.Nodes, nodeID)
				}
			}
		}
	}

	return createdAsset, nil
}

// UpdateAsset 更新资产
func (c *Client) UpdateAsset(assetID string, asset *Asset) (*Asset, error) {
	// 使用hosts端点更新资产
	url := fmt.Sprintf("%s/api/v1/assets/hosts/%s/", c.baseURL, assetID)

	// 构建更新请求
	updateReq := map[string]interface{}{
		"name":    asset.Hostname,
		"address": asset.IP,
		"comment": asset.Comment,
		"nodes":   asset.Nodes,
	}

	var updatedHost map[string]interface{}
	if err := c.doRequest("PATCH", url, updateReq, &updatedHost); err != nil {
		return nil, err
	}

	// 转换响应为Asset结构
	updatedAsset := &Asset{
		ID:       assetID,
		IsActive: true,
	}

	if hostname, ok := updatedHost["name"].(string); ok {
		updatedAsset.Hostname = hostname
	}
	if ip, ok := updatedHost["address"].(string); ok {
		updatedAsset.IP = ip
	}
	if comment, ok := updatedHost["comment"].(string); ok {
		updatedAsset.Comment = comment
	}

	return updatedAsset, nil
}

// DeleteAsset 删除资产
func (c *Client) DeleteAsset(assetID string) error {
	// 使用hosts端点删除资产
	url := fmt.Sprintf("%s/api/v1/assets/hosts/%s/", c.baseURL, assetID)

	return c.doRequest("DELETE", url, nil, nil)
}

// doRequest 执行HTTP请求
func (c *Client) doRequest(method, url string, body interface{}, result interface{}) error {
	var reqBody io.Reader
	if body != nil {
		jsonData, err := json.Marshal(body)
		if err != nil {
			return fmt.Errorf("failed to marshal request body: %v", err)
		}
		reqBody = bytes.NewBuffer(jsonData)
	}

	req, err := http.NewRequest(method, url, reqBody)
	if err != nil {
		return fmt.Errorf("failed to create request: %v", err)
	}

	c.setHeaders(req)

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("request failed: %v", err)
	}
	defer resp.Body.Close()

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read response body: %v", err)
	}

	if resp.StatusCode >= 400 {
		var errorResp ErrorResponse
		if err := json.Unmarshal(respBody, &errorResp); err == nil {
			if errorResp.Detail != "" {
				return fmt.Errorf("API error: %s", errorResp.Detail)
			}
			if len(errorResp.Errors) > 0 {
				var errorMsgs []string
				for field, msg := range errorResp.Errors {
					errorMsgs = append(errorMsgs, fmt.Sprintf("%s: %s", field, msg))
				}
				return fmt.Errorf("API validation errors: %s", strings.Join(errorMsgs, "; "))
			}
		}
		return fmt.Errorf("HTTP error: status=%d, body=%s", resp.StatusCode, string(respBody))
	}

	if result != nil && method != "DELETE" {
		if err := json.Unmarshal(respBody, result); err != nil {
			return fmt.Errorf("failed to unmarshal response: %v", err)
		}
	}

	return nil
}

// setHeaders 设置请求头
func (c *Client) setHeaders(req *http.Request) {
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")

	if c.organization != "" {
		req.Header.Set("X-JMS-ORG", c.organization)
	}

	if c.token != "" {
		req.Header.Set("Authorization", "Token "+c.token)
	} else if c.username != "" && c.password != "" {
		req.SetBasicAuth(c.username, c.password)
	}
}

// ==================== 用户管理API ====================

// CreateUser 创建用户（不设置密码）
func (c *Client) CreateUser(user *User) (*User, error) {
	url := fmt.Sprintf("%s/api/v1/users/users/", c.baseURL)

	// 创建专门的请求结构，只包含JumpServer需要的字段
	userReq := map[string]interface{}{
		"username":     user.Username,
		"name":         user.Name,
		"email":        user.Email,
		"is_active":    user.IsActive,
		"is_superuser": user.IsSuperuser,
		"source":       user.Source,
		"groups":       user.Groups,
		"system_roles": user.SystemRoles,
		"org_roles":    user.OrgRoles,
	}

	// 只有在有值的情况下才添加可选字段
	if user.Phone != "" {
		userReq["phone"] = user.Phone
	}
	if user.Comment != "" {
		userReq["comment"] = user.Comment
	}

	var result User
	err := c.doRequest("POST", url, userReq, &result)
	if err != nil {
		logger.Errorf("Failed to create user in JumpServer: URL=%s, Request=%+v, Error=%v", url, userReq, err)
		return nil, fmt.Errorf("failed to create user: %v", err)
	}
	return &result, nil
}

// CreateUserWithPassword 创建用户并设置密码（两步操作）
func (c *Client) CreateUserWithPassword(user *User) (*User, error) {
	// 第一步：创建用户（不设置密码）
	createdUser, err := c.CreateUser(user)
	if err != nil {
		return nil, err
	}

	// 第二步：设置密码（如果提供了密码）
	if user.Password != "" {
		err = c.ChangeUserPassword(createdUser.ID, user.Password)
		if err != nil {
			logger.Infof("Warning: User created but failed to set password for %s: %v", user.Username, err)
			// 不返回错误，因为用户已经创建成功
		} else {
			logger.Infof("Password set successfully for user: %s", user.Username)
		}
	}

	return createdUser, nil
}

// GetUser 获取用户
func (c *Client) GetUser(username string) (*User, error) {
	url := fmt.Sprintf("%s/api/v1/users/users/?username=%s", c.baseURL, url.QueryEscape(username))

	// 尝试直接解析为数组格式
	var users []User
	if err := c.doRequest("GET", url, nil, &users); err != nil {
		// 如果直接解析失败，尝试APIResponse格式
		var response APIResponse
		if err2 := c.doRequest("GET", url, nil, &response); err2 != nil {
			return nil, fmt.Errorf("failed to get user: %v", err2)
		}

		results, ok := response.Results.([]interface{})
		if !ok || len(results) == 0 {
			return nil, fmt.Errorf("user not found: %s", username)
		}

		userBytes, err := json.Marshal(results[0])
		if err != nil {
			return nil, fmt.Errorf("failed to marshal user data: %v", err)
		}

		var user User
		err = json.Unmarshal(userBytes, &user)
		if err != nil {
			return nil, fmt.Errorf("failed to unmarshal user data: %v", err)
		}
		return &user, nil
	}

	if len(users) == 0 {
		return nil, fmt.Errorf("user not found: %s", username)
	}

	return &users[0], nil
}

// UpdateUser 更新用户
func (c *Client) UpdateUser(userID string, user *User) (*User, error) {
	url := fmt.Sprintf("%s/api/v1/users/users/%s/", c.baseURL, userID)
	var result User
	err := c.doRequest("PUT", url, user, &result)
	if err != nil {
		return nil, fmt.Errorf("failed to update user: %v", err)
	}
	return &result, nil
}

// UpdateUserStatus 只更新用户状态
func (c *Client) UpdateUserStatus(userID string, isActive bool) error {
	url := fmt.Sprintf("%s/api/v1/users/users/%s/", c.baseURL, userID)

	// 只发送需要更新的字段
	data := map[string]interface{}{
		"is_active": isActive,
	}

	err := c.doRequest("PATCH", url, data, nil)
	if err != nil {
		return fmt.Errorf("failed to update user status: %v", err)
	}
	return nil
}

// DeleteUser 删除用户
func (c *Client) DeleteUser(userID string) error {
	url := fmt.Sprintf("%s/api/v1/users/users/%s/", c.baseURL, userID)
	err := c.doRequest("DELETE", url, nil, nil)
	if err != nil {
		return fmt.Errorf("failed to delete user: %v", err)
	}
	return nil
}

// ChangeUserPassword 修改用户密码
func (c *Client) ChangeUserPassword(userID, newPassword string) error {
	url := fmt.Sprintf("%s/api/v1/users/users/%s/password/", c.baseURL, userID)
	data := map[string]string{
		"password": newPassword,
	}

	logger.Infof("Setting password for user: userID=%s", userID)
	err := c.doRequest("PATCH", url, data, nil)
	if err != nil {
		logger.Errorf("Failed to set password for user %s: %v", userID, err)
		return fmt.Errorf("failed to change user password: %v", err)
	}
	logger.Infof("Password set successfully for user: %s", userID)
	return nil
}

// ==================== 用户组管理API ====================

// CreateUserGroup 创建用户组
func (c *Client) CreateUserGroup(group *UserGroup) (*UserGroup, error) {
	url := fmt.Sprintf("%s/api/v1/users/groups/", c.baseURL)

	// 创建专门的请求结构，只包含JumpServer需要的字段
	groupReq := map[string]interface{}{
		"name":    group.Name,
		"comment": group.Comment,
		"users":   group.GetUsernames(), // 使用GetUsernames方法获取用户名列表
		"labels":  []string{},           // 初始化为空数组，避免null值
	}

	var result UserGroup
	err := c.doRequest("POST", url, groupReq, &result)
	if err != nil {
		logger.Errorf("Failed to create user group in JumpServer: URL=%s, Request=%+v, Error=%v", url, groupReq, err)
		return nil, fmt.Errorf("failed to create user group: %v", err)
	}
	return &result, nil
}

// GetUserGroup 获取用户组
func (c *Client) GetUserGroup(name string) (*UserGroup, error) {
	url := fmt.Sprintf("%s/api/v1/users/groups/?search=%s&offset=0&limit=15&display=1", c.baseURL, url.QueryEscape(name))

	// 搜索API返回分页格式
	var response struct {
		Count    int         `json:"count"`
		Next     interface{} `json:"next"`
		Previous interface{} `json:"previous"`
		Results  []UserGroup `json:"results"`
	}

	err := c.doRequest("GET", url, nil, &response)
	if err != nil {
		return nil, fmt.Errorf("failed to search user group: %v", err)
	}

	if response.Count == 0 || len(response.Results) == 0 {
		return nil, fmt.Errorf("user group not found: %s", name)
	}

	// 查找精确匹配的用户组
	for _, group := range response.Results {
		if group.Name == name {
			return &group, nil
		}
	}

	// 如果没有精确匹配，返回第一个结果
	return &response.Results[0], nil
}

// UpdateUserGroup 更新用户组
func (c *Client) UpdateUserGroup(groupID string, group *UserGroup) (*UserGroup, error) {
	url := fmt.Sprintf("%s/api/v1/users/groups/%s/", c.baseURL, groupID)
	var result UserGroup
	err := c.doRequest("PUT", url, group, &result)
	if err != nil {
		return nil, fmt.Errorf("failed to update user group: %v", err)
	}
	return &result, nil
}

// DeleteUserGroup 删除用户组
func (c *Client) DeleteUserGroup(groupID string) error {
	url := fmt.Sprintf("%s/api/v1/users/groups/%s/", c.baseURL, groupID)
	err := c.doRequest("DELETE", url, nil, nil)
	if err != nil {
		return fmt.Errorf("failed to delete user group: %v", err)
	}
	return nil
}

// AddUserToGroup 将用户添加到用户组
func (c *Client) AddUserToGroup(userID, groupID string) error {
	url := fmt.Sprintf("%s/api/v1/users/users-groups-relations/", c.baseURL)

	relation := map[string]interface{}{
		"user":      userID,
		"usergroup": groupID,
	}

	var result map[string]interface{}
	err := c.doRequest("POST", url, relation, &result)
	if err != nil {
		return fmt.Errorf("failed to add user to group: %v", err)
	}

	logger.Infof("Successfully added user %s to group %s", userID, groupID)
	return nil
}

// RemoveUserFromGroup 从用户组中移除用户
func (c *Client) RemoveUserFromGroup(userID, groupID string) error {
	// 首先查找关系ID
	url := fmt.Sprintf("%s/api/v1/users/users-groups-relations/?user=%s&usergroup=%s",
		c.baseURL, userID, groupID)

	// JumpServer API在有过滤参数时返回直接数组，无过滤参数时返回分页格式
	// 先尝试解析为直接数组格式
	var relations []map[string]interface{}
	err := c.doRequest("GET", url, nil, &relations)
	if err != nil {
		// 如果直接数组解析失败，尝试分页格式
		var response struct {
			Count    int                      `json:"count"`
			Next     interface{}              `json:"next"`
			Previous interface{}              `json:"previous"`
			Results  []map[string]interface{} `json:"results"`
		}

		err2 := c.doRequest("GET", url, nil, &response)
		if err2 != nil {
			return fmt.Errorf("failed to find user-group relation: %v (array format: %v, paginated format: %v)", err2, err, err2)
		}
		relations = response.Results
	}

	if len(relations) == 0 {
		logger.Infof("User-group relation not found: user=%s, group=%s", userID, groupID)
		return nil // 关系不存在，认为已经移除
	}

	// 获取关系ID
	relation := relations[0]
	relationID := relation["id"]

	// 删除关系
	deleteURL := fmt.Sprintf("%s/api/v1/users/users-groups-relations/%v/", c.baseURL, relationID)
	err = c.doRequest("DELETE", deleteURL, nil, nil)
	if err != nil {
		return fmt.Errorf("failed to remove user from group: %v", err)
	}

	logger.Infof("Successfully removed user %s from group %s", userID, groupID)
	return nil
}

// UpdateUserGroupMembers 更新用户组成员
func (c *Client) UpdateUserGroupMembers(groupID string, userIDs []string) error {
	// 获取当前用户组信息
	group, err := c.GetUserGroupByID(groupID)
	if err != nil {
		return fmt.Errorf("failed to get user group: %v", err)
	}

	// 更新用户组的users字段
	group.SetUsernames(userIDs)

	url := fmt.Sprintf("%s/api/v1/users/groups/%s/", c.baseURL, groupID)
	var result UserGroup
	err = c.doRequest("PUT", url, group, &result)
	if err != nil {
		return fmt.Errorf("failed to update user group members: %v", err)
	}

	logger.Infof("Successfully updated user group %s members: %v", groupID, userIDs)
	return nil
}

// GetUserGroupMembers 获取用户组成员列表
func (c *Client) GetUserGroupMembers(groupID string) ([]User, error) {
	url := fmt.Sprintf("%s/api/v1/users/groups/%s/", c.baseURL, groupID)

	var group UserGroup
	err := c.doRequest("GET", url, nil, &group)
	if err != nil {
		return nil, fmt.Errorf("failed to get user group: %v", err)
	}

	// 创建用户对象列表，处理不同的数据格式
	var members []User
	for _, user := range group.Users {
		switch u := user.(type) {
		case string:
			// 如果是字符串，可能是用户名或用户ID
			// 由于我们需要ID，如果是用户名需要查询获取ID
			// 为了简化，我们假设字符串就是用户ID
			members = append(members, User{ID: u})
		case map[string]interface{}:
			// 如果是对象，提取id字段
			if userID, ok := u["id"].(string); ok {
				members = append(members, User{ID: userID})
			} else if username, ok := u["username"].(string); ok {
				// 如果没有id字段但有username，通过username查询用户获取ID
				if userObj, err := c.GetUser(username); err == nil && userObj != nil {
					members = append(members, User{ID: userObj.ID})
				} else {
					logger.Infof("Warning: Failed to get user ID for username %s: %v", username, err)
				}
			}
		default:
			logger.Infof("Warning: Unknown user data type in group %s: %T", groupID, user)
		}
	}

	logger.Infof("Retrieved %d members for user group %s", len(members), groupID)
	return members, nil
}

// GetUserGroupByID 根据ID获取用户组
func (c *Client) GetUserGroupByID(groupID string) (*UserGroup, error) {
	url := fmt.Sprintf("%s/api/v1/users/groups/%s/", c.baseURL, groupID)

	var group UserGroup
	err := c.doRequest("GET", url, nil, &group)
	if err != nil {
		return nil, fmt.Errorf("failed to get user group by ID: %v", err)
	}

	return &group, nil
}

// ==================== 权限管理API ====================

// CreateAssetPermission 创建资产权限
func (c *Client) CreateAssetPermission(permission *AssetPermission) (*AssetPermission, error) {
	url := fmt.Sprintf("%s/api/v1/perms/asset-permissions/", c.baseURL)

	// 创建专门的请求结构，确保格式与JumpServer页面请求完全一致
	permReq := map[string]interface{}{
		"name":         permission.Name,
		"users":        permission.Users,      // 应该是 [{"pk": "user-id"}] 格式
		"user_groups":  permission.UserGroups, // 应该是 [{"pk": "group-id"}] 格式
		"assets":       permission.Assets,     // 应该是 [{"pk": "asset-id"}] 格式
		"nodes":        permission.Nodes,      // 应该是 [{"pk": "node-id"}] 格式
		"accounts":     permission.Accounts,   // ["@ALL"] 格式
		"protocols":    permission.Protocols,  // ["all"] 格式
		"actions":      permission.Actions,    // [{"value": "connect", "label": "连接"}] 格式
		"is_active":    permission.IsActive,
		"date_start":   permission.DateStart,   // ISO 8601格式
		"date_expired": permission.DateExpired, // ISO 8601格式
		"comment":      permission.Comment,
	}

	// 确保空数组不是nil
	if permReq["users"] == nil {
		permReq["users"] = []map[string]string{}
	}
	if permReq["user_groups"] == nil {
		permReq["user_groups"] = []map[string]string{}
	}
	if permReq["assets"] == nil {
		permReq["assets"] = []map[string]string{}
	}
	if permReq["nodes"] == nil {
		permReq["nodes"] = []map[string]string{}
	}

	//// 记录详细的权限创建参数
	//logger.Infof("Creating asset permission with parameters:")
	//logger.Infof("  name: %s", permission.Name)
	//logger.Infof("  users: %+v", permission.Users)
	//logger.Infof("  user_groups: %+v", permission.UserGroups)
	//logger.Infof("  assets: %+v", permission.Assets)
	//logger.Infof("  nodes: %+v", permission.Nodes)
	//logger.Infof("  accounts: %+v", permission.Accounts)
	//logger.Infof("  protocols: %+v", permission.Protocols)
	//logger.Infof("  actions: %+v", permission.Actions)
	//logger.Infof("  is_active: %v", permission.IsActive)
	//logger.Infof("  date_start: %s", permission.DateStart)
	//logger.Infof("  date_expired: %s", permission.DateExpired)
	//logger.Infof("  comment: %s", permission.Comment)

	var result AssetPermission
	err := c.doRequest("POST", url, permReq, &result)
	if err != nil {
		logger.Errorf("Failed to create asset permission: %v", err)
		return nil, fmt.Errorf("failed to create asset permission: %v", err)
	}

	logger.Infof("Asset permission created successfully: id=%s, name=%s", result.ID, result.Name)
	return &result, nil
}

// GetAssetPermission 获取资产权限
func (c *Client) GetAssetPermission(name string) (*AssetPermission, error) {
	// 使用search参数进行查询，然后在结果中精确匹配名称
	url := fmt.Sprintf("%s/api/v1/perms/asset-permissions/?search=%s", c.baseURL, url.QueryEscape(name))
	logger.Infof("Searching for asset permission: name=%s, url=%s", name, url)

	// JumpServer权限查询API返回的是数组格式，不是APIResponse格式
	var results []AssetPermission
	err := c.doRequest("GET", url, nil, &results)
	if err != nil {
		logger.Errorf("Failed to query asset permission: %v", err)
		return nil, fmt.Errorf("failed to get asset permission: %v", err)
	}

	if len(results) == 0 {
		logger.Infof("No asset permissions found for search: %s", name)
		return nil, fmt.Errorf("asset permission not found: %s", name)
	}

	logger.Infof("Found %d asset permissions in search results", len(results))

	// 在搜索结果中查找精确匹配的权限名称
	for i, permission := range results {
		logger.Infof("Checking permission %d: name=%s", i, permission.Name)

		// 精确匹配权限名称
		if permission.Name == name {
			logger.Infof("Found exact match for permission: %s (id=%s)", name, permission.ID)

			// 获取权限详情，因为搜索接口返回的数据不完整
			detailPermission, err := c.GetAssetPermissionDetail(permission.ID)
			if err != nil {
				logger.Errorf("Failed to get permission detail for %s (id=%s): %v", name, permission.ID, err)
				return nil, fmt.Errorf("failed to get permission detail: %v", err)
			}

			logger.Infof("Successfully retrieved permission detail: %s (id=%s)", name, permission.ID)
			return detailPermission, nil
		}
	}

	logger.Infof("No exact match found for permission: %s", name)
	return nil, fmt.Errorf("asset permission not found: %s", name)
}

// GetAssetPermissionDetail 获取资产权限详情
func (c *Client) GetAssetPermissionDetail(permissionID string) (*AssetPermission, error) {
	url := fmt.Sprintf("%s/api/v1/perms/asset-permissions/%s/", c.baseURL, permissionID)
	logger.Infof("Getting asset permission detail: id=%s, url=%s", permissionID, url)

	var permission AssetPermission
	err := c.doRequest("GET", url, nil, &permission)
	if err != nil {
		logger.Errorf("Failed to get asset permission detail: %v", err)
		return nil, fmt.Errorf("failed to get asset permission detail: %v", err)
	}

	logger.Infof("Successfully retrieved permission detail: id=%s, name=%s", permission.ID, permission.Name)
	return &permission, nil
}

// UpdateAssetPermission 更新资产权限
func (c *Client) UpdateAssetPermission(permissionID string, permission *AssetPermission) (*AssetPermission, error) {
	url := fmt.Sprintf("%s/api/v1/perms/asset-permissions/%s/", c.baseURL, permissionID)

	// 创建专门的更新请求结构，只包含JumpServer需要的字段，不包含ID
	permReq := map[string]interface{}{
		"name":         permission.Name,
		"users":        permission.Users,
		"user_groups":  permission.UserGroups,
		"assets":       permission.Assets,
		"nodes":        permission.Nodes,
		"accounts":     permission.Accounts,
		"protocols":    permission.Protocols,
		"actions":      permission.Actions,
		"is_active":    permission.IsActive,
		"date_start":   permission.DateStart,
		"date_expired": permission.DateExpired,
		"comment":      permission.Comment,
	}

	// 确保assets和nodes不为null
	if permReq["assets"] == nil {
		permReq["assets"] = []map[string]string{}
	}
	if permReq["nodes"] == nil {
		permReq["nodes"] = []map[string]string{}
	}
	if permReq["users"] == nil {
		permReq["users"] = []map[string]string{}
	}
	if permReq["user_groups"] == nil {
		permReq["user_groups"] = []map[string]string{}
	}

	// 记录更新参数
	logger.Infof("Updating asset permission %s with parameters:", permissionID)
	logger.Infof("  name: %s", permission.Name)
	logger.Infof("  users: %+v", permission.Users)
	logger.Infof("  user_groups: %+v", permission.UserGroups)
	logger.Infof("  assets: %+v", permission.Assets)
	logger.Infof("  nodes: %+v", permission.Nodes)

	var result AssetPermission
	err := c.doRequest("PUT", url, permReq, &result)
	if err != nil {
		logger.Errorf("Failed to update asset permission %s: %v", permissionID, err)
		return nil, fmt.Errorf("failed to update asset permission: %v", err)
	}

	logger.Infof("Asset permission updated successfully: id=%s, name=%s", result.ID, result.Name)
	return &result, nil
}

// DeleteAssetPermission 删除资产权限
func (c *Client) DeleteAssetPermission(permissionID string) error {
	url := fmt.Sprintf("%s/api/v1/perms/asset-permissions/%s/", c.baseURL, permissionID)
	err := c.doRequest("DELETE", url, nil, nil)
	if err != nil {
		return fmt.Errorf("failed to delete asset permission: %v", err)
	}
	return nil
}

// GetUserPermissions 获取用户权限（实际返回用户可访问的资产列表）
func (c *Client) GetUserPermissions(username string) ([]Asset, error) {
	// 首先获取用户信息
	user, err := c.GetUser(username)
	if err != nil {
		return nil, err
	}

	url := fmt.Sprintf("%s/api/v1/perms/users/%s/assets/", c.baseURL, user.ID)

	logger.Infof("Getting user assets for %s (ID: %s), URL: %s", username, user.ID, url)

	// JumpServer用户资产API可能直接返回数组格式
	var assets []Asset
	if err := c.doRequest("GET", url, nil, &assets); err != nil {
		// 如果直接解析失败，尝试APIResponse格式
		var response APIResponse
		if err2 := c.doRequest("GET", url, nil, &response); err2 != nil {
			logger.Infof("Failed to get user assets for %s: %v", username, err2)
			return nil, err2
		}

		if results, ok := response.Results.([]interface{}); ok {
			for _, result := range results {
				assetData, _ := json.Marshal(result)
				var asset Asset
				if err := json.Unmarshal(assetData, &asset); err == nil {
					assets = append(assets, asset)
				}
			}
		}
	}

	logger.Infof("Successfully got %d assets for user %s", len(assets), username)
	return assets, nil
}

// GetOrCreateIDCNode 获取或创建IDC节点
func (c *Client) GetOrCreateIDCNode(idcName, parentNodeID string) (*Node, error) {
	// 首先尝试查找现有的IDC节点
	if existingNode, err := c.GetNodeByValue(idcName); err == nil && existingNode != nil {
		return existingNode, nil
	}

	// 节点不存在，创建新的IDC节点
	node := &Node{
		Key:    idcName,
		Value:  idcName,
		Parent: parentNodeID, // 使用父节点ID
	}

	createdNode, err := c.CreateNode(node)
	if err != nil {
		return nil, fmt.Errorf("failed to create IDC node '%s': %v", idcName, err)
	}

	logger.Infof("Successfully created IDC node: %+v", createdNode)
	return createdNode, nil
}
