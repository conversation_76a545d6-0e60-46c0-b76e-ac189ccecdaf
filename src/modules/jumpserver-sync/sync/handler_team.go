package sync

import (
	"fmt"

	"github.com/toolkits/pkg/logger"

	"arboris/src/modules/jumpserver-sync/events"
	"arboris/src/modules/jumpserver-sync/jumpserver"
)

// ==================== 用户组管理事件处理 ====================

// handleTeamCreate 处理用户组创建事件
func (h *Handler) handleTeamCreate(event *events.Event) error {
	teamData, err := h.parseTeamEventData(event)
	if err != nil {
		return err
	}

	logger.Infof("Creating user group: ident=%s, name=%s", teamData.Ident, teamData.Name)

	// 映射RDB团队到JumpServer用户组
	jsGroup := &jumpserver.UserGroup{
		Name:    teamData.Ident, // 使用ident作为JumpServer的name
		Comment: fmt.Sprintf("%s - %s", teamData.Name, teamData.Note),
		Users:   []interface{}{}, // 初始为空，后续通过成员事件添加
	}

	// 创建用户组
	createdGroup, err := h.jsClient.CreateUserGroup(jsGroup)
	if err != nil {
		return fmt.Errorf("failed to create user group in JumpServer: %v", err)
	}

	logger.Infof("User group created successfully: ident=%s, js_id=%s", teamData.Ident, createdGroup.ID)
	return nil
}

// handleTeamUpdate 处理用户组更新事件
func (h *Handler) handleTeamUpdate(event *events.Event) error {
	teamData, err := h.parseTeamEventData(event)
	if err != nil {
		return err
	}

	logger.Infof("Updating user group: ident=%s", teamData.Ident)

	// 获取现有用户组
	existingGroup, err := h.jsClient.GetUserGroup(teamData.Ident)
	if err != nil {
		logger.Infof("User group not found in JumpServer, creating new group: %s", teamData.Ident)
		return h.handleTeamCreate(event)
	}

	// 更新用户组信息
	existingGroup.Comment = fmt.Sprintf("%s - %s", teamData.Name, teamData.Note)

	// 执行更新
	_, err = h.jsClient.UpdateUserGroup(existingGroup.ID, existingGroup)
	if err != nil {
		return fmt.Errorf("failed to update user group in JumpServer: %v", err)
	}

	logger.Infof("User group updated successfully: ident=%s, js_id=%s", teamData.Ident, existingGroup.ID)
	return nil
}

// handleTeamDelete 处理用户组删除事件
func (h *Handler) handleTeamDelete(event *events.Event) error {
	teamData, err := h.parseTeamEventData(event)
	if err != nil {
		return err
	}

	logger.Infof("Deleting user group: ident=%s", teamData.Ident)

	// 获取现有用户组
	existingGroup, err := h.jsClient.GetUserGroup(teamData.Ident)
	if err != nil {
		logger.Infof("User group not found in JumpServer, skipping deletion: %s", teamData.Ident)
		return nil
	}

	// 删除用户组
	err = h.jsClient.DeleteUserGroup(existingGroup.ID)
	if err != nil {
		return fmt.Errorf("failed to delete user group in JumpServer: %v", err)
	}

	logger.Infof("User group deleted successfully: ident=%s, js_id=%s", teamData.Ident, existingGroup.ID)
	return nil
}

// handleTeamAddUser 处理用户组添加用户事件
func (h *Handler) handleTeamAddUser(event *events.Event) error {
	teamData, err := h.parseTeamEventData(event)
	if err != nil {
		return err
	}

	operationUserIDs := teamData.GetOperationUserIDs()
	logger.Infof("Adding users to group: ident=%s, users_count=%d", teamData.Ident, len(operationUserIDs))

	// 获取现有用户组
	existingGroup, err := h.jsClient.GetUserGroup(teamData.Ident)
	if err != nil {
		return fmt.Errorf("user group not found in JumpServer: %s", teamData.Ident)
	}

	// 获取用户名列表（需要根据用户ID查询用户名）
	usernames, err := h.getUsernamesByIDs(operationUserIDs)
	if err != nil {
		return fmt.Errorf("failed to get usernames by IDs: %v", err)
	}

	// 获取要添加用户的JumpServer ID列表
	var newUserJSIDs []string
	for _, username := range usernames {
		user, err := h.jsClient.GetUser(username)
		if err != nil {
			logger.Infof("Warning: User not found in JumpServer: %s", username)
			continue
		}
		newUserJSIDs = append(newUserJSIDs, user.ID)
	}

	logger.Infof("Found %d new users to add: %v", len(newUserJSIDs), usernames)

	// 获取现有用户组成员
	existingMembers, err := h.jsClient.GetUserGroupMembers(existingGroup.ID)
	if err != nil {
		return fmt.Errorf("failed to get existing group members: %v", err)
	}

	logger.Infof("Existing group members count: %d", len(existingMembers))

	// 合并现有成员和新成员，去重处理
	memberMap := make(map[string]bool)
	var allUserJSIDs []string

	// 添加现有成员
	for _, member := range existingMembers {
		if !memberMap[member.ID] {
			memberMap[member.ID] = true
			allUserJSIDs = append(allUserJSIDs, member.ID)
		}
	}

	// 添加新成员
	addedCount := 0
	for _, userID := range newUserJSIDs {
		if !memberMap[userID] {
			memberMap[userID] = true
			allUserJSIDs = append(allUserJSIDs, userID)
			addedCount++
		}
	}

	logger.Infof("Total members after merge: %d (added %d new members)", len(allUserJSIDs), addedCount)

	// 如果没有新成员需要添加，直接返回
	if addedCount == 0 {
		logger.Infof("No new members to add, all users are already in the group")
		return nil
	}

	// 更新用户组成员（现在是完整的成员列表）
	err = h.jsClient.UpdateUserGroupMembers(existingGroup.ID, allUserJSIDs)
	if err != nil {
		return fmt.Errorf("failed to update user group members in JumpServer: %v", err)
	}

	logger.Infof("Users added to group successfully: ident=%s, added_users=%v, total_members=%d",
		teamData.Ident, usernames, len(allUserJSIDs))
	return nil
}

// handleTeamRemoveUser 处理用户组移除用户事件
func (h *Handler) handleTeamRemoveUser(event *events.Event) error {
	teamData, err := h.parseTeamEventData(event)
	if err != nil {
		return err
	}

	operationUserIDs := teamData.GetOperationUserIDs()
	logger.Infof("Removing users from group: ident=%s, users_count=%d", teamData.Ident, len(operationUserIDs))

	// 获取现有用户组
	existingGroup, err := h.jsClient.GetUserGroup(teamData.Ident)
	if err != nil {
		return fmt.Errorf("user group not found in JumpServer: %s", teamData.Ident)
	}

	// 获取要移除的用户名列表
	usernamesToRemove, err := h.getUsernamesByIDs(operationUserIDs)
	if err != nil {
		return fmt.Errorf("failed to get usernames by IDs: %v", err)
	}

	// 逐个移除用户
	for _, username := range usernamesToRemove {
		user, err := h.jsClient.GetUser(username)
		if err != nil {
			logger.Infof("Warning: User not found in JumpServer: %s", username)
			continue
		}

		// 使用新的API移除用户
		err = h.jsClient.RemoveUserFromGroup(user.ID, existingGroup.ID)
		if err != nil {
			logger.Infof("Warning: Failed to remove user %s from group %s: %v", username, teamData.Ident, err)
			continue
		}
	}

	logger.Infof("Users removed from group successfully: ident=%s, removed_users=%v", teamData.Ident, usernamesToRemove)
	return nil
}

// getUsernamesByIDs 方法已经在database.go中实现

// removeUsersFromList 从用户列表中移除指定用户
func (h *Handler) removeUsersFromList(users []string, toRemove []string) []string {
	removeMap := make(map[string]bool)
	for _, user := range toRemove {
		removeMap[user] = true
	}

	var result []string
	for _, user := range users {
		if !removeMap[user] {
			result = append(result, user)
		}
	}

	return result
}
