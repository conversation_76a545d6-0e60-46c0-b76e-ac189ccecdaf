package sync

import (
	"arboris/src/models"
	"fmt"
	"strings"
	"time"

	"github.com/toolkits/pkg/logger"

	"arboris/src/modules/jumpserver-sync/events"
	"arboris/src/modules/jumpserver-sync/jumpserver"
)

// ==================== 权限管理事件处理 ====================

// handlePermissionGrant 处理权限授予事件
func (h *Handler) handlePermissionGrant(event *events.Event) error {
	permData, err := h.parsePermissionEventData(event)
	if err != nil {
		return err
	}

	userIDs := permData.GetPermissionUserIDs()
	teamIDs := permData.GetPermissionTeamIDs()

	logger.Infof("Granting permission: node_path=%s, users=%v, teams=%v",
		permData.NodePath, userIDs, teamIDs)

	// 获取用户ID和用户组ID（JumpServer格式）
	userPKs, err := h.getUserPKsByIDs(userIDs)
	if err != nil {
		return fmt.Errorf("failed to get user PKs: %v", err)
	}

	teamPKs, err := h.getTeamPKsByIDs(teamIDs)
	if err != nil {
		return fmt.Errorf("failed to get team PKs: %v", err)
	}

	// 确保user_groups不为null，如果为空则设置为空数组
	if teamPKs == nil {
		teamPKs = []map[string]string{}
	}

	// 确保users不为null，如果为空则设置为空数组
	if userPKs == nil {
		userPKs = []map[string]string{}
	}

	// 获取节点PK
	nodePK, _ := h.convertNodePathToJSNodePK(permData.NodePath)
	if nodePK == "" {
		return fmt.Errorf("failed to find JumpServer node for path: %s", permData.NodePath)
	}

	// 生成权限名称：RDB_节点名称路径_角色名称（使用可读的节点名称，避免同名节点冲突）
	nodeNamePath := h.getNodeNamePath(permData.NodePath)
	permissionName := h.generatePermissionName(nodeNamePath, permData.RoleName)

	// 直接给节点权限，不需要查询具体资产
	// JumpServer支持直接给节点授权，会自动包含节点下的所有资产
	// 使用本地时间，确保与JumpServer的创建时间保持一致
	// 设置生效时间为当前时间减去1分钟，确保立即生效
	now := time.Now()
	startTime := now.Add(-1 * time.Minute)
	// 设置过期时间为很远的将来，使用本地时区
	expiredTime := time.Date(2095, 7, 16, 22, 18, 16, 0, now.Location())

	permission := &jumpserver.AssetPermission{
		Name:        permissionName,
		Users:       userPKs,
		UserGroups:  teamPKs,
		Assets:      []map[string]string{}, // 空资产列表，只给节点权限
		Nodes:       []map[string]string{{"pk": nodePK}},
		Accounts:    []string{"@ALL"},
		Protocols:   []string{"all"},
		Actions:     h.getPermissionActions(permData.Permission),
		IsActive:    true,
		DateStart:   startTime.Format("2006-01-02T15:04:05.000000+08:00"),
		DateExpired: expiredTime.Format("2006-01-02T15:04:05.000000+08:00"),
		Comment:     fmt.Sprintf("Auto-synced from RDB - Role: %s", permData.RoleName),
	}

	// 检查权限是否已存在
	existingPerm, err := h.jsClient.GetAssetPermission(permissionName)
	if err != nil {
		// 权限不存在，创建新权限
		createdPerm, err := h.jsClient.CreateAssetPermission(permission)
		if err != nil {
			return fmt.Errorf("failed to create asset permission: %v", err)
		}
		logger.Infof("Asset permission created: name=%s, js_id=%s", permissionName, createdPerm.ID)
	} else {
		// 权限已存在，更新权限
		logger.Infof("Permission already exists, updating permission: %s (id=%s)", permissionName, existingPerm.ID)

		// 合并用户和用户组（避免覆盖现有的用户/用户组）
		mergedUsers := h.mergeUserPKs(existingPerm.Users, permission.Users)
		mergedUserGroups := h.mergeUserPKs(existingPerm.UserGroups, permission.UserGroups)

		// 更新权限内容，但保持现有的ID和其他字段
		existingPerm.Users = mergedUsers
		existingPerm.UserGroups = mergedUserGroups
		existingPerm.Actions = permission.Actions
		existingPerm.Comment = permission.Comment
		existingPerm.IsActive = permission.IsActive

		_, err = h.jsClient.UpdateAssetPermission(existingPerm.ID, existingPerm)
		if err != nil {
			return fmt.Errorf("failed to update asset permission: %v", err)
		}
		logger.Infof("Asset permission updated: name=%s (id=%s)", permissionName, existingPerm.ID)
	}

	return nil
}

// handlePermissionRevoke 处理权限撤销事件
func (h *Handler) handlePermissionRevoke(event *events.Event) error {
	permData, err := h.parsePermissionEventData(event)
	if err != nil {
		return err
	}

	userIDs := permData.GetPermissionUserIDs()
	teamIDs := permData.GetPermissionTeamIDs()

	logger.Infof("Revoking permission: node_path=%s, users=%v, teams=%v",
		permData.NodePath, userIDs, teamIDs)

	// 获取节点PK（与授权函数保持一致）
	nodePK, _ := h.convertNodePathToJSNodePK(permData.NodePath)
	if nodePK == "" {
		return fmt.Errorf("failed to find JumpServer node for path: %s", permData.NodePath)
	}

	// 生成权限名称：RDB_节点名称路径_角色名称（与授权函数保持一致，避免同名节点冲突）
	nodeNamePath := h.getNodeNamePath(permData.NodePath)
	permissionName := h.generatePermissionName(nodeNamePath, permData.RoleName)

	// 获取现有权限
	existingPerm, err := h.jsClient.GetAssetPermission(permissionName)
	if err != nil {
		logger.Infof("Asset permission not found, skipping revoke: %s", permissionName)
		return nil
	}

	// 记录权限撤销前的状态
	logger.Infof("Permission before revoke: name=%s, users_count=%d, user_groups_count=%d",
		permissionName, len(existingPerm.Users), len(existingPerm.UserGroups))
	logger.Infof("Existing users: %+v", existingPerm.Users)
	logger.Infof("Existing user groups: %+v", existingPerm.UserGroups)

	// 获取要撤销的用户PK和用户组PK
	userPKsToRevoke, err := h.getUserPKsByIDs(userIDs)
	if err != nil {
		return fmt.Errorf("failed to get user PKs to revoke: %v", err)
	}

	teamPKsToRevoke, err := h.getTeamPKsByIDs(teamIDs)
	if err != nil {
		return fmt.Errorf("failed to get team PKs to revoke: %v", err)
	}

	// 记录要撤销的用户和用户组
	logger.Infof("Users to revoke: %+v", userPKsToRevoke)
	logger.Infof("Teams to revoke: %+v", teamPKsToRevoke)

	// 从现有权限中移除指定的用户和用户组
	originalUsersCount := len(existingPerm.Users)
	originalUserGroupsCount := len(existingPerm.UserGroups)

	existingPerm.Users = h.removeUserPKsFromList(existingPerm.Users, userPKsToRevoke)
	existingPerm.UserGroups = h.removeUserPKsFromList(existingPerm.UserGroups, teamPKsToRevoke)

	// 记录移除后的状态
	logger.Infof("After removal: users_count=%d (was %d), user_groups_count=%d (was %d)",
		len(existingPerm.Users), originalUsersCount, len(existingPerm.UserGroups), originalUserGroupsCount)
	logger.Infof("Remaining users: %+v", existingPerm.Users)
	logger.Infof("Remaining user groups: %+v", existingPerm.UserGroups)

	// 如果没有用户和用户组了，删除权限
	if len(existingPerm.Users) == 0 && len(existingPerm.UserGroups) == 0 {
		logger.Infof("No users or user groups remaining, deleting permission: %s", permissionName)
		err = h.jsClient.DeleteAssetPermission(existingPerm.ID)
		if err != nil {
			return fmt.Errorf("failed to delete asset permission: %v", err)
		}
		logger.Infof("Asset permission deleted: name=%s, js_id=%s", permissionName, existingPerm.ID)
	} else {
		// 更新权限
		logger.Infof("Users or user groups still remaining, updating permission: %s", permissionName)
		_, err = h.jsClient.UpdateAssetPermission(existingPerm.ID, existingPerm)
		if err != nil {
			return fmt.Errorf("failed to update asset permission: %v", err)
		}
		logger.Infof("Asset permission updated: name=%s, js_id=%s", permissionName, existingPerm.ID)
	}

	return nil
}

// ==================== 权限处理辅助方法 ====================

// generatePermissionName 生成权限名称
func (h *Handler) generatePermissionName(nodePath, roleName string) string {
	// 处理 RDB 路径格式（如：inner.code.master）
	// 将点分隔转换为下划线分隔，确保权限名称的唯一性
	cleanPath := strings.ReplaceAll(nodePath, ".", "_")
	cleanPath = strings.ReplaceAll(cleanPath, "/", "_")
	cleanPath = strings.ReplaceAll(cleanPath, " ", "_")
	cleanPath = strings.Trim(cleanPath, "_")

	if cleanPath == "" {
		cleanPath = "root"
	}

	// 清理角色名称中的特殊字符
	cleanRole := strings.ReplaceAll(roleName, " ", "_")
	cleanRole = strings.ReplaceAll(cleanRole, "/", "_")

	return fmt.Sprintf("RDB_%s_%s", cleanPath, cleanRole)
}

// convertNodePathToJSNode 将RDB节点路径转换为JumpServer节点格式
func (h *Handler) convertNodePathToJSNode(nodePath string) string {
	// RDB的node path格式: inner.code.master
	// 直接使用相同的路径，无需转换
	return nodePath
}

// getPermissionActions 根据权限类型获取JumpServer的actions
func (h *Handler) getPermissionActions(permission string) []map[string]interface{} {
	// 根据RDB的权限类型映射到JumpServer的actions
	var jsActions []map[string]interface{}

	switch permission {
	case "rw", "read_write":
		// 完整的读写权限，与页面创建的权限保持一致
		jsActions = []map[string]interface{}{
			{"value": "connect", "label": "连接 (所有协议)"},
			{"value": "upload", "label": "上传 (RDP, SFTP)"},
			{"value": "download", "label": "下载 (RDP, SFTP)"},
			{"value": "copy", "label": "复制 (RDP, VNC)"},
			{"value": "paste", "label": "粘贴 (RDP, VNC)"},
			{"value": "delete", "label": "删除 (SFTP)"},
			{"value": "share", "label": "分享 (SSH)"},
		}
	case "ro", "read_only":
		jsActions = []map[string]interface{}{
			{"value": "connect", "label": "连接 (所有协议)"},
		}
	default:
		// 默认给完整权限
		jsActions = []map[string]interface{}{
			{"value": "connect", "label": "连接 (所有协议)"},
			{"value": "upload", "label": "上传 (RDP, SFTP)"},
			{"value": "download", "label": "下载 (RDP, SFTP)"},
			{"value": "copy", "label": "复制 (RDP, VNC)"},
			{"value": "paste", "label": "粘贴 (RDP, VNC)"},
			{"value": "delete", "label": "删除 (SFTP)"},
			{"value": "share", "label": "分享 (SSH)"},
		}
	}

	return jsActions
}

// getUserPKsByIDs 根据用户ID获取JumpServer用户PK列表
func (h *Handler) getUserPKsByIDs(userIDs []int64) ([]map[string]string, error) {
	var userPKs []map[string]string

	for _, userID := range userIDs {
		// 根据RDB用户ID查询用户名
		usernames, err := h.getUsernamesByIDs([]int64{userID})
		if err != nil || len(usernames) == 0 {
			logger.Errorf("Failed to get username for user ID %d: %v", userID, err)
			continue
		}
		username := usernames[0]

		// 根据用户名查询JumpServer用户
		user, err := h.jsClient.GetUser(username)
		if err != nil {
			logger.Errorf("Failed to get JumpServer user for username %s: %v", username, err)
			continue
		}

		if user != nil {
			userPKs = append(userPKs, map[string]string{"pk": user.ID})
		}
	}

	return userPKs, nil
}

// getTeamPKsByIDs 根据团队ID获取JumpServer用户组PK列表
func (h *Handler) getTeamPKsByIDs(teamIDs []int64) ([]map[string]string, error) {
	var teamPKs []map[string]string

	for _, teamID := range teamIDs {
		// 根据RDB团队ID查询团队名称
		teamNames, err := h.getTeamNamesByIDs([]int64{teamID})
		if err != nil || len(teamNames) == 0 {
			logger.Errorf("Failed to get team name for team ID %d: %v", teamID, err)
			continue
		}
		teamName := teamNames[0]

		// 根据团队名称查询JumpServer用户组
		group, err := h.jsClient.GetUserGroup(teamName)
		if err != nil {
			logger.Errorf("Failed to get JumpServer user group for team name %s: %v", teamName, err)
			continue
		}

		if group != nil {
			teamPKs = append(teamPKs, map[string]string{"pk": group.ID})
		}
	}

	return teamPKs, nil
}

// convertNodePathToJSNodePK 将RDB节点路径转换为JumpServer节点PK和节点名称
func (h *Handler) convertNodePathToJSNodePK(nodePath string) (string, string) {
	logger.Infof("Converting RDB node path to JumpServer node PK: path=%s", nodePath)

	// 从节点路径中提取节点标识（最后一段）
	parts := strings.Split(nodePath, ".")
	if len(parts) == 0 {
		logger.Errorf("Invalid node path: empty path")
		return "", ""
	}

	nodeIdent := parts[len(parts)-1]
	logger.Infof("Target node ident: %s", nodeIdent)

	// 首先通过RDB查询节点的实际名称
	nodeName, err := h.getNodeNameByIdent(nodeIdent)
	if err != nil {
		logger.Errorf("Failed to get node name for ident %s: %v", nodeIdent, err)
		return "", ""
	}

	if nodeName == "" {
		logger.Errorf("Node name not found for ident: %s", nodeIdent)
		return "", ""
	}

	logger.Infof("Target node name: %s", nodeName)

	// 递归查找父节点ID
	var parentID string
	if len(parts) > 1 {
		parentPath := strings.Join(parts[:len(parts)-1], ".")
		logger.Infof("Finding parent node for path: %s", parentPath)

		parentPK, _ := h.convertNodePathToJSNodePKRecursive(parentPath)
		if parentPK != "" {
			parentID = parentPK
			logger.Infof("Parent node found: parent_id=%s", parentID)
		} else {
			logger.Errorf("Failed to find parent node for path: %s", parentPath)
		}
	} else {
		logger.Infof("Target node is root level")
	}

	// 通过父节点ID和节点名称查找JumpServer中的对应节点
	logger.Infof("Searching JumpServer node: parent_id=%s, node_name=%s", parentID, nodeName)
	node, err := h.jsClient.GetNodeByParentAndValue(parentID, nodeName)
	if err != nil {
		logger.Errorf("Failed to find JumpServer node by parent and value: parent_id=%s, value=%s, error=%v", parentID, nodeName, err)
		return "", ""
	}

	if node == nil {
		logger.Errorf("JumpServer node not found: parent_id=%s, value=%s", parentID, nodeName)

		// 尝试查找可能的旧名称节点或通过其他方式匹配
		logger.Infof("Attempting to find node with alternative methods...")
		if parentID != "" {
			children, childErr := h.jsClient.GetNodeChildren(parentID)
			if childErr == nil {
				logger.Infof("Parent node has %d children, checking for possible matches:", len(children))
				for i, child := range children {
					logger.Infof("  Child %d: ID=%s, Value=%s, Key=%s", i+1, child.ID, child.Value, child.Key)
				}

				// 方法1: 检查是否有节点的 ident 匹配
				for _, child := range children {
					if childIdent, exists := child.Meta["rdb_ident"]; exists && childIdent == nodeIdent {
						logger.Infof("Found node by RDB ident match: ID=%s, Value=%s, ident=%s",
							child.ID, child.Value, childIdent)
						return child.ID, child.Value
					}
				}

				// 方法2: 检查是否有节点的 rdb_path 匹配
				targetPath := nodePath
				for _, child := range children {
					if childPath, exists := child.Meta["rdb_path"]; exists && childPath == targetPath {
						logger.Infof("Found node by RDB path match: ID=%s, Value=%s, path=%s",
							child.ID, child.Value, childPath)
						return child.ID, child.Value
					}
				}

				// 方法3: 如果只有一个子节点，可能就是我们要找的（名称可能已更新）
				if len(children) == 1 {
					child := children[0]
					logger.Infof("Only one child node found, assuming it's the target: ID=%s, Value=%s",
						child.ID, child.Value)
					return child.ID, child.Value
				}
			}
		}

		return "", ""
	}

	logger.Infof("Found JumpServer node: ident=%s, name=%s, pk=%s", nodeIdent, nodeName, node.ID)
	return node.ID, nodeName
}

// convertNodePathToJSNodePKRecursive 递归查找节点PK（辅助方法）
func (h *Handler) convertNodePathToJSNodePKRecursive(nodePath string) (string, string) {
	parts := strings.Split(nodePath, ".")
	if len(parts) == 0 {
		return "", ""
	}

	nodeIdent := parts[len(parts)-1]

	// 获取当前节点名称
	nodeName, err := h.getNodeNameByIdent(nodeIdent)
	if err != nil || nodeName == "" {
		return "", ""
	}

	// 如果是根节点，直接查找
	if len(parts) == 1 {
		node, err := h.jsClient.GetNodeByParentAndValue("", nodeName)
		if err != nil || node == nil {
			return "", ""
		}
		return node.ID, nodeName
	}

	// 递归查找父节点
	parentPath := strings.Join(parts[:len(parts)-1], ".")
	parentPK, _ := h.convertNodePathToJSNodePKRecursive(parentPath)
	if parentPK == "" {
		return "", ""
	}

	// 在父节点下查找当前节点
	node, err := h.jsClient.GetNodeByParentAndValue(parentPK, nodeName)
	if err != nil || node == nil {
		return "", ""
	}

	return node.ID, nodeName
}

// ConvertNodePathToJSNodePK 公开的节点路径转换方法（用于测试）
func (h *Handler) ConvertNodePathToJSNodePK(nodePath string) (string, string) {
	return h.convertNodePathToJSNodePK(nodePath)
}

// getNodeNameByPath 根据节点路径获取节点名称
func (h *Handler) getNodeNameByPath(nodePath string) (string, error) {
	nodeInfo, err := h.getNodeByPath(nodePath)
	if err != nil {
		return "", err
	}
	return nodeInfo.Name, nil
}

// getNodeNamePath 根据节点路径获取完整的节点名称路径
// 例如：inner.code.master -> 内网.代码.主分支
func (h *Handler) getNodeNamePath(nodePath string) string {
	if nodePath == "" {
		return "根节点"
	}

	// 分解路径
	parts := strings.Split(nodePath, ".")
	nameparts := make([]string, 0, len(parts))

	// 逐级获取节点名称
	currentPath := ""
	for i, part := range parts {
		if i == 0 {
			currentPath = part
		} else {
			currentPath = currentPath + "." + part
		}

		// 获取当前节点的名称
		if nodeName, err := h.getNodeNameByPath(currentPath); err == nil && nodeName != "" {
			nameparts = append(nameparts, nodeName)
		} else {
			// 如果获取失败，使用原始标识作为备选
			nameparts = append(nameparts, part)
		}
	}

	return strings.Join(nameparts, ".")
}

// getNodeNameByIdent 根据节点标识获取节点名称
func (h *Handler) getNodeNameByIdent(nodeIdent string) (string, error) {
	// 检查RDB数据库是否可用
	if models.DB["rdb"] == nil {
		return "", fmt.Errorf("RDB database not connected")
	}

	// 查询节点信息
	var node models.Node
	has, err := models.DB["rdb"].Where("ident = ?", nodeIdent).Get(&node)
	if err != nil {
		return "", fmt.Errorf("failed to query node by ident %s: %v", nodeIdent, err)
	}

	if !has {
		return "", fmt.Errorf("node not found for ident: %s", nodeIdent)
	}

	logger.Infof("Found RDB node: ident=%s, name=%s, id=%d", nodeIdent, node.Name, node.Id)
	return node.Name, nil
}

// removeUserPKsFromList 从用户PK列表中移除指定的用户PK
func (h *Handler) removeUserPKsFromList(originalList []map[string]string, toRemove []map[string]string) []map[string]string {
	if len(toRemove) == 0 {
		return originalList
	}

	// 创建要移除的PK集合
	removeSet := make(map[string]bool)
	for _, item := range toRemove {
		if pk, exists := item["pk"]; exists {
			removeSet[pk] = true
		}
	}

	// 过滤原列表，移除指定的PK
	// 注意：权限详情接口返回的用户对象使用"id"字段，而不是"pk"字段
	var result []map[string]string
	for _, item := range originalList {
		var userID string
		var found bool

		// 尝试从"pk"字段获取ID（用于创建/更新权限时的格式）
		if pk, exists := item["pk"]; exists {
			userID = pk
			found = true
		} else if id, exists := item["id"]; exists {
			// 尝试从"id"字段获取ID（权限详情接口返回的格式）
			userID = id
			found = true
		}

		if found && !removeSet[userID] {
			result = append(result, item)
		}
	}

	return result
}

// mergeUserPKs 合并用户PK列表，避免重复
func (h *Handler) mergeUserPKs(existing []map[string]string, new []map[string]string) []map[string]string {
	// 创建现有PK的集合
	existingPKs := make(map[string]bool)
	for _, item := range existing {
		if pk, exists := item["pk"]; exists {
			existingPKs[pk] = true
		}
	}

	// 合并结果，从现有列表开始
	result := make([]map[string]string, len(existing))
	copy(result, existing)

	// 添加新的PK，但跳过重复的
	for _, item := range new {
		if pk, exists := item["pk"]; exists {
			if !existingPKs[pk] {
				result = append(result, item)
				existingPKs[pk] = true
			}
		}
	}

	return result
}
