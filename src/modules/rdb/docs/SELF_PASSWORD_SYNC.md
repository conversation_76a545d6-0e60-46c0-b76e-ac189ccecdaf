# 个人密码修改同步功能文档

## 🎯 功能概述

实现了个人用户修改密码时自动同步到 JumpServer 的功能，复用了现有的密码变更事件机制。

## 🔍 需求分析

### 业务场景

- **用户自助修改密码**：用户通过个人中心修改自己的密码
- **密码同步需求**：修改后的密码需要同步到 JumpServer
- **复用现有机制**：利用已有的密码变更事件处理逻辑

### 现有机制

在 `router_user.go` 中已经存在管理员修改用户密码的同步机制：

```go
// 异步发布密码变更事件
go func() {
    passwordData := &jsEvents.PasswordChangeEventData{
        UserID:      user.Id,
        Username:    user.Username,
        NewPassword: f.Password,
    }
    events.PublishPasswordChangeEvent(passwordData)
}()
```

## ✅ 实现方案

### 1. 修改个人密码接口

在 `src/modules/rdb/http/router_self.go` 的 `selfPasswordPut` 函数中添加事件发布逻辑：

**修改前**：
```go
func selfPasswordPut(c *gin.Context) {
    var f selfPasswordForm
    bind(c, &f)

    err := func() error {
        user, err := models.UserMustGet("username=?", f.Username)
        if err != nil {
            return err
        }
        oldpass, err := models.CryptoPass(f.OldPass)
        if err != nil {
            return err
        }
        if user.Password != oldpass {
            return _e("Incorrect old password")
        }

        if err := auth.ChangePassword(user, f.NewPass); err != nil {
            return err
        }

        return nil
    }()

    renderMessage(c, err)
}
```

**修改后**：
```go
func selfPasswordPut(c *gin.Context) {
    var f selfPasswordForm
    bind(c, &f)

    var updatedUser *models.User
    err := func() error {
        user, err := models.UserMustGet("username=?", f.Username)
        if err != nil {
            return err
        }
        oldpass, err := models.CryptoPass(f.OldPass)
        if err != nil {
            return err
        }
        if user.Password != oldpass {
            return _e("Incorrect old password")
        }

        if err := auth.ChangePassword(user, f.NewPass); err != nil {
            return err
        }

        // 保存更新后的用户信息，用于事件发布
        updatedUser = user

        return nil
    }()

    // 如果密码修改成功，发布密码变更事件同步到 JumpServer
    if err == nil && updatedUser != nil {
        logger.Infof("User password changed successfully, publishing sync event: username=%s", updatedUser.Username)
        
        // 异步发布密码变更事件，与 router_user.go 保持一致
        go func() {
            passwordData := &jsEvents.PasswordChangeEventData{
                UserID:      updatedUser.Id,
                Username:    updatedUser.Username,
                NewPassword: f.NewPass, // 使用明文密码
            }
            events.PublishPasswordChangeEvent(passwordData)
        }()
    }

    renderMessage(c, err)
}
```

### 2. 添加必要的导入

```go
import (
    "arboris/src/models"
    jsEvents "arboris/src/modules/jumpserver-sync/events"
    "arboris/src/modules/rdb/auth"
    "arboris/src/modules/rdb/config"
    "arboris/src/modules/rdb/events"
    "github.com/toolkits/pkg/logger"

    "github.com/gin-gonic/gin"
)
```

## 🔄 工作流程

### 密码修改流程

1. **用户请求**：用户通过 API 提交密码修改请求
   ```json
   {
       "username": "testuser",
       "oldpass": "oldpassword",
       "newpass": "newpassword"
   }
   ```

2. **验证处理**：
   - 验证用户存在
   - 验证旧密码正确
   - 更新本地数据库密码

3. **事件发布**：
   - 密码修改成功后，异步发布 `PasswordChangeEvent`
   - 事件包含用户ID、用户名和新密码

4. **JumpServer 同步**：
   - jumpserver-sync 模块接收事件
   - 更新 JumpServer 中对应用户的密码

### 事件数据结构

```go
type PasswordChangeEventData struct {
    UserID      int64  `json:"user_id"`
    Username    string `json:"username"`
    NewPassword string `json:"new_password"`
}
```

## 📊 关键特性

### 1. 复用现有机制

- ✅ **一致性**：与管理员修改密码使用相同的事件类型
- ✅ **可靠性**：复用已验证的同步逻辑
- ✅ **维护性**：减少重复代码

### 2. 异步处理

- ✅ **性能**：不阻塞用户请求响应
- ✅ **容错**：同步失败不影响本地密码修改
- ✅ **可扩展**：支持后续添加其他同步目标

### 3. 安全考虑

- ✅ **密码验证**：严格验证旧密码
- ✅ **权限控制**：只能修改自己的密码
- ✅ **日志记录**：记录密码修改操作

## 🧪 测试验证

### API 测试

```bash
# 个人密码修改接口
curl -X PUT http://localhost:8080/api/rdb/self/password \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-jwt-token" \
  -d '{
    "username": "testuser",
    "oldpass": "oldpassword",
    "newpass": "newpassword"
  }'
```

### 预期结果

1. **成功响应**：
   ```json
   {
       "err": "",
       "msg": "Password changed successfully"
   }
   ```

2. **日志输出**：
   ```
   INFO User password changed successfully, publishing sync event: username=testuser
   ```

3. **JumpServer 同步**：用户在 JumpServer 中的密码被更新

## 📁 修改的文件

1. **src/modules/rdb/http/router_self.go**
   - 修改 `selfPasswordPut` 函数
   - 添加事件发布逻辑
   - 添加必要的导入

## 🔗 相关组件

### 事件系统

- **事件发布**：`src/modules/rdb/events/producer.go`
- **事件类型**：`src/modules/jumpserver-sync/events/types.go`
- **事件处理**：`src/modules/jumpserver-sync/sync/handler_user.go`

### 同步机制

- **密码同步**：jumpserver-sync 模块处理 `PasswordChangeEvent`
- **用户管理**：JumpServer 用户密码更新
- **错误处理**：同步失败的重试和日志记录

## ⚠️ 注意事项

1. **事件依赖**：需要确保 jumpserver-sync 服务正在运行
2. **网络连接**：需要 RDB 与 JumpServer 之间的网络连通
3. **权限配置**：确保同步服务有足够的 JumpServer 权限
4. **密码策略**：新密码需要符合 JumpServer 的密码策略

## 🚀 部署建议

1. **测试环境验证**：先在测试环境验证功能正常
2. **监控日志**：关注密码修改和同步的日志输出
3. **错误告警**：设置同步失败的告警机制
4. **用户通知**：考虑在同步失败时通知用户

## 🎯 总结

通过复用现有的密码变更事件机制，成功实现了个人密码修改的 JumpServer 同步功能：

- **简单高效**：复用现有代码，减少开发工作量
- **一致可靠**：与管理员修改密码使用相同的同步逻辑
- **用户友好**：异步处理，不影响用户体验

现在用户可以通过个人中心修改密码，并自动同步到 JumpServer 了！🎉
